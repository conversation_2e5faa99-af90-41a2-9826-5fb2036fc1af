# دليل استكشاف أخطاء ميزة إلغاء فواتير الشراء

## المشكلة: لم يظهر زر الإلغاء

### الأسباب المحتملة والحلول:

#### 1. مشكلة الصلاحيات
**السبب:** المستخدم الحالي لا يملك صلاحيات لإلغاء الفواتير

**التحقق:**
- افتح أدوات المطور (F12) → Console
- اكتب: `localStorage.getItem('currentUserRole')`
- تحقق من النتيجة:
  - `"admin"` = مدير (لا يمكنه الشراء أو الإلغاء)
  - `"viewer"` = مشاهد (لا يمكنه الشراء أو الإلغاء)
  - `"employee"` أو `null` = موظف (يمكنه الشراء والإلغاء)

**الحل:**
- سجل الدخول كموظف (employee)
- أو تأكد من أن دورك في النظام صحيح

#### 2. مشكلة تحديث قاعدة البيانات
**السبب:** الحقول الجديدة لم تُضف لقاعدة البيانات

**التحقق:**
1. افتح قاعدة البيانات باستخدام SQLite Browser
2. تحقق من جدول `transactions`
3. ابحث عن الحقول التالية:
   - `status`
   - `cancelled_at`
   - `cancelled_by`
   - `cancellation_reason`

**الحل:**
```sql
-- تشغيل هذه الأوامر في SQLite Browser
ALTER TABLE transactions ADD COLUMN status TEXT DEFAULT 'active';
ALTER TABLE transactions ADD COLUMN cancelled_at TEXT;
ALTER TABLE transactions ADD COLUMN cancelled_by INTEGER;
ALTER TABLE transactions ADD COLUMN cancellation_reason TEXT;

-- تحديث المعاملات الموجودة
UPDATE transactions SET status = 'active' WHERE status IS NULL;

-- إنشاء جدول سجل التدقيق
CREATE TABLE IF NOT EXISTS transaction_audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_id INTEGER NOT NULL,
    action_type TEXT NOT NULL,
    old_status TEXT,
    new_status TEXT,
    reason TEXT,
    performed_by INTEGER NOT NULL,
    performed_at TEXT NOT NULL,
    additional_data TEXT
);
```

#### 3. مشكلة إعادة تشغيل التطبيق
**السبب:** التطبيق لم يُعاد تشغيله بعد إضافة الكود الجديد

**الحل:**
1. أغلق التطبيق تماماً
2. أعد تشغيله
3. تأكد من تحميل الملفات الجديدة

#### 4. مشكلة في البيانات
**السبب:** المعاملات لا تحتوي على حقل الحالة

**التحقق:**
```sql
-- في SQLite Browser
SELECT status, COUNT(*) FROM transactions GROUP BY status;
```

**الحل:**
```sql
-- تحديث جميع المعاملات لتكون نشطة
UPDATE transactions SET status = 'active' WHERE status IS NULL OR status = '';
```

#### 5. مشكلة في الكود
**السبب:** خطأ في JavaScript

**التحقق:**
1. افتح أدوات المطور (F12)
2. انتقل إلى تبويب Console
3. ابحث عن أخطاء باللون الأحمر

**الأخطاء الشائعة:**
- `Cannot read property 'status' of undefined`
- `handleCancelPurchase is not defined`
- `CancellationConfirmDialog is not defined`

**الحل:**
- تأكد من أن جميع الملفات محفوظة
- أعد تشغيل التطبيق
- تحقق من أن الـ imports صحيحة

## خطوات التشخيص السريع:

### الخطوة 1: تحقق من الصلاحيات
```javascript
// في Console المتصفح
console.log('User Role:', localStorage.getItem('currentUserRole'));
console.log('Can Make Purchases:', !['admin', 'viewer'].includes(localStorage.getItem('currentUserRole')));
```

### الخطوة 2: تحقق من البيانات
```javascript
// في Console المتصفح
window.api.invoke('get-transactions').then(transactions => {
  const purchases = transactions.filter(t => t.transaction_type === 'purchase');
  console.log('Purchase Transactions:', purchases);
  console.log('First Purchase Status:', purchases[0]?.status);
});
```

### الخطوة 3: تحقق من الوظائف
```javascript
// في Console المتصفح
console.log('handleCancelPurchase exists:', typeof handleCancelPurchase !== 'undefined');
console.log('CancellationConfirmDialog imported:', typeof CancellationConfirmDialog !== 'undefined');
```

## الحلول السريعة:

### إذا كان المستخدم مدير أو مشاهد:
1. سجل الخروج
2. سجل الدخول كموظف
3. أو أنشئ حساب موظف جديد

### إذا كانت قاعدة البيانات غير محدثة:
1. افتح SQLite Browser
2. افتح ملف قاعدة البيانات
3. نفذ محتويات ملف `manual-db-migration.sql`
4. احفظ التغييرات
5. أعد تشغيل التطبيق

### إذا كان هناك خطأ في الكود:
1. تحقق من وحدة التحكم للأخطاء
2. أعد تشغيل التطبيق
3. امسح cache المتصفح (Ctrl+Shift+R)

## اختبار سريع:

بعد تطبيق الحلول، اختبر الميزة:

1. **انتقل لصفحة المشتريات**
2. **تحقق من وجود عمود "الحالة"** - يجب أن يظهر "نشطة" للفواتير العادية
3. **تحقق من وجود عمود "الإجراءات"** - يجب أن يحتوي على زر "إلغاء"
4. **اضغط على زر "إلغاء"** - يجب أن يظهر حوار التأكيد
5. **أدخل سبب الإلغاء واضغط تأكيد** - يجب أن تتغير الحالة إلى "ملغاة"

## إذا استمرت المشكلة:

1. **تحقق من ملفات المشروع:**
   - `src/components/CancellationConfirmDialog.js` موجود؟
   - `purchase-invoice-cancellation-manager.js` موجود؟
   - `src/pages/Purchases.js` محدث؟

2. **تحقق من قاعدة البيانات:**
   - الحقول الجديدة موجودة؟
   - جدول `transaction_audit_log` موجود؟

3. **تحقق من الشبكة:**
   - افتح Network tab في أدوات المطور
   - ابحث عن طلبات فاشلة (باللون الأحمر)

4. **تحقق من الملفات:**
   - تأكد من حفظ جميع التغييرات
   - تأكد من أن الملفات في المكان الصحيح

## معلومات إضافية للمطورين:

### ملفات تم تعديلها:
- `src/pages/Purchases.js` - الواجهة الرئيسية
- `src/components/CancellationConfirmDialog.js` - حوار التأكيد
- `purchase-invoice-cancellation-manager.js` - منطق الإلغاء
- `main.js` - تهيئة النظام
- `ipc-handlers.js` - معالجات الاتصال
- `database-schema.sql` - هيكل قاعدة البيانات

### IPC Handlers المضافة:
- `validate-purchase-cancellation`
- `cancel-purchase-invoice`
- `get-transaction-audit-log`

### Database Changes:
- حقول جديدة في جدول `transactions`
- جدول جديد `transaction_audit_log`
- فهارس جديدة للأداء
