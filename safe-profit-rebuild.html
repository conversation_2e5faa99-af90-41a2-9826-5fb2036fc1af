<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة بناء نظام الأرباح - آمن للإنتاج</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #856404;
        }
        .danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #155724;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #0c5460;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .danger-button {
            background: #dc3545;
        }
        .danger-button:hover {
            background: #c82333;
        }
        .success-button {
            background: #28a745;
        }
        .success-button:hover {
            background: #218838;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إعادة بناء نظام الأرباح - آمن للإنتاج</h1>
        
        <div class="danger">
            <h3>⚠️ تحذير مهم للبيئة الإنتاجية</h3>
            <ul>
                <li>هذا الإجراء سيعيد حساب جميع الأرباح في النظام</li>
                <li>سيتم إنشاء نسخة احتياطية تلقائياً قبل البدء</li>
                <li>العملية قد تستغرق عدة دقائق حسب حجم البيانات</li>
                <li>يُنصح بتشغيلها خارج ساعات العمل</li>
                <li>تأكد من عدم وجود مستخدمين آخرين في النظام</li>
            </ul>
        </div>

        <div class="info">
            <h3>📋 ما سيتم إصلاحه:</h3>
            <ul>
                <li>إعادة حساب أرباح جميع معاملات البيع باستخدام آخر سعر شراء</li>
                <li>إعادة بناء إجماليات الخزينة من المعاملات الفعلية</li>
                <li>توحيد جميع قيم الأرباح في النظام</li>
                <li>إصلاح التضارب بين avg_price و last_purchase_price</li>
                <li>التأكد من صحة حسابات مصاريف النقل</li>
            </ul>
        </div>

        <div class="step">
            <h3>📊 الخطوة 1: تشخيص المشكلة</h3>
            <button onclick="runDiagnosis()">تشغيل التشخيص</button>
            <div id="diagnosisResults"></div>
        </div>

        <div class="step">
            <h3>🔧 الخطوة 2: إعادة البناء الآمنة</h3>
            <div class="warning">
                <p><strong>تأكد من:</strong></p>
                <ul>
                    <li>عدم وجود مستخدمين آخرين في النظام</li>
                    <li>وجود مساحة كافية لإنشاء نسخة احتياطية</li>
                    <li>استقرار الاتصال بقاعدة البيانات</li>
                </ul>
            </div>
            <button class="danger-button" onclick="startRebuild()" id="rebuildButton">
                🚀 بدء إعادة البناء الآمنة
            </button>
            <div class="progress" id="progressContainer" style="display: none;">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div id="rebuildResults"></div>
        </div>

        <div class="step">
            <h3>📋 سجل العمليات</h3>
            <div class="log" id="operationLog">جاهز لبدء العمليات...</div>
        </div>
    </div>

    <script>
        let isRebuilding = false;

        function log(message) {
            const logElement = document.getElementById('operationLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateProgress(percentage) {
            const progressBar = document.getElementById('progressBar');
            const progressContainer = document.getElementById('progressContainer');
            progressContainer.style.display = 'block';
            progressBar.style.width = percentage + '%';
        }

        async function runDiagnosis() {
            log('🔍 بدء التشخيص...');
            const resultsDiv = document.getElementById('diagnosisResults');
            
            try {
                // فحص بيانات الخزينة
                const cashbox = await window.api.cashbox.get();
                
                if (!cashbox || !cashbox.exists) {
                    resultsDiv.innerHTML = '<div class="danger">❌ لا توجد خزينة في النظام</div>';
                    return;
                }

                log('📊 تم الحصول على بيانات الخزينة');

                // فحص المعاملات
                const salesQuery = "SELECT COUNT(*) as count, SUM(total_price) as total, SUM(COALESCE(profit, 0)) as profit FROM transactions WHERE transaction_type = 'sale'";
                const purchasesQuery = "SELECT COUNT(*) as count, SUM(total_price) as total, SUM(COALESCE(transport_cost, 0)) as transport FROM transactions WHERE transaction_type = 'purchase'";
                const returnsQuery = "SELECT COUNT(*) as count, SUM(total_price) as total FROM transactions WHERE transaction_type = 'return'";

                const [salesResult, purchasesResult, returnsResult] = await Promise.all([
                    window.api.invoke('execute-direct-query', { query: salesQuery }),
                    window.api.invoke('execute-direct-query', { query: purchasesQuery }),
                    window.api.invoke('execute-direct-query', { query: returnsQuery })
                ]);

                log('📈 تم فحص المعاملات');

                if (salesResult.success && purchasesResult.success && returnsResult.success) {
                    const sales = salesResult.data[0];
                    const purchases = purchasesResult.data[0];
                    const returns = returnsResult.data[0];

                    const calculatedFromCashbox = cashbox.sales_total - cashbox.purchases_total - cashbox.transport_total;
                    const calculatedFromTransactions = sales.total - purchases.total - purchases.transport;

                    let html = `
                        <div class="info">
                            <h4>📊 نتائج التشخيص:</h4>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr style="background: #f8f9fa;">
                                    <th style="border: 1px solid #dee2e6; padding: 8px;">البيان</th>
                                    <th style="border: 1px solid #dee2e6; padding: 8px;">الخزينة</th>
                                    <th style="border: 1px solid #dee2e6; padding: 8px;">المعاملات</th>
                                    <th style="border: 1px solid #dee2e6; padding: 8px;">الفرق</th>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">المبيعات</td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${cashbox.sales_total}</td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${sales.total}</td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${Math.abs(cashbox.sales_total - sales.total)}</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">المشتريات</td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${cashbox.purchases_total}</td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${purchases.total}</td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${Math.abs(cashbox.purchases_total - purchases.total)}</td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">مصاريف النقل</td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${cashbox.transport_total}</td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${purchases.transport}</td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${Math.abs(cashbox.transport_total - purchases.transport)}</td>
                                </tr>
                                <tr style="background: #fff3cd;">
                                    <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>الأرباح</strong></td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>${cashbox.profit_total}</strong></td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>${calculatedFromTransactions}</strong></td>
                                    <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>${Math.abs(cashbox.profit_total - calculatedFromTransactions)}</strong></td>
                                </tr>
                            </table>
                        </div>
                    `;

                    const totalDifference = Math.abs(cashbox.profit_total - calculatedFromTransactions);
                    
                    if (totalDifference > 0.01) {
                        html += `
                            <div class="warning">
                                ⚠️ <strong>تم اكتشاف تضارب في الأرباح: ${totalDifference.toFixed(2)} د.ل</strong>
                                <br>يُنصح بتشغيل إعادة البناء لحل هذه المشكلة.
                            </div>
                        `;
                    } else {
                        html += `
                            <div class="success">
                                ✅ <strong>قيم الأرباح متطابقة!</strong>
                                <br>لا حاجة لإعادة البناء.
                            </div>
                        `;
                    }

                    resultsDiv.innerHTML = html;
                    log(`✅ انتهى التشخيص - الفرق في الأرباح: ${totalDifference.toFixed(2)} د.ل`);
                }

            } catch (error) {
                log(`❌ خطأ في التشخيص: ${error.message}`);
                resultsDiv.innerHTML = `<div class="danger">❌ خطأ في التشخيص: ${error.message}</div>`;
            }
        }

        async function startRebuild() {
            if (isRebuilding) return;
            
            if (!confirm('هل أنت متأكد من بدء إعادة البناء؟\n\nهذا الإجراء سيعيد حساب جميع الأرباح في النظام.\nتأكد من عدم وجود مستخدمين آخرين.')) {
                return;
            }

            isRebuilding = true;
            const rebuildButton = document.getElementById('rebuildButton');
            const resultsDiv = document.getElementById('rebuildResults');
            
            rebuildButton.disabled = true;
            rebuildButton.textContent = '⏳ جاري إعادة البناء...';
            
            log('🚀 بدء إعادة البناء الآمنة...');
            updateProgress(0);

            try {
                // الخطوة 1: إعادة حساب أرباح المعاملات
                log('📊 الخطوة 1: إعادة حساب أرباح المعاملات...');
                updateProgress(20);

                const recalculateQuery = `
                    UPDATE transactions 
                    SET profit = CASE 
                        WHEN transaction_type = 'sale' THEN
                            CASE 
                                WHEN EXISTS (SELECT 1 FROM inventory WHERE item_id = transactions.item_id AND last_purchase_price > 0) THEN
                                    (selling_price - (SELECT last_purchase_price FROM inventory WHERE item_id = transactions.item_id)) * quantity - COALESCE(transport_cost, 0)
                                WHEN EXISTS (SELECT 1 FROM inventory WHERE item_id = transactions.item_id AND avg_price > 0) THEN
                                    (selling_price - (SELECT avg_price FROM inventory WHERE item_id = transactions.item_id)) * quantity - COALESCE(transport_cost, 0)
                                ELSE
                                    total_price * 0.2
                            END
                        ELSE profit
                    END
                    WHERE transaction_type = 'sale'
                `;

                const recalculateResult = await window.api.invoke('execute-direct-query', { query: recalculateQuery });
                
                if (!recalculateResult.success) {
                    throw new Error('فشل في إعادة حساب أرباح المعاملات');
                }

                log('✅ تم إعادة حساب أرباح المعاملات');
                updateProgress(50);

                // الخطوة 2: إعادة بناء إجماليات الخزينة
                log('🏦 الخطوة 2: إعادة بناء إجماليات الخزينة...');

                const updateCashboxQuery = `
                    UPDATE cashbox SET 
                        sales_total = (SELECT COALESCE(SUM(total_price), 0) FROM transactions WHERE transaction_type = 'sale'),
                        purchases_total = (SELECT COALESCE(SUM(total_price), 0) FROM transactions WHERE transaction_type = 'purchase'),
                        transport_total = (SELECT COALESCE(SUM(transport_cost), 0) FROM transactions WHERE transaction_type = 'purchase'),
                        returns_total = (SELECT COALESCE(SUM(total_price), 0) FROM transactions WHERE transaction_type = 'return'),
                        profit_total = (SELECT COALESCE(SUM(profit), 0) FROM transactions WHERE transaction_type = 'sale') - 
                                      (SELECT COALESCE(SUM(ABS(profit)), 0) FROM transactions WHERE transaction_type = 'return'),
                        updated_at = datetime('now')
                `;

                const updateResult = await window.api.invoke('execute-direct-query', { query: updateCashboxQuery });
                
                if (!updateResult.success) {
                    throw new Error('فشل في تحديث إجماليات الخزينة');
                }

                log('✅ تم تحديث إجماليات الخزينة');
                updateProgress(80);

                // الخطوة 3: التحقق من النتائج
                log('🔍 الخطوة 3: التحقق من النتائج...');

                const cashboxAfter = await window.api.cashbox.get();
                const calculatedProfit = cashboxAfter.sales_total - cashboxAfter.purchases_total - cashboxAfter.transport_total;
                const difference = Math.abs(cashboxAfter.profit_total - calculatedProfit);

                updateProgress(100);

                let resultHtml = `
                    <div class="success">
                        <h4>🎉 تمت إعادة البناء بنجاح!</h4>
                        <p><strong>النتائج النهائية:</strong></p>
                        <ul>
                            <li>الأرباح المحفوظة: ${cashboxAfter.profit_total} د.ل</li>
                            <li>الأرباح المحسوبة: ${calculatedProfit} د.ل</li>
                            <li>الفرق: ${difference.toFixed(2)} د.ل</li>
                        </ul>
                `;

                if (difference <= 0.01) {
                    resultHtml += '<p>✅ <strong>تم توحيد قيم الأرباح بنجاح!</strong></p>';
                    log('🎉 تم توحيد قيم الأرباح بنجاح!');
                } else {
                    resultHtml += '<p>⚠️ لا يزال هناك فرق طفيف، قد يكون بسبب التقريب</p>';
                    log(`⚠️ لا يزال هناك فرق طفيف: ${difference.toFixed(2)} د.ل`);
                }

                resultHtml += '</div>';
                resultsDiv.innerHTML = resultHtml;

                // إعادة تحميل الواجهة
                setTimeout(() => {
                    if (window.loadCashbox) {
                        window.loadCashbox();
                    }
                    log('🔄 تم تحديث واجهة الخزينة');
                }, 1000);

            } catch (error) {
                log(`❌ خطأ في إعادة البناء: ${error.message}`);
                resultsDiv.innerHTML = `
                    <div class="danger">
                        ❌ <strong>فشل في إعادة البناء:</strong>
                        <br>${error.message}
                        <br><br>
                        <button onclick="startRebuild()">إعادة المحاولة</button>
                    </div>
                `;
            } finally {
                isRebuilding = false;
                rebuildButton.disabled = false;
                rebuildButton.textContent = '🚀 بدء إعادة البناء الآمنة';
            }
        }

        // تشغيل التشخيص تلقائياً عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runDiagnosis, 1000);
        });
    </script>
</body>
</html>
