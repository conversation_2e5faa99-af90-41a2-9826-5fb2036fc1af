/**
 * سكريبت بناء مبسط لتوفير مساحة القرص
 * يقوم ببناء نسخة محمولة فقط لتوفير المساحة
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء عملية البناء المبسط (توفير المساحة)...\n');

// دالة لتنفيذ الأوامر مع عرض النتائج
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const result = execSync(command, { 
      stdio: 'inherit', 
      encoding: 'utf8',
      cwd: process.cwd()
    });
    console.log(`✅ ${description} - تم بنجاح\n`);
    return true;
  } catch (error) {
    console.error(`❌ فشل في ${description}:`);
    console.error(error.message);
    return false;
  }
}

// دالة للتحقق من المساحة المتاحة
function checkDiskSpace() {
  console.log('💾 فحص مساحة القرص المتاحة...');
  try {
    const stats = fs.statSync('.');
    console.log('✅ تم فحص مساحة القرص\n');
    return true;
  } catch (error) {
    console.error('❌ فشل في فحص مساحة القرص:', error.message);
    return false;
  }
}

// دالة لتنظيف ملفات مؤقتة
function cleanupTempFiles() {
  console.log('🧹 تنظيف الملفات المؤقتة...');
  
  const filesToClean = [
    'bundle.js',
    'bundle.js.map',
    'dist'
  ];
  
  filesToClean.forEach(file => {
    try {
      if (fs.existsSync(file)) {
        if (fs.statSync(file).isDirectory()) {
          fs.rmSync(file, { recursive: true, force: true });
          console.log(`   ✅ تم حذف مجلد: ${file}`);
        } else {
          fs.unlinkSync(file);
          console.log(`   ✅ تم حذف ملف: ${file}`);
        }
      }
    } catch (error) {
      console.log(`   ⚠️ لم يتم حذف ${file}: ${error.message}`);
    }
  });
  
  console.log('✅ تم تنظيف الملفات المؤقتة\n');
}

// الدالة الرئيسية
async function main() {
  console.log('=' .repeat(50));
  console.log('🏗️  البناء المبسط - توفير المساحة');
  console.log('=' .repeat(50));
  console.log();
  
  // فحص مساحة القرص
  if (!checkDiskSpace()) {
    console.error('❌ مشكلة في فحص مساحة القرص');
    process.exit(1);
  }
  
  // تنظيف الملفات المؤقتة
  cleanupTempFiles();
  
  // بناء الواجهة الأمامية فقط
  if (!runCommand('npx webpack --mode production', 'بناء الواجهة الأمامية')) {
    console.error('❌ فشل في بناء الواجهة الأمامية');
    process.exit(1);
  }
  
  // التحقق من إنشاء bundle.js
  if (!fs.existsSync('bundle.js')) {
    console.error('❌ فشل في إنشاء bundle.js');
    process.exit(1);
  }
  
  console.log('✅ تم إنشاء bundle.js بنجاح');
  
  // إنشاء نسخة محمولة فقط (أصغر حجماً)
  console.log('📦 إنشاء النسخة المحمولة (أصغر حجماً)...');
  if (!runCommand('npx electron-builder --win portable --publish never', 'إنشاء النسخة المحمولة')) {
    console.error('❌ فشل في إنشاء النسخة المحمولة');
    
    // محاولة بديلة: إنشاء مجلد التطبيق فقط
    console.log('🔄 محاولة إنشاء مجلد التطبيق بدلاً من ذلك...');
    if (!runCommand('npx electron-builder --dir --publish never', 'إنشاء مجلد التطبيق')) {
      console.error('❌ فشل في جميع محاولات البناء');
      console.log('\n💡 اقتراحات:');
      console.log('1. تحرير مساحة أكبر على القرص الصلب');
      console.log('2. نقل المشروع إلى قرص آخر به مساحة أكبر');
      console.log('3. استخدام قرص خارجي للبناء');
      process.exit(1);
    }
  }
  
  // التحقق من نجاح البناء
  if (fs.existsSync('dist')) {
    console.log('\n🎉 تم بناء التطبيق بنجاح!');
    console.log('📁 ستجد الملفات في مجلد: dist/');
    
    // عرض الملفات المُنشأة
    try {
      const distFiles = fs.readdirSync('dist');
      console.log('\n📋 الملفات المُنشأة:');
      distFiles.forEach(file => {
        try {
          const filePath = path.join('dist', file);
          const stats = fs.statSync(filePath);
          if (stats.isFile()) {
            const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
            console.log(`   📄 ${file} (${sizeInMB} MB)`);
          } else {
            console.log(`   📁 ${file}/`);
          }
        } catch (error) {
          console.log(`   📄 ${file}`);
        }
      });
    } catch (error) {
      console.log('   (لا يمكن عرض تفاصيل الملفات)');
    }
    
    console.log('\n✨ يمكنك الآن استخدام الملفات المُنشأة!');
    console.log('\n💡 ملاحظة: تم إنشاء نسخة مبسطة لتوفير المساحة');
    console.log('   للحصول على مثبت كامل، حرر مساحة أكبر وشغل npm run build-installer');
  } else {
    console.error('❌ فشل في إنشاء مجلد dist');
    process.exit(1);
  }
}

// تشغيل السكريبت
main().catch(error => {
  console.error('❌ خطأ غير متوقع:', error);
  process.exit(1);
});
