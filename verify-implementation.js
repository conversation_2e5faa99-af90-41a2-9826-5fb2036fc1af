/**
 * التحقق من تنفيذ ميزة إلغاء فواتير الشراء
 */

const fs = require('fs');
const path = require('path');

function verifyImplementation() {
  console.log('🔍 التحقق من تنفيذ ميزة إلغاء فواتير الشراء...\n');
  
  const checks = [];
  
  // 1. التحقق من ملفات قاعدة البيانات
  console.log('📁 التحقق من ملفات قاعدة البيانات:');
  
  const dbFiles = [
    'database-schema.sql',
    'add-transaction-cancellation-fields.js',
    'simple-db-migration.js'
  ];
  
  dbFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    checks.push({ name: `Database file: ${file}`, passed: exists });
  });
  
  // 2. التحقق من ملفات الخلفية
  console.log('\n🔧 التحقق من ملفات الخلفية:');
  
  const backendFiles = [
    'purchase-invoice-cancellation-manager.js',
    'main.js',
    'ipc-handlers.js'
  ];
  
  backendFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    checks.push({ name: `Backend file: ${file}`, passed: exists });
  });
  
  // 3. التحقق من ملفات الواجهة الأمامية
  console.log('\n🎨 التحقق من ملفات الواجهة الأمامية:');
  
  const frontendFiles = [
    'src/components/CancellationConfirmDialog.js',
    'src/components/CancellationConfirmDialog.css',
    'src/pages/Purchases.js',
    'src/pages/Purchases.css'
  ];
  
  frontendFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    checks.push({ name: `Frontend file: ${file}`, passed: exists });
  });
  
  // 4. التحقق من محتوى الملفات المهمة
  console.log('\n📝 التحقق من محتوى الملفات:');
  
  // التحقق من إضافة import في Purchases.js
  try {
    const purchasesContent = fs.readFileSync('src/pages/Purchases.js', 'utf8');
    const hasCancellationImport = purchasesContent.includes('CancellationConfirmDialog');
    console.log(`  ${hasCancellationImport ? '✅' : '❌'} Purchases.js يحتوي على import للحوار`);
    checks.push({ name: 'Purchases.js import', passed: hasCancellationImport });
    
    const hasCancellationState = purchasesContent.includes('showCancellationDialog');
    console.log(`  ${hasCancellationState ? '✅' : '❌'} Purchases.js يحتوي على state للإلغاء`);
    checks.push({ name: 'Purchases.js state', passed: hasCancellationState });
    
    const hasCancellationFunction = purchasesContent.includes('handleCancelPurchase');
    console.log(`  ${hasCancellationFunction ? '✅' : '❌'} Purchases.js يحتوي على دالة الإلغاء`);
    checks.push({ name: 'Purchases.js function', passed: hasCancellationFunction });
  } catch (error) {
    console.log(`  ❌ خطأ في قراءة Purchases.js: ${error.message}`);
    checks.push({ name: 'Purchases.js content', passed: false });
  }
  
  // التحقق من إضافة import في main.js
  try {
    const mainContent = fs.readFileSync('main.js', 'utf8');
    const hasCancellationManagerImport = mainContent.includes('purchase-invoice-cancellation-manager');
    console.log(`  ${hasCancellationManagerImport ? '✅' : '❌'} main.js يحتوي على import للمدير`);
    checks.push({ name: 'main.js import', passed: hasCancellationManagerImport });
  } catch (error) {
    console.log(`  ❌ خطأ في قراءة main.js: ${error.message}`);
    checks.push({ name: 'main.js content', passed: false });
  }
  
  // التحقق من إضافة handlers في ipc-handlers.js
  try {
    const ipcContent = fs.readFileSync('ipc-handlers.js', 'utf8');
    const hasValidationHandler = ipcContent.includes('validate-purchase-cancellation');
    console.log(`  ${hasValidationHandler ? '✅' : '❌'} ipc-handlers.js يحتوي على handler التحقق`);
    checks.push({ name: 'IPC validation handler', passed: hasValidationHandler });
    
    const hasCancellationHandler = ipcContent.includes('cancel-purchase-invoice');
    console.log(`  ${hasCancellationHandler ? '✅' : '❌'} ipc-handlers.js يحتوي على handler الإلغاء`);
    checks.push({ name: 'IPC cancellation handler', passed: hasCancellationHandler });
  } catch (error) {
    console.log(`  ❌ خطأ في قراءة ipc-handlers.js: ${error.message}`);
    checks.push({ name: 'ipc-handlers.js content', passed: false });
  }
  
  // 5. التحقق من قاعدة البيانات
  console.log('\n🗄️ التحقق من قاعدة البيانات:');
  
  const dbPaths = [
    './wms-database.db',
    './wms-database/warehouse.db',
    './warehouse.db'
  ];
  
  let dbFound = false;
  for (const dbPath of dbPaths) {
    if (fs.existsSync(dbPath)) {
      console.log(`  ✅ تم العثور على قاعدة البيانات: ${dbPath}`);
      dbFound = true;
      break;
    }
  }
  
  if (!dbFound) {
    console.log('  ❌ لم يتم العثور على قاعدة البيانات');
  }
  
  checks.push({ name: 'Database exists', passed: dbFound });
  
  // 6. ملخص النتائج
  console.log('\n📊 ملخص النتائج:');
  
  const passedChecks = checks.filter(check => check.passed).length;
  const totalChecks = checks.length;
  const successRate = Math.round((passedChecks / totalChecks) * 100);
  
  console.log(`  ✅ نجح: ${passedChecks}/${totalChecks} (${successRate}%)`);
  console.log(`  ❌ فشل: ${totalChecks - passedChecks}/${totalChecks}`);
  
  if (successRate >= 90) {
    console.log('\n🎉 التنفيذ مكتمل وجاهز للاختبار!');
  } else if (successRate >= 70) {
    console.log('\n⚠️ التنفيذ شبه مكتمل، قد تحتاج لبعض التعديلات');
  } else {
    console.log('\n❌ التنفيذ غير مكتمل، يحتاج لمراجعة');
  }
  
  // 7. الخطوات التالية
  console.log('\n📋 الخطوات التالية:');
  console.log('  1. تشغيل ترقية قاعدة البيانات: node simple-db-migration.js');
  console.log('  2. إعادة تشغيل التطبيق');
  console.log('  3. اتباع دليل الاختبار في CANCELLATION_TEST_GUIDE.md');
  console.log('  4. التحقق من عمل الميزة في الواجهة');
  
  return {
    totalChecks,
    passedChecks,
    successRate,
    checks
  };
}

// تشغيل التحقق
if (require.main === module) {
  verifyImplementation();
}

module.exports = { verifyImplementation };
