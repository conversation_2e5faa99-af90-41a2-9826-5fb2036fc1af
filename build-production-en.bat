@echo off
echo ========================================
echo Building WMS for Production
echo Warehouse Management System v1.1.1
echo With Purchase Invoice Cancellation
echo ========================================
echo.

REM Change to project directory
cd /d "%~dp0"

REM Check Node.js
echo [1/8] Checking Node.js...
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Node.js not found
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

echo SUCCESS: Node.js found
node --version
npm --version
echo.

REM Clean old files
echo [2/8] Cleaning old files...
if exist "bundle.js" del "bundle.js"
if exist "bundle.js.map" del "bundle.js.map"
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "dist-final" rmdir /s /q "dist-final" 2>nul
echo SUCCESS: Cleanup completed
echo.

REM Install dependencies
echo [3/8] Installing dependencies...
npm install
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo SUCCESS: Dependencies installed
echo.

REM Apply database migration
echo [4/8] Applying database migration...
node simple-db-migration.js
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Database migration applied
) else (
    echo WARNING: Database migration may need manual application
    echo Use SQLite Browser with manual-db-migration.sql
)
echo.

REM Build with Webpack
echo [5/8] Building with Webpack (Production Mode)...
npx webpack --mode production --progress
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Webpack build failed
    echo Try: node node_modules\webpack\bin\webpack.js --mode production
    pause
    exit /b 1
)
echo SUCCESS: Webpack build completed
echo.

REM Verify built files
echo [6/8] Verifying built files...
if not exist "bundle.js" (
    echo ERROR: bundle.js not found
    pause
    exit /b 1
)
echo SUCCESS: bundle.js found
echo.

REM Build with Electron Builder
echo [7/8] Building with Electron Builder...
npm run dist
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Electron Builder failed
    echo Try: npx electron-builder
    pause
    exit /b 1
)
echo SUCCESS: Application built successfully
echo.

REM Show results
echo [8/8] Build completed successfully!
echo.
echo Built files:
if exist "dist-final" (
    echo   - dist-final folder
    if exist "dist-final\win-unpacked" echo   - Executable app in win-unpacked
    for %%f in (dist-final\*.exe) do echo   - Installer: %%f
)
if exist "bundle.js" echo   - bundle.js
if exist "bundle.js.map" echo   - bundle.js.map
echo.

echo New features added:
echo   - Purchase invoice cancellation
echo   - Transaction audit log
echo   - Invoice status filtering
echo   - User permissions system
echo   - Confirmation dialogs
echo.

echo You can now:
echo   1. Run app from: dist-final\win-unpacked\
echo   2. Distribute installer: dist-final\*.exe
echo   3. Test new features
echo.

echo Important notes:
echo   - Backup database before deployment
echo   - Test new features before production use
echo   - Check QUICK_TEST_STEPS.md for testing guide
echo.

pause
