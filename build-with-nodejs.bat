@echo off
echo ========================================
echo بناء التطبيق باستخدام Node.js و Webpack
echo ========================================
echo.

REM إعادة تحميل متغيرات البيئة
call refreshenv 2>nul

REM التحقق من Node.js
echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير متوفر في PATH
    echo.
    echo الحلول المحتملة:
    echo 1. أعد تشغيل PowerShell/Command Prompt
    echo 2. أعد تشغيل الكمبيوتر
    echo 3. تحقق من تثبيت Node.js بشكل صحيح
    echo.
    echo في هذه الأثناء، يمكنك:
    echo - استخدام التطبيق الموجود في dist-final
    echo - تطبيق ترقية قاعدة البيانات يدوياً
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
node --version
echo.

REM التحقق من npm
echo 🔍 التحقق من npm...
npm --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)

echo ✅ npm متوفر
npm --version
echo.

REM التحقق من node_modules
if not exist "node_modules" (
    echo 📦 تثبيت dependencies...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت dependencies
        pause
        exit /b 1
    )
)

echo ✅ Dependencies متوفرة
echo.

REM تطبيق ترقية قاعدة البيانات
echo 🗄️ تطبيق ترقية قاعدة البيانات...
node simple-db-migration.js
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم تطبيق ترقية قاعدة البيانات بنجاح
) else (
    echo ⚠️ تحذير: قد تحتاج لتطبيق ترقية قاعدة البيانات يدوياً
)
echo.

REM بناء التطبيق باستخدام webpack
echo 📦 بناء التطبيق باستخدام Webpack...
npx webpack --mode production --progress

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo.
    echo الملفات المبنية:
    if exist "bundle.js" echo   ✅ bundle.js
    if exist "bundle.js.map" echo   ✅ bundle.js.map
    echo.
    echo يمكنك الآن:
    echo 1. تشغيل التطبيق: npm start
    echo 2. أو: electron .
    echo 3. أو بناء المثبت: npm run dist
    echo.
    echo 🎉 ميزة إلغاء فواتير الشراء جاهزة للاختبار!
) else (
    echo.
    echo ❌ فشل في بناء التطبيق
    echo تحقق من الأخطاء أعلاه
    echo.
    echo يمكنك استخدام التطبيق الموجود في dist-final في هذه الأثناء
)

echo.
pause
