/**
 * إعدادات electron-builder المتقدمة
 * ملف تكوين منفصل لإعدادات البناء المعقدة
 */

const path = require('path');

module.exports = {
  // معلومات التطبيق الأساسية
  appId: "com.hgroup.wms",
  productName: "نظام إدارة المخازن",
  copyright: "Copyright © 2025 H Group",
  
  // مجلدات البناء
  directories: {
    output: "dist",
    buildResources: "build"
  },
  
  // الملفات المراد تضمينها
  files: [
    "**/*",
    "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}",
    "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}",
    "!**/node_modules/*.d.ts",
    "!**/node_modules/.bin",
    "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}",
    "!.editorconfig",
    "!**/._*",
    "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}",
    "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}",
    "!**/{appveyor.yml,.travis.yml,circle.yml}",
    "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}",
    "!**/test*.js",
    "!**/fix*.js",
    "!**/quick*.js",
    "!**/debug*.js",
    "!**/diagnose*.js",
    "!**/*.md",
    "!src/**/*",
    "bundle.js",
    "bundle.js.map",
    "main.js",
    "preload.js",
    "index.html",
    "package.json",
    "assets/**/*",
    "wms-database/**/*",
    "*.js",
    "!webpack.config.js",
    "!babel.config.js",
    "!electron-builder.config.js",
    "!build-installer.js"
  ],
  
  // الموارد الإضافية
  extraResources: [
    {
      from: "wms-database",
      to: "wms-database",
      filter: ["**/*"]
    },
    {
      from: "README.txt",
      to: "README.txt"
    }
  ],
  
  // إعدادات Windows
  win: {
    target: [
      {
        target: "nsis",
        arch: ["x64"]
      },
      {
        target: "portable",
        arch: ["x64"]
      }
    ],
    icon: "assets/icons/share_ico_socialnetwork_16174.ico",
    requestedExecutionLevel: "asInvoker",
    artifactName: "${productName}-${version}-${arch}.${ext}",
    
    // إعدادات الأمان
    certificateFile: null, // يمكن إضافة شهادة رقمية هنا
    certificatePassword: null,
    
    // إعدادات إضافية
    publisherName: "H Group",
    verifyUpdateCodeSignature: false
  },
  
  // إعدادات مثبت NSIS
  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    allowElevation: true,
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: "نظام إدارة المخازن",
    artifactName: "نظام-إدارة-المخازن-${version}-installer.${ext}",
    menuCategory: "H Group",
    displayLanguageSelector: false,
    language: "1025", // Arabic
    perMachine: false,
    installerIcon: "assets/icons/share_ico_socialnetwork_16174.ico",
    uninstallerIcon: "assets/icons/share_ico_socialnetwork_16174.ico",
    installerHeaderIcon: "assets/icons/share_ico_socialnetwork_16174.ico",
    deleteAppDataOnUninstall: false,
    runAfterFinish: true,
    include: "build/installer.nsh",
    
    // إعدادات متقدمة للمثبت
    warningsAsErrors: false,
    unicode: true,
    guid: "a1b2c3d4-e5f6-7890-abcd-ef1234567890", // معرف فريد للتطبيق
    
    // رسائل مخصصة
    installerLanguages: ["ar", "en"],
    multiLanguageInstaller: false
  },
  
  // إعدادات النسخة المحمولة
  portable: {
    artifactName: "نظام-إدارة-المخازن-${version}-portable.${ext}",
    requestExecutionLevel: "user"
  },
  
  // إعدادات النشر (معطلة)
  publish: null,
  
  // إعدادات ضغط الملفات
  compression: "maximum",
  
  // إعدادات البناء المتوازي
  buildDependenciesFromSource: false,
  nodeGypRebuild: false,
  npmRebuild: true,
  
  // إعدادات التوقيع الرقمي (اختيارية)
  forceCodeSigning: false,
  
  // معلومات إضافية للملف التنفيذي
  extraMetadata: {
    main: "main.js",
    homepage: "https://hgroup.com",
    repository: {
      type: "git",
      url: "https://github.com/hgroup/warehouse-management-system"
    },
    bugs: {
      url: "https://github.com/hgroup/warehouse-management-system/issues"
    }
  },
  
  // إعدادات التحقق من التحديثات
  electronUpdaterCompatibility: ">=2.16",
  
  // إعدادات البناء للمطورين
  buildVersion: process.env.BUILD_NUMBER || undefined,
  
  // معالجة الأخطاء
  detectUpdateChannel: false,
  generateUpdatesFilesForAllChannels: false,
  
  // إعدادات الأداء
  removePackageScripts: true,
  removePackageKeywords: true,
  
  // إعدادات إضافية للأمان
  protocols: [
    {
      name: "warehouse-management-system",
      schemes: ["wms"]
    }
  ]
};
