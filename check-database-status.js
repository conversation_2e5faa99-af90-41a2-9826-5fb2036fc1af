/**
 * فحص حالة قاعدة البيانات والتحقق من وجود الحقول الجديدة
 */

const Database = require('better-sqlite3');
const fs = require('fs');

function checkDatabaseStatus() {
  console.log('🔍 فحص حالة قاعدة البيانات...\n');
  
  // البحث عن قاعدة البيانات
  const dbPaths = [
    './wms-database.db',
    './wms-database/warehouse.db',
    './warehouse.db'
  ];
  
  let dbPath = null;
  for (const testPath of dbPaths) {
    try {
      if (fs.existsSync(testPath)) {
        dbPath = testPath;
        break;
      }
    } catch (e) {
      continue;
    }
  }
  
  if (!dbPath) {
    console.error('❌ لم يتم العثور على قاعدة البيانات');
    return false;
  }
  
  console.log(`📁 تم العثور على قاعدة البيانات: ${dbPath}\n`);
  
  try {
    const db = new Database(dbPath, { readonly: true });
    
    // فحص جدول المعاملات
    console.log('📋 فحص جدول المعاملات:');
    const columns = db.prepare("PRAGMA table_info(transactions)").all();
    const columnNames = columns.map(col => col.name);
    
    console.log(`  الأعمدة الموجودة (${columns.length}):`, columnNames.join(', '));
    
    // التحقق من الحقول الجديدة
    const requiredFields = ['status', 'cancelled_at', 'cancelled_by', 'cancellation_reason'];
    const missingFields = [];
    
    console.log('\n🔍 التحقق من الحقول الجديدة:');
    for (const field of requiredFields) {
      const exists = columnNames.includes(field);
      console.log(`  ${exists ? '✅' : '❌'} ${field}`);
      if (!exists) {
        missingFields.push(field);
      }
    }
    
    // فحص جدول سجل التدقيق
    console.log('\n📝 فحص جدول سجل التدقيق:');
    const auditTableExists = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='transaction_audit_log'
    `).get();
    
    console.log(`  ${auditTableExists ? '✅' : '❌'} جدول transaction_audit_log`);
    
    if (auditTableExists) {
      const auditColumns = db.prepare("PRAGMA table_info(transaction_audit_log)").all();
      console.log(`  أعمدة جدول التدقيق (${auditColumns.length}):`, auditColumns.map(col => col.name).join(', '));
    }
    
    // فحص بيانات المعاملات
    console.log('\n📊 إحصائيات المعاملات:');
    const totalTransactions = db.prepare("SELECT COUNT(*) as count FROM transactions").get();
    console.log(`  إجمالي المعاملات: ${totalTransactions.count}`);
    
    const purchaseTransactions = db.prepare("SELECT COUNT(*) as count FROM transactions WHERE transaction_type = 'purchase'").get();
    console.log(`  معاملات الشراء: ${purchaseTransactions.count}`);
    
    if (columnNames.includes('status')) {
      const activeTransactions = db.prepare("SELECT COUNT(*) as count FROM transactions WHERE status = 'active' OR status IS NULL").get();
      const cancelledTransactions = db.prepare("SELECT COUNT(*) as count FROM transactions WHERE status = 'cancelled'").get();
      console.log(`  المعاملات النشطة: ${activeTransactions.count}`);
      console.log(`  المعاملات الملغاة: ${cancelledTransactions.count}`);
    }
    
    // فحص الفهارس
    console.log('\n🗂️ فحص الفهارس:');
    const indexes = db.prepare("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE '%transaction%'").all();
    console.log(`  الفهارس المرتبطة بالمعاملات (${indexes.length}):`);
    indexes.forEach(index => {
      console.log(`    ✅ ${index.name}`);
    });
    
    db.close();
    
    // ملخص النتائج
    console.log('\n📋 ملخص النتائج:');
    if (missingFields.length === 0 && auditTableExists) {
      console.log('✅ قاعدة البيانات محدثة ومجهزة لميزة إلغاء الفواتير');
      return true;
    } else {
      console.log('❌ قاعدة البيانات تحتاج لتحديث:');
      if (missingFields.length > 0) {
        console.log(`  - حقول مفقودة: ${missingFields.join(', ')}`);
      }
      if (!auditTableExists) {
        console.log('  - جدول سجل التدقيق مفقود');
      }
      console.log('\n💡 لتحديث قاعدة البيانات، قم بتشغيل:');
      console.log('   node simple-db-migration.js');
      console.log('   أو استخدم ملف manual-db-migration.sql');
      return false;
    }
    
  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error.message);
    return false;
  }
}

// تشغيل الفحص
if (require.main === module) {
  const success = checkDatabaseStatus();
  process.exit(success ? 0 : 1);
}

module.exports = { checkDatabaseStatus };
