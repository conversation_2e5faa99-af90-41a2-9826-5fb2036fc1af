#!/usr/bin/env node

/**
 * إعادة بناء شاملة وآمنة لنظام حساب الأرباح
 * 
 * هذا السكريبت يحل المشاكل الناتجة عن فصل last_purchase_price عن avg_price
 * ويعيد بناء جميع الحسابات بشكل صحيح وآمن للبيئة الإنتاجية
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// مسار قاعدة البيانات
const dbPath = path.join(__dirname, 'wms.db');

console.log('🔧 بدء إعادة البناء الشاملة لنظام الأرباح...');
console.log('📁 مسار قاعدة البيانات:', dbPath);

async function comprehensiveProfitRebuild() {
  let db;
  
  try {
    // 1. إنشاء نسخة احتياطية
    console.log('💾 إنشاء نسخة احتياطية...');
    const backupPath = path.join(__dirname, `wms_backup_${Date.now()}.db`);
    fs.copyFileSync(dbPath, backupPath);
    console.log('✅ تم إنشاء نسخة احتياطية:', backupPath);
    
    // 2. فتح قاعدة البيانات
    db = new Database(dbPath);
    console.log('✅ تم فتح قاعدة البيانات');
    
    // 3. تشخيص شامل للمشكلة
    console.log('\n🔍 تشخيص شامل للمشكلة...');
    
    // فحص بيانات الخزينة الحالية
    const cashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const cashbox = cashboxStmt.get();
    
    if (!cashbox) {
      throw new Error('لا توجد خزينة في النظام');
    }
    
    console.log('📊 بيانات الخزينة الحالية:');
    console.log(`   - المبيعات: ${cashbox.sales_total}`);
    console.log(`   - المشتريات: ${cashbox.purchases_total}`);
    console.log(`   - مصاريف النقل: ${cashbox.transport_total}`);
    console.log(`   - المرتجعات: ${cashbox.returns_total}`);
    console.log(`   - الأرباح المحفوظة: ${cashbox.profit_total}`);
    
    // فحص المعاملات الفعلية
    const salesStmt = db.prepare(`
      SELECT 
        COUNT(*) as count,
        SUM(total_price) as total_amount,
        SUM(CASE WHEN profit IS NOT NULL THEN profit ELSE 0 END) as total_profit
      FROM transactions 
      WHERE transaction_type = 'sale'
    `);
    const salesData = salesStmt.get();
    
    const purchasesStmt = db.prepare(`
      SELECT 
        COUNT(*) as count,
        SUM(total_price) as total_amount,
        SUM(CASE WHEN transport_cost IS NOT NULL THEN transport_cost ELSE 0 END) as total_transport
      FROM transactions 
      WHERE transaction_type = 'purchase'
    `);
    const purchasesData = purchasesStmt.get();
    
    const returnsStmt = db.prepare(`
      SELECT 
        COUNT(*) as count,
        SUM(total_price) as total_amount,
        SUM(CASE WHEN profit IS NOT NULL THEN ABS(profit) ELSE 0 END) as total_return_profit
      FROM transactions 
      WHERE transaction_type = 'return'
    `);
    const returnsData = returnsStmt.get();
    
    console.log('\n📈 بيانات المعاملات الفعلية:');
    console.log(`   - المبيعات: ${salesData.count} معاملة، إجمالي: ${salesData.total_amount}، أرباح: ${salesData.total_profit}`);
    console.log(`   - المشتريات: ${purchasesData.count} معاملة، إجمالي: ${purchasesData.total_amount}، نقل: ${purchasesData.total_transport}`);
    console.log(`   - المرتجعات: ${returnsData.count} معاملة، إجمالي: ${returnsData.total_amount}، أرباح مخصومة: ${returnsData.total_return_profit}`);
    
    // 4. فحص مشكلة avg_price vs last_purchase_price
    console.log('\n🔍 فحص مشكلة الأسعار...');
    
    const priceIssuesStmt = db.prepare(`
      SELECT 
        COUNT(*) as total_items,
        COUNT(CASE WHEN avg_price IS NULL OR avg_price = 0 THEN 1 END) as missing_avg_price,
        COUNT(CASE WHEN last_purchase_price IS NULL OR last_purchase_price = 0 THEN 1 END) as missing_last_price,
        COUNT(CASE WHEN avg_price != last_purchase_price THEN 1 END) as price_mismatch
      FROM inventory
    `);
    const priceIssues = priceIssuesStmt.get();
    
    console.log('💰 تحليل الأسعار:');
    console.log(`   - إجمالي الأصناف: ${priceIssues.total_items}`);
    console.log(`   - أصناف بدون متوسط سعر: ${priceIssues.missing_avg_price}`);
    console.log(`   - أصناف بدون آخر سعر شراء: ${priceIssues.missing_last_price}`);
    console.log(`   - أصناف بأسعار مختلفة: ${priceIssues.price_mismatch}`);
    
    // 5. إعادة بناء شاملة
    console.log('\n🔧 بدء إعادة البناء الشاملة...');
    
    // بدء معاملة قاعدة البيانات
    const rebuildTransaction = db.transaction(() => {
      
      // أ. إعادة حساب أرباح المعاملات
      console.log('📊 إعادة حساب أرباح المعاملات...');
      
      const salesTransactions = db.prepare(`
        SELECT 
          t.id,
          t.item_id,
          t.quantity,
          t.selling_price,
          t.total_price,
          t.transport_cost,
          i.avg_price,
          i.last_purchase_price
        FROM transactions t
        LEFT JOIN inventory i ON t.item_id = i.item_id
        WHERE t.transaction_type = 'sale'
      `).all();
      
      let recalculatedCount = 0;
      const updateProfitStmt = db.prepare('UPDATE transactions SET profit = ? WHERE id = ?');
      
      salesTransactions.forEach(transaction => {
        let newProfit = 0;
        
        // استخدام آخر سعر شراء إذا كان متوفراً، وإلا متوسط السعر
        const costPrice = (transaction.last_purchase_price && transaction.last_purchase_price > 0) 
          ? transaction.last_purchase_price 
          : (transaction.avg_price || 0);
        
        if (transaction.selling_price > 0 && costPrice > 0) {
          // حساب الربح الأساسي
          const basicProfit = (transaction.selling_price - costPrice) * transaction.quantity;
          
          // خصم مصاريف النقل إذا كانت موجودة
          const transportCost = transaction.transport_cost || 0;
          newProfit = basicProfit - transportCost;
        } else {
          // تقدير 20% من إجمالي السعر
          newProfit = transaction.total_price * 0.2;
        }
        
        updateProfitStmt.run(newProfit, transaction.id);
        recalculatedCount++;
      });
      
      console.log(`✅ تم إعادة حساب أرباح ${recalculatedCount} معاملة بيع`);
      
      // ب. إعادة بناء إجماليات الخزينة
      console.log('🏦 إعادة بناء إجماليات الخزينة...');
      
      const newSalesTotal = db.prepare(`
        SELECT COALESCE(SUM(total_price), 0) as total 
        FROM transactions 
        WHERE transaction_type = 'sale'
      `).get().total;
      
      const newPurchasesTotal = db.prepare(`
        SELECT COALESCE(SUM(total_price), 0) as total 
        FROM transactions 
        WHERE transaction_type = 'purchase'
      `).get().total;
      
      const newTransportTotal = db.prepare(`
        SELECT COALESCE(SUM(transport_cost), 0) as total 
        FROM transactions 
        WHERE transaction_type = 'purchase'
      `).get().total;
      
      const newReturnsTotal = db.prepare(`
        SELECT COALESCE(SUM(total_price), 0) as total 
        FROM transactions 
        WHERE transaction_type = 'return'
      `).get().total;
      
      const newProfitTotal = db.prepare(`
        SELECT COALESCE(SUM(profit), 0) as total 
        FROM transactions 
        WHERE transaction_type = 'sale'
      `).get().total - db.prepare(`
        SELECT COALESCE(SUM(ABS(profit)), 0) as total 
        FROM transactions 
        WHERE transaction_type = 'return'
      `).get().total;
      
      // تحديث الخزينة
      const updateCashboxStmt = db.prepare(`
        UPDATE cashbox 
        SET 
          sales_total = ?,
          purchases_total = ?,
          transport_total = ?,
          returns_total = ?,
          profit_total = ?,
          updated_at = ?
        WHERE id = ?
      `);
      
      updateCashboxStmt.run(
        newSalesTotal,
        newPurchasesTotal,
        newTransportTotal,
        newReturnsTotal,
        newProfitTotal,
        new Date().toISOString(),
        cashbox.id
      );
      
      console.log('✅ تم تحديث إجماليات الخزينة');
      console.log(`   - المبيعات الجديدة: ${newSalesTotal}`);
      console.log(`   - المشتريات الجديدة: ${newPurchasesTotal}`);
      console.log(`   - مصاريف النقل الجديدة: ${newTransportTotal}`);
      console.log(`   - المرتجعات الجديدة: ${newReturnsTotal}`);
      console.log(`   - الأرباح الجديدة: ${newProfitTotal}`);
      
      return {
        salesTotal: newSalesTotal,
        purchasesTotal: newPurchasesTotal,
        transportTotal: newTransportTotal,
        returnsTotal: newReturnsTotal,
        profitTotal: newProfitTotal
      };
    });
    
    // تنفيذ المعاملة
    const result = rebuildTransaction();
    
    // 6. التحقق من النتائج
    console.log('\n✅ تم الانتهاء من إعادة البناء!');
    
    const finalCashbox = cashboxStmt.get();
    const calculatedProfit = finalCashbox.sales_total - finalCashbox.purchases_total - finalCashbox.transport_total;
    
    console.log('\n📊 النتائج النهائية:');
    console.log(`   - الأرباح المحفوظة: ${finalCashbox.profit_total}`);
    console.log(`   - الأرباح المحسوبة: ${calculatedProfit}`);
    console.log(`   - الفرق: ${Math.abs(finalCashbox.profit_total - calculatedProfit)}`);
    
    if (Math.abs(finalCashbox.profit_total - calculatedProfit) <= 0.01) {
      console.log('🎉 تم توحيد قيم الأرباح بنجاح!');
    } else {
      console.log('⚠️ لا يزال هناك فرق طفيف، قد يكون بسبب التقريب');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ خطأ في إعادة البناء:', error.message);
    return false;
  } finally {
    if (db) {
      db.close();
      console.log('🔒 تم إغلاق قاعدة البيانات');
    }
  }
}

// تشغيل إعادة البناء
if (require.main === module) {
  comprehensiveProfitRebuild().then(success => {
    if (success) {
      console.log('\n🎉 تمت إعادة البناء بنجاح!');
      console.log('💡 يُنصح بإعادة تشغيل التطبيق لضمان تحديث جميع البيانات');
    } else {
      console.log('\n❌ فشلت إعادة البناء');
      console.log('💡 تحقق من النسخة الاحتياطية واتصل بالدعم الفني');
    }
    process.exit(success ? 0 : 1);
  });
}

module.exports = { comprehensiveProfitRebuild };
