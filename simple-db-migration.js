/**
 * إضافة حقول إلغاء المعاملات - نسخة مبسطة
 */

const Database = require('better-sqlite3');
const path = require('path');

function addCancellationFields() {
  try {
    console.log('🔧 بدء إضافة حقول إلغاء المعاملات...');
    
    // البحث عن قاعدة البيانات
    const dbPaths = [
      './wms-database.db',
      './wms-database/warehouse.db',
      './warehouse.db'
    ];
    
    let dbPath = null;
    for (const testPath of dbPaths) {
      try {
        const fs = require('fs');
        if (fs.existsSync(testPath)) {
          dbPath = testPath;
          break;
        }
      } catch (e) {
        continue;
      }
    }
    
    if (!dbPath) {
      console.error('❌ لم يتم العثور على قاعدة البيانات');
      return false;
    }
    
    console.log('📁 تم العثور على قاعدة البيانات:', dbPath);
    
    // الاتصال بقاعدة البيانات
    const db = new Database(dbPath);
    
    // التحقق من وجود جدول المعاملات
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'").all();
    if (tables.length === 0) {
      console.error('❌ جدول المعاملات غير موجود');
      db.close();
      return false;
    }
    
    console.log('✅ تم العثور على جدول المعاملات');
    
    // التحقق من الأعمدة الموجودة
    const columns = db.prepare("PRAGMA table_info(transactions)").all();
    const columnNames = columns.map(col => col.name);
    
    console.log('📋 الأعمدة الموجودة:', columnNames.join(', '));
    
    // إضافة الأعمدة الجديدة
    const newColumns = [
      { name: 'status', sql: "ALTER TABLE transactions ADD COLUMN status TEXT DEFAULT 'active'" },
      { name: 'cancelled_at', sql: "ALTER TABLE transactions ADD COLUMN cancelled_at TEXT" },
      { name: 'cancelled_by', sql: "ALTER TABLE transactions ADD COLUMN cancelled_by INTEGER" },
      { name: 'cancellation_reason', sql: "ALTER TABLE transactions ADD COLUMN cancellation_reason TEXT" }
    ];
    
    for (const column of newColumns) {
      if (!columnNames.includes(column.name)) {
        try {
          console.log(`➕ إضافة عمود ${column.name}...`);
          db.prepare(column.sql).run();
          console.log(`✅ تم إضافة عمود ${column.name} بنجاح`);
        } catch (error) {
          console.error(`❌ فشل في إضافة عمود ${column.name}:`, error.message);
        }
      } else {
        console.log(`⏭️ عمود ${column.name} موجود بالفعل`);
      }
    }
    
    // تحديث المعاملات الموجودة
    try {
      const updateResult = db.prepare("UPDATE transactions SET status = 'active' WHERE status IS NULL").run();
      console.log(`✅ تم تحديث ${updateResult.changes} معاملة لتكون نشطة`);
    } catch (error) {
      console.warn('⚠️ تحذير: فشل في تحديث المعاملات الموجودة:', error.message);
    }
    
    // إنشاء جدول سجل التدقيق
    try {
      console.log('📝 إنشاء جدول سجل التدقيق...');
      db.prepare(`
        CREATE TABLE IF NOT EXISTS transaction_audit_log (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transaction_id INTEGER NOT NULL,
          action_type TEXT NOT NULL,
          old_status TEXT,
          new_status TEXT,
          reason TEXT,
          performed_by INTEGER NOT NULL,
          performed_at TEXT NOT NULL,
          additional_data TEXT,
          FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
          FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `).run();
      console.log('✅ تم إنشاء جدول سجل التدقيق بنجاح');
    } catch (error) {
      console.error('❌ فشل في إنشاء جدول سجل التدقيق:', error.message);
    }
    
    // إنشاء الفهارس
    const indexes = [
      "CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)",
      "CREATE INDEX IF NOT EXISTS idx_transactions_cancelled_by ON transactions(cancelled_by)",
      "CREATE INDEX IF NOT EXISTS idx_transaction_audit_log_transaction_id ON transaction_audit_log(transaction_id)",
      "CREATE INDEX IF NOT EXISTS idx_transaction_audit_log_performed_by ON transaction_audit_log(performed_by)"
    ];
    
    for (const indexSql of indexes) {
      try {
        db.prepare(indexSql).run();
        console.log('✅ تم إنشاء فهرس بنجاح');
      } catch (error) {
        console.warn('⚠️ تحذير: فشل في إنشاء فهرس:', error.message);
      }
    }
    
    // إغلاق الاتصال
    db.close();
    
    console.log('🎉 تم إضافة حقول إلغاء المعاملات بنجاح!');
    return true;
    
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
    return false;
  }
}

// تشغيل الوظيفة
if (require.main === module) {
  const success = addCancellationFields();
  process.exit(success ? 0 : 1);
}

module.exports = { addCancellationFields };
