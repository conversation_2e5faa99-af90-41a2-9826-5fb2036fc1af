@echo off
chcp 65001 >nul
echo ========================================
echo بناء مثبت نظام إدارة المخازن
echo مع ميزة إلغاء فواتير الشراء
echo ========================================
echo.

REM الانتقال لمجلد المشروع
cd /d "%~dp0"

echo 🔍 التحقق من المتطلبات...

REM التحقق من Node.js
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير متوفر
    pause
    exit /b 1
)

REM التحقق من الملفات المطلوبة
if not exist "package.json" (
    echo ❌ package.json غير موجود
    pause
    exit /b 1
)

if not exist "main.js" (
    echo ❌ main.js غير موجود
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.

REM تنظيف المجلدات القديمة
echo 🧹 تنظيف المجلدات القديمة...
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "dist-final" rmdir /s /q "dist-final" 2>nul
echo ✅ تم التنظيف
echo.

REM تثبيت Dependencies
echo 📦 تحديث Dependencies...
npm install --production=false
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تثبيت Dependencies
    pause
    exit /b 1
)
echo ✅ تم تحديث Dependencies
echo.

REM بناء Webpack
echo 📦 بناء Webpack للإنتاج...
npx webpack --mode production
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء Webpack
    pause
    exit /b 1
)
echo ✅ تم بناء Webpack
echo.

REM بناء المثبت
echo 🏗️ بناء المثبت...
npx electron-builder --win --publish never
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المثبت
    echo جرب تشغيل: npm run dist
    pause
    exit /b 1
)

echo.
echo 🎉 تم بناء المثبت بنجاح!
echo.

REM عرض معلومات الملفات المبنية
echo 📁 الملفات المبنية:
for %%f in (dist-final\*.exe) do (
    echo   ✅ %%f
    echo      الحجم: 
    dir "%%f" | findstr /C:"%%~nxf"
)

if exist "dist-final\win-unpacked" (
    echo   ✅ مجلد التطبيق القابل للتشغيل: dist-final\win-unpacked\
)

echo.
echo 📋 الميزات المضافة في هذا الإصدار:
echo   🆕 إلغاء فواتير الشراء
echo   🆕 سجل تدقيق شامل
echo   🆕 فلترة متقدمة للفواتير
echo   🆕 نظام صلاحيات محسن
echo   🆕 واجهة مستخدم محسنة
echo.

echo 🚀 خطوات النشر:
echo   1. اختبر التطبيق من: dist-final\win-unpacked\
echo   2. وزع ملف المثبت على المستخدمين
echo   3. تأكد من عمل نسخة احتياطية من قواعد البيانات
echo.

echo 📝 ملاحظات للمستخدمين:
echo   - سيتم تطبيق ترقية قاعدة البيانات تلقائياً
echo   - الميزات الجديدة متاحة فقط للموظفين
echo   - المديرين والمشاهدين يمكنهم رؤية البيانات فقط
echo.

pause
