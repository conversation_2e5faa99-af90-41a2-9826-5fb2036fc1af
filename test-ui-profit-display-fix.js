/**
 * اختبار إصلاح الخلل البصري في عرض إجمالي الأرباح أثناء عمليات الشراء المتتالية
 * 
 * هذا السكريبت يقوم باختبار أن الواجهة تعرض القيم الصحيحة فوراً
 * دون الحاجة لانتظار عملية بيع لتصحيح العرض
 */

console.log('🧪 بدء اختبار إصلاح الخلل البصري في عرض إجمالي الأرباح...');

// التحقق من وجود window.api
if (typeof window !== 'undefined' && window.api) {
  
  // اختبار شامل للخلل البصري
  async function testUIProfitDisplayFix() {
    try {
      console.log('📊 بدء الاختبار الشامل للخلل البصري في عرض الأرباح...');
      
      // 1. تعيين رصيد ابتدائي للاختبار
      console.log('\n1️⃣ تعيين رصيد ابتدائي للاختبار (8000)...');
      const initialBalanceResult = await window.api.cashbox.updateInitialBalance(8000);
      if (initialBalanceResult.success) {
        console.log('✅ تم تعيين الرصيد الابتدائي بنجاح');
        console.log('📊 الخزينة الأولية:', {
          initial_balance: initialBalanceResult.cashbox.initial_balance,
          current_balance: initialBalanceResult.cashbox.current_balance,
          profit_total: initialBalanceResult.cashbox.profit_total
        });
      } else {
        console.error('❌ فشل في تعيين الرصيد الابتدائي:', initialBalanceResult.error);
        return;
      }
      
      // انتظار قصير للتأكد من تحديث الواجهة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 2. عملية بيع أولى لإنشاء أرباح
      console.log('\n2️⃣ عملية بيع أولى لإنشاء أرباح (مبلغ 2000)...');
      const firstSaleResult = await window.api.cashbox.addTransaction({
        type: 'sale',
        amount: 2000,
        source: 'test',
        notes: 'بيع أولي لإنشاء أرباح',
        user_id: 1
      });
      
      if (firstSaleResult.success) {
        console.log('✅ تم تسجيل عملية البيع الأولى');
        console.log('📊 الخزينة بعد البيع الأولى:', {
          current_balance: firstSaleResult.cashbox.current_balance,
          sales_total: firstSaleResult.cashbox.sales_total,
          profit_total: firstSaleResult.cashbox.profit_total,
          expected_profit_total: 2000 // المبيعات (2000) - المشتريات (0) = 2000
        });
        
        // التحقق من صحة الأرباح
        if (firstSaleResult.cashbox.profit_total === 2000) {
          console.log('✅ إجمالي الأرباح صحيح بعد البيع الأولى');
        } else {
          console.error(`❌ خطأ في إجمالي الأرباح! المتوقع: 2000، الفعلي: ${firstSaleResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية البيع الأولى:', firstSaleResult.error);
        return;
      }
      
      // انتظار قصير للتأكد من تحديث الواجهة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 3. عملية شراء أولى (يجب أن تحافظ على دقة عرض الأرباح)
      console.log('\n3️⃣ عملية شراء أولى (مبلغ 800)...');
      const beforeFirstPurchase = await window.api.cashbox.getCashbox();
      console.log(`📊 إجمالي الأرباح قبل الشراء الأول: ${beforeFirstPurchase.profit_total}`);
      
      const firstPurchaseResult = await window.api.cashbox.addTransaction({
        type: 'purchase',
        amount: 800,
        source: 'test',
        notes: 'شراء أول لاختبار الخلل البصري',
        user_id: 1
      });
      
      if (firstPurchaseResult.success) {
        console.log('✅ تم تسجيل عملية الشراء الأولى');
        console.log('📊 الخزينة بعد الشراء الأولى:', {
          current_balance: firstPurchaseResult.cashbox.current_balance,
          purchases_total: firstPurchaseResult.cashbox.purchases_total,
          profit_total: firstPurchaseResult.cashbox.profit_total,
          expected_profit_total: 2000 - 800 // المبيعات (2000) - المشتريات (800) = 1200
        });
        
        // الاختبار الأهم: التحقق من أن الأرباح تعرض بشكل صحيح في الواجهة
        const expectedProfitAfterFirstPurchase = 1200;
        if (firstPurchaseResult.cashbox.profit_total === expectedProfitAfterFirstPurchase) {
          console.log('✅ إجمالي الأرباح يعرض بشكل صحيح بعد الشراء الأول (لا يوجد خلل بصري)');
        } else {
          console.error(`❌ خلل بصري في عرض الأرباح! المتوقع: ${expectedProfitAfterFirstPurchase}، الفعلي: ${firstPurchaseResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية الشراء الأولى:', firstPurchaseResult.error);
        return;
      }
      
      // انتظار قصير للتأكد من تحديث الواجهة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 4. عملية شراء ثانية متتالية (هنا كان يحدث الخلل البصري)
      console.log('\n4️⃣ عملية شراء ثانية متتالية (مبلغ 500) - اختبار الخلل البصري...');
      const beforeSecondPurchase = await window.api.cashbox.getCashbox();
      console.log(`📊 إجمالي الأرباح قبل الشراء الثاني: ${beforeSecondPurchase.profit_total}`);
      
      const secondPurchaseResult = await window.api.cashbox.addTransaction({
        type: 'purchase',
        amount: 500,
        source: 'test',
        notes: 'شراء ثاني لاختبار الخلل البصري',
        user_id: 1
      });
      
      if (secondPurchaseResult.success) {
        console.log('✅ تم تسجيل عملية الشراء الثانية');
        console.log('📊 الخزينة بعد الشراء الثانية:', {
          current_balance: secondPurchaseResult.cashbox.current_balance,
          purchases_total: secondPurchaseResult.cashbox.purchases_total,
          profit_total: secondPurchaseResult.cashbox.profit_total,
          expected_profit_total: 2000 - 800 - 500 // المبيعات (2000) - المشتريات (1300) = 700
        });
        
        // الاختبار الحاسم: التحقق من أن الخلل البصري تم إصلاحه
        const expectedProfitAfterSecondPurchase = 700;
        if (secondPurchaseResult.cashbox.profit_total === expectedProfitAfterSecondPurchase) {
          console.log('✅ تم إصلاح الخلل البصري! الأرباح تعرض بشكل صحيح بعد الشراء الثاني');
        } else {
          console.error(`❌ الخلل البصري لا يزال موجود! المتوقع: ${expectedProfitAfterSecondPurchase}، الفعلي: ${secondPurchaseResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية الشراء الثانية:', secondPurchaseResult.error);
        return;
      }
      
      // انتظار قصير للتأكد من تحديث الواجهة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 5. عملية شراء ثالثة للتأكد من الاستقرار
      console.log('\n5️⃣ عملية شراء ثالثة للتأكد من الاستقرار (مبلغ 300)...');
      const beforeThirdPurchase = await window.api.cashbox.getCashbox();
      console.log(`📊 إجمالي الأرباح قبل الشراء الثالث: ${beforeThirdPurchase.profit_total}`);
      
      const thirdPurchaseResult = await window.api.cashbox.addTransaction({
        type: 'purchase',
        amount: 300,
        source: 'test',
        notes: 'شراء ثالث للتأكد من الاستقرار',
        user_id: 1
      });
      
      if (thirdPurchaseResult.success) {
        console.log('✅ تم تسجيل عملية الشراء الثالثة');
        console.log('📊 الخزينة بعد الشراء الثالثة:', {
          current_balance: thirdPurchaseResult.cashbox.current_balance,
          purchases_total: thirdPurchaseResult.cashbox.purchases_total,
          profit_total: thirdPurchaseResult.cashbox.profit_total,
          expected_profit_total: 2000 - 800 - 500 - 300 // المبيعات (2000) - المشتريات (1600) = 400
        });
        
        // التحقق من الاستقرار
        const expectedProfitAfterThirdPurchase = 400;
        if (thirdPurchaseResult.cashbox.profit_total === expectedProfitAfterThirdPurchase) {
          console.log('✅ النظام مستقر! الأرباح تعرض بشكل صحيح بعد عمليات الشراء المتتالية');
        } else {
          console.error(`❌ عدم استقرار في النظام! المتوقع: ${expectedProfitAfterThirdPurchase}، الفعلي: ${thirdPurchaseResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية الشراء الثالثة:', thirdPurchaseResult.error);
        return;
      }
      
      // انتظار قصير للتأكد من تحديث الواجهة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 6. عملية بيع للتأكد من أن النظام يعمل بشكل طبيعي
      console.log('\n6️⃣ عملية بيع للتأكد من أن النظام يعمل بشكل طبيعي (مبلغ 1500)...');
      const beforeFinalSale = await window.api.cashbox.getCashbox();
      console.log(`📊 إجمالي الأرباح قبل البيع النهائي: ${beforeFinalSale.profit_total}`);
      
      const finalSaleResult = await window.api.cashbox.addTransaction({
        type: 'sale',
        amount: 1500,
        source: 'test',
        notes: 'بيع نهائي للتأكد من عمل النظام',
        user_id: 1
      });
      
      if (finalSaleResult.success) {
        console.log('✅ تم تسجيل عملية البيع النهائية');
        console.log('📊 الخزينة النهائية:', {
          current_balance: finalSaleResult.cashbox.current_balance,
          sales_total: finalSaleResult.cashbox.sales_total,
          purchases_total: finalSaleResult.cashbox.purchases_total,
          profit_total: finalSaleResult.cashbox.profit_total,
          expected_profit_total: (2000 + 1500) - 1600 // المبيعات (3500) - المشتريات (1600) = 1900
        });
        
        // التحقق النهائي
        const expectedFinalProfit = 1900;
        if (finalSaleResult.cashbox.profit_total === expectedFinalProfit) {
          console.log('✅ النظام يعمل بشكل طبيعي بعد الإصلاح');
        } else {
          console.error(`❌ مشكلة في النظام! المتوقع: ${expectedFinalProfit}، الفعلي: ${finalSaleResult.cashbox.profit_total}`);
        }
      } else {
        console.error('❌ فشل في تسجيل عملية البيع النهائية:', finalSaleResult.error);
        return;
      }
      
      // 7. التحقق من الحالة النهائية
      console.log('\n7️⃣ التحقق من الحالة النهائية للنظام...');
      const finalCashbox = await window.api.cashbox.getCashbox();
      console.log('📊 الحالة النهائية للخزينة:', finalCashbox);
      
      // التحقق من صحة جميع الحسابات النهائية
      const finalExpectedValues = {
        initial_balance: 8000,
        current_balance: 8000, // يجب أن يبقى مساوياً للرصيد الافتتاحي
        sales_total: 3500, // 2000 + 1500
        purchases_total: 1600, // 800 + 500 + 300
        profit_total: 1900 // 3500 - 1600
      };
      
      console.log('\n📋 مقارنة النتائج النهائية:');
      let allTestsPassed = true;
      
      for (const [key, expectedValue] of Object.entries(finalExpectedValues)) {
        const actualValue = finalCashbox[key];
        const isCorrect = actualValue === expectedValue;
        console.log(`${key}: متوقع ${expectedValue}, فعلي ${actualValue} ${isCorrect ? '✅' : '❌'}`);
        if (!isCorrect) allTestsPassed = false;
      }
      
      // النتيجة النهائية
      if (allTestsPassed) {
        console.log('\n🎉 جميع الاختبارات نجحت! تم إصلاح الخلل البصري في عرض إجمالي الأرباح');
        console.log('✅ الواجهة تعرض القيم الصحيحة فوراً أثناء عمليات الشراء المتتالية');
        console.log('✅ لا حاجة لانتظار عملية بيع لتصحيح العرض');
        console.log('✅ البيانات في قاعدة البيانات والواجهة متطابقة');
      } else {
        console.log('\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الإصلاحات');
      }
      
    } catch (error) {
      console.error('❌ خطأ في تشغيل اختبار الخلل البصري:', error);
    }
  }
  
  // تشغيل الاختبار
  testUIProfitDisplayFix();
  
} else {
  console.error('❌ window.api غير متوفر');
  console.log('💡 يجب تشغيل هذا السكريبت من داخل التطبيق');
  console.log('');
  console.log('📋 لتشغيل الاختبار:');
  console.log('1. افتح التطبيق');
  console.log('2. افتح وحدة التحكم (F12)');
  console.log('3. انسخ والصق محتوى هذا الملف');
}
