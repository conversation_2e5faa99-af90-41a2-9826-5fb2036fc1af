#!/usr/bin/env node

/**
 * اختبار وظيفة إلغاء فواتير الشراء
 * هذا الملف يختبر الإصلاحات المطبقة على مشكلة FOREIGN KEY constraint failed
 */

const Database = require('better-sqlite3');
const path = require('path');

// مسار قاعدة البيانات
const dbPath = path.join(__dirname, 'wms.db');

console.log('🧪 بدء اختبار وظيفة إلغاء فواتير الشراء...');
console.log('📁 مسار قاعدة البيانات:', dbPath);

try {
  // فتح قاعدة البيانات
  const db = new Database(dbPath);
  
  console.log('\n✅ تم فتح قاعدة البيانات بنجاح');
  
  // فحص الجداول المطلوبة
  console.log('\n🔍 فحص الجداول المطلوبة...');
  
  const tables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name IN ('transactions', 'transaction_audit_log', 'transaction_cancellations', 'users', 'inventory', 'cashbox')
  `).all();
  
  console.log('📋 الجداول الموجودة:');
  tables.forEach(table => {
    console.log(`   ✅ ${table.name}`);
  });
  
  // فحص بنية جدول transactions
  console.log('\n🔍 فحص بنية جدول transactions...');
  const transactionColumns = db.prepare("PRAGMA table_info(transactions)").all();
  
  const requiredColumns = ['status', 'cancelled_at', 'cancelled_by', 'cancellation_reason'];
  const existingColumns = transactionColumns.map(col => col.name);
  
  console.log('📊 الأعمدة المطلوبة للإلغاء:');
  requiredColumns.forEach(col => {
    const exists = existingColumns.includes(col);
    console.log(`   ${exists ? '✅' : '❌'} ${col}`);
  });
  
  // فحص وجود معاملات للاختبار
  console.log('\n🔍 فحص المعاملات المتاحة للاختبار...');
  const activeTransactions = db.prepare(`
    SELECT id, item_id, quantity, price, total_price, invoice_number, transaction_date
    FROM transactions 
    WHERE transaction_type = 'purchase' 
    AND (status IS NULL OR status = 'active')
    LIMIT 5
  `).all();
  
  if (activeTransactions.length > 0) {
    console.log(`📈 تم العثور على ${activeTransactions.length} معاملة شراء نشطة:`);
    activeTransactions.forEach((trans, index) => {
      console.log(`   ${index + 1}. ID: ${trans.id}, الفاتورة: ${trans.invoice_number || 'غير محدد'}, المبلغ: ${trans.total_price}`);
    });
  } else {
    console.log('⚠️ لا توجد معاملات شراء نشطة للاختبار');
  }
  
  // فحص المستخدمين
  console.log('\n🔍 فحص المستخدمين...');
  const users = db.prepare("SELECT id, username, role FROM users LIMIT 3").all();
  
  if (users.length > 0) {
    console.log(`👥 تم العثور على ${users.length} مستخدم:`);
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ID: ${user.id}, الاسم: ${user.username}, الدور: ${user.role}`);
    });
  } else {
    console.log('⚠️ لا توجد مستخدمين في النظام');
  }
  
  // فحص حالة قيود المفاتيح الخارجية
  console.log('\n🔍 فحص حالة قيود المفاتيح الخارجية...');
  const foreignKeysStatus = db.pragma('foreign_keys');
  console.log(`🔗 حالة قيود المفاتيح الخارجية: ${foreignKeysStatus ? 'مفعلة' : 'معطلة'}`);
  
  // اختبار تعطيل وتفعيل قيود المفاتيح الخارجية
  console.log('\n🧪 اختبار تعطيل وتفعيل قيود المفاتيح الخارجية...');
  
  db.pragma('foreign_keys = OFF');
  const disabledStatus = db.pragma('foreign_keys');
  console.log(`   تعطيل: ${disabledStatus ? 'فشل' : 'نجح'}`);
  
  db.pragma('foreign_keys = ON');
  const enabledStatus = db.pragma('foreign_keys');
  console.log(`   تفعيل: ${enabledStatus ? 'نجح' : 'فشل'}`);
  
  // إغلاق قاعدة البيانات
  db.close();
  
  console.log('\n🎉 تم إنهاء الاختبار بنجاح!');
  console.log('\n📋 ملخص النتائج:');
  console.log('   ✅ قاعدة البيانات متاحة ويمكن الوصول إليها');
  console.log('   ✅ الجداول المطلوبة موجودة');
  console.log('   ✅ يمكن تعطيل وتفعيل قيود المفاتيح الخارجية');
  
  if (activeTransactions.length > 0 && users.length > 0) {
    console.log('   ✅ البيانات متوفرة لاختبار وظيفة الإلغاء');
    console.log('\n💡 يمكنك الآن اختبار إلغاء فاتورة الشراء من واجهة التطبيق');
  } else {
    console.log('   ⚠️ تحتاج لإضافة بيانات (معاملات ومستخدمين) لاختبار الوظيفة');
  }
  
} catch (error) {
  console.error('❌ خطأ في الاختبار:', error.message);
  console.error('📋 تفاصيل الخطأ:', error.stack);
  process.exit(1);
}
