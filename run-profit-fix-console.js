/**
 * سكريبت سريع لإصلاح حساب الأرباح
 * 
 * يمكن تشغيله من وحدة التحكم في المتصفح
 * انسخ والصق هذا الكود في وحدة التحكم واضغط Enter
 */

async function runProfitFix() {
    console.log('🔧 بدء إصلاح حساب الأرباح...');
    
    try {
        // 1. فحص الحالة الحالية
        console.log('📊 فحص الحالة الحالية...');
        const cashboxBefore = await window.api.cashbox.get();
        
        if (!cashboxBefore || !cashboxBefore.exists) {
            console.error('❌ لا توجد خزينة في النظام');
            return false;
        }

        const profitBefore = cashboxBefore.profit_total || 0;
        const calculatedBefore = (cashboxBefore.sales_total || 0) - (cashboxBefore.purchases_total || 0) - (cashboxBefore.transport_total || 0);
        const differenceBefore = Math.abs(profitBefore - calculatedBefore);
        
        console.log('📈 القيم قبل الإصلاح:');
        console.log(`   - إجمالي المبيعات: ${cashboxBefore.sales_total || 0} د.ل`);
        console.log(`   - إجمالي المشتريات: ${cashboxBefore.purchases_total || 0} د.ل`);
        console.log(`   - إجمالي مصاريف النقل: ${cashboxBefore.transport_total || 0} د.ل`);
        console.log(`   - الأرباح المحفوظة: ${profitBefore} د.ل`);
        console.log(`   - الأرباح المحسوبة: ${calculatedBefore} د.ل`);
        console.log(`   - الفرق: ${differenceBefore.toFixed(2)} د.ل`);

        if (differenceBefore <= 0.01) {
            console.log('✅ قيم الأرباح متطابقة بالفعل، لا حاجة للإصلاح');
            return true;
        }

        // 2. تشغيل الإصلاح
        console.log('⚙️ تشغيل دالة إصلاح الأرباح...');
        const fixResult = await window.api.cashbox.fixProfitCalculation();
        
        if (!fixResult.success) {
            console.error('❌ فشل في تشغيل إصلاح الأرباح:', fixResult.error);
            return false;
        }

        console.log('✅ تم تشغيل دالة الإصلاح بنجاح');

        // 3. فحص النتائج
        console.log('🔍 فحص النتائج...');
        
        // انتظار قليل للتأكد من تحديث البيانات
        await new Promise(resolve => setTimeout(resolve, 1000));

        const cashboxAfter = await window.api.cashbox.get();
        
        if (!cashboxAfter || !cashboxAfter.exists) {
            console.error('❌ فشل في الحصول على بيانات الخزينة بعد الإصلاح');
            return false;
        }

        const profitAfter = cashboxAfter.profit_total || 0;
        const calculatedAfter = (cashboxAfter.sales_total || 0) - (cashboxAfter.purchases_total || 0) - (cashboxAfter.transport_total || 0);
        const differenceAfter = Math.abs(profitAfter - calculatedAfter);

        console.log('📊 النتائج بعد الإصلاح:');
        console.log(`   - الأرباح المحفوظة: ${profitAfter} د.ل`);
        console.log(`   - الأرباح المحسوبة: ${calculatedAfter} د.ل`);
        console.log(`   - الفرق: ${differenceAfter.toFixed(2)} د.ل`);

        // 4. تقييم النتائج
        if (differenceAfter <= 0.01) {
            console.log('🎉 تم توحيد قيم الأرباح بنجاح!');
            console.log(`✅ جميع القيم أصبحت متطابقة: ${profitAfter} د.ل`);
            
            // تحديث الواجهة
            if (window.loadCashbox && typeof window.loadCashbox === 'function') {
                console.log('🔄 تحديث واجهة الخزينة...');
                window.loadCashbox();
            }
            
            // إرسال حدث تحديث
            if (window.dispatchEvent) {
                window.dispatchEvent(new CustomEvent('cashbox-updated-ui', {
                    detail: cashboxAfter
                }));
            }
            
            // فرض إعادة تحميل الصفحة للتأكد من التحديث
            setTimeout(() => {
                console.log('🔄 إعادة تحميل الصفحة للتأكد من التحديث...');
                window.location.reload();
            }, 2000);
            
            return true;
        } else {
            console.warn(`⚠️ لا يزال هناك فرق طفيف: ${differenceAfter.toFixed(2)} د.ل`);
            console.warn('قد تحتاج لإعادة تشغيل الإصلاح أو فحص البيانات يدوياً');
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في إصلاح الأرباح:', error);
        return false;
    }
}

// تشغيل الإصلاح فوراً
console.log('🚀 تم تحميل سكريبت إصلاح الأرباح');
console.log('⏳ سيتم تشغيل الإصلاح خلال 3 ثواني...');
console.log('💡 لإلغاء التشغيل التلقائي، اكتب: clearTimeout(autoFixTimer)');

const autoFixTimer = setTimeout(() => {
    runProfitFix();
}, 3000);

// إتاحة تشغيل الإصلاح يدوياً
window.runProfitFix = runProfitFix;
