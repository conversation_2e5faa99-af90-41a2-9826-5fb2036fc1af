# دليل اختبار ميزة إلغاء فواتير الشراء

## الخطوة 1: تحضير قاعدة البيانات

### 1.1 تشغيل ترقية قاعدة البيانات
```bash
# إذا كان Node.js متوفر في PATH
node simple-db-migration.js

# أو باستخدام npm
npm run migrate-cancellation

# أو يدوياً عبر SQLite
```

### 1.2 التحقق من إضافة الحقول الجديدة
افتح قاعدة البيانات وتحقق من وجود:
- حقل `status` في جدول `transactions`
- حقل `cancelled_at` في جدول `transactions`
- حقل `cancelled_by` في جدول `transactions`
- حقل `cancellation_reason` في جدول `transactions`
- جدول `transaction_audit_log`

## الخطوة 2: اختبار الواجهة الأمامية

### 2.1 تسجيل الدخول
1. افتح التطبيق
2. سجل الدخول كمستخدم عادي أو مدير
3. انتقل إلى صفحة "المشتريات"

### 2.2 إنشاء فاتورة شراء جديدة
1. اضغط على "إضافة عملية شراء"
2. اختر صنف موجود
3. أدخل الكمية والسعر
4. احفظ الفاتورة
5. تحقق من ظهورها في قائمة المشتريات

### 2.3 اختبار فلتر الحالة
1. ابحث عن قائمة منسدلة "فلتر الحالة" في أعلى جدول المشتريات
2. جرب التبديل بين:
   - "جميع الفواتير"
   - "الفواتير النشطة"
   - "الفواتير الملغاة"

### 2.4 اختبار عمود الحالة
1. تحقق من وجود عمود "الحالة" في جدول المشتريات
2. يجب أن تظهر جميع الفواتير الموجودة بحالة "نشطة"
3. يجب أن تكون الحالة ملونة (أخضر للنشطة)

### 2.5 اختبار عمود الإجراءات
1. تحقق من وجود عمود "الإجراءات" في جدول المشتريات
2. يجب أن يحتوي على زر "إلغاء" للفواتير النشطة
3. يجب أن يكون الزر أحمر اللون مع أيقونة X

## الخطوة 3: اختبار عملية الإلغاء

### 3.1 اختبار حوار التأكيد
1. اضغط على زر "إلغاء" لإحدى فواتير الشراء
2. يجب أن يظهر حوار تأكيد يحتوي على:
   - رمز تحذير
   - تفاصيل الفاتورة (رقم الفاتورة، الصنف، الكمية، المبلغ)
   - تحذيرات حول تأثير الإلغاء
   - حقل إدخال سبب الإلغاء (مطلوب)
   - زر "تأكيد الإلغاء" وزر "إلغاء"

### 3.2 اختبار التحقق من صحة البيانات
1. حاول تأكيد الإلغاء بدون إدخال سبب
   - يجب أن يظهر خطأ "يرجى إدخال سبب الإلغاء"
2. أدخل سبب قصير (أقل من 5 أحرف)
   - يجب أن يظهر خطأ "سبب الإلغاء يجب أن يكون 5 أحرف على الأقل"
3. أدخل سبب صالح (5 أحرف أو أكثر)
   - يجب أن يصبح زر "تأكيد الإلغاء" نشطاً

### 3.3 اختبار عملية الإلغاء الفعلية
1. أدخل سبب إلغاء صالح (مثل: "خطأ في الطلب")
2. اضغط على "تأكيد الإلغاء"
3. يجب أن يظهر مؤشر تحميل
4. يجب أن تظهر رسالة نجاح "تم إلغاء فاتورة الشراء بنجاح"
5. يجب أن يُغلق حوار التأكيد تلقائياً

### 3.4 التحقق من تحديث الواجهة
1. تحقق من تحديث حالة الفاتورة إلى "ملغاة" (أحمر)
2. تحقق من اختفاء زر "إلغاء" وظهور زر "تفاصيل"
3. اضغط على زر "تفاصيل" للتحقق من معلومات الإلغاء

## الخطوة 4: اختبار قواعد العمل

### 4.1 اختبار الصلاحيات
1. **كمستخدم عادي:**
   - يجب أن تتمكن من إلغاء فواتيرك فقط
   - يجب أن تتمكن من إلغاء الفواتير خلال 30 يوم فقط
2. **كمدير:**
   - يجب أن تتمكن من إلغاء أي فاتورة
   - لا يوجد قيود زمنية

### 4.2 اختبار منع الإلغاء المتكرر
1. حاول إلغاء فاتورة ملغاة بالفعل
2. يجب ألا يظهر زر "إلغاء" للفواتير الملغاة

### 4.3 اختبار تأثير الإلغاء على المخزون
1. تحقق من كمية الصنف في المخزون قبل الإلغاء
2. ألغِ فاتورة شراء
3. تحقق من انخفاض كمية الصنف في المخزون بنفس كمية الشراء الملغاة

### 4.4 اختبار تأثير الإلغاء على الخزينة
1. تحقق من رصيد الخزينة قبل الإلغاء
2. ألغِ فاتورة شراء
3. تحقق من زيادة رصيد الخزينة بمبلغ الفاتورة الملغاة

## الخطوة 5: اختبار سجل التدقيق

### 5.1 التحقق من تسجيل الإلغاء
1. افتح قاعدة البيانات
2. تحقق من جدول `transaction_audit_log`
3. يجب أن يحتوي على سجل للإلغاء مع:
   - معرف المعاملة
   - نوع الإجراء: 'cancel'
   - الحالة القديمة: 'active'
   - الحالة الجديدة: 'cancelled'
   - سبب الإلغاء
   - معرف المستخدم الذي قام بالإلغاء
   - تاريخ ووقت الإلغاء

## الخطوة 6: اختبار الحالات الاستثنائية

### 6.1 اختبار عدم توفر كمية كافية
1. أنشئ فاتورة شراء بكمية 100
2. بع 50 قطعة من نفس الصنف
3. حاول إلغاء فاتورة الشراء
4. يجب أن تنجح العملية لأن الكمية المتبقية (50) كافية

### 6.2 اختبار وجود مبيعات مرتبطة
1. أنشئ فاتورة شراء
2. بع من نفس الصنف بعد تاريخ الشراء
3. حاول إلغاء فاتورة الشراء
4. يجب أن تفشل العملية مع رسالة خطأ مناسبة

## النتائج المتوقعة

✅ **نجح الاختبار إذا:**
- تم إضافة الحقول الجديدة لقاعدة البيانات
- ظهرت أعمدة الحالة والإجراءات في جدول المشتريات
- عمل حوار تأكيد الإلغاء بشكل صحيح
- تم تحديث حالة الفاتورة بعد الإلغاء
- تم عكس تأثير الفاتورة على المخزون والخزينة
- تم تسجيل الإلغاء في سجل التدقيق
- عملت قواعد العمل والصلاحيات بشكل صحيح

❌ **فشل الاختبار إذا:**
- لم تظهر الأعمدة الجديدة
- لم يعمل حوار الإلغاء
- لم تتحدث حالة الفاتورة
- لم يتم عكس التأثير على المخزون/الخزينة
- ظهرت أخطاء في وحدة التحكم

## استكشاف الأخطاء

### إذا لم تظهر الأعمدة الجديدة:
1. تحقق من تشغيل ترقية قاعدة البيانات
2. تحقق من إعادة تشغيل التطبيق
3. تحقق من وحدة التحكم للأخطاء

### إذا لم يعمل الإلغاء:
1. افتح أدوات المطور (F12)
2. تحقق من وحدة التحكم للأخطاء
3. تحقق من تسجيل الدخول والصلاحيات
4. تحقق من اتصال قاعدة البيانات
