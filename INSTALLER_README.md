# 🏗️ دليل إنشاء الملف التنفيذي لنظام إدارة المخازن

## 📋 نظرة عامة

هذا الدليل يوضح كيفية إنشاء ملف تنفيذي (.exe) قابل للتثبيت من تطبيق إدارة المستودعات لتوزيعه على أجهزة العملاء.

## ✨ الميزات المتضمنة

- ✅ **مثبت NSIS تفاعلي** مع واجهة مستخدم باللغة العربية
- ✅ **نسخة محمولة** لا تحتاج تثبيت
- ✅ **إعداد تلقائي** لقاعدة البيانات SQLite
- ✅ **اختصارات تلقائية** على سطح المكتب وقائمة البرامج
- ✅ **نسخ احتياطية** تلقائية لقاعدة البيانات
- ✅ **واجهة إلغاء تثبيت** مع خيار حفظ البيانات

## 🚀 البدء السريع

### 1. التحقق من الجاهزية
```bash
npm run test-build
```

### 2. إنشاء الملف التنفيذي
```bash
npm run build-installer
```

### 3. العثور على الملفات
ستجد الملفات في مجلد `dist/`:
- `نظام-إدارة-المخازن-1.1.1-installer.exe` - المثبت الرئيسي
- `نظام-إدارة-المخازن-1.1.1-portable.exe` - النسخة المحمولة

## 📁 بنية الملفات

```
📦 مشروع نظام إدارة المخازن
├── 📁 build/                    # ملفات إعدادات البناء
│   ├── installer.nsh            # إعدادات NSIS المخصصة
│   └── license.txt              # اتفاقية الترخيص
├── 📁 assets/icons/             # أيقونات التطبيق
├── 📁 wms-database/             # قاعدة البيانات الافتراضية
├── 📄 build-installer.js        # سكريبت البناء الشامل
├── 📄 test-build-readiness.js   # اختبار جاهزية البناء
├── 📄 package.json              # إعدادات electron-builder
└── 📄 webpack.config.js         # إعدادات بناء الواجهة
```

## ⚙️ إعدادات التخصيص

### تغيير معلومات التطبيق
عدّل في `package.json`:
```json
{
  "name": "warehouse-management-system",
  "version": "1.1.1",
  "description": "نظام إدارة المخازن",
  "build": {
    "productName": "نظام إدارة المخازن",
    "copyright": "Copyright © 2025 H Group"
  }
}
```

### تخصيص المثبت
عدّل في `package.json` → `build.nsis`:
```json
{
  "shortcutName": "نظام إدارة المخازن",
  "menuCategory": "H Group",
  "artifactName": "نظام-إدارة-المخازن-${version}-installer.${ext}"
}
```

### تغيير الأيقونة
1. ضع ملف `.ico` في `assets/icons/`
2. حدّث المسار في `package.json`:
```json
{
  "build": {
    "win": {
      "icon": "assets/icons/your-icon.ico"
    }
  }
}
```

## 🔧 أوامر البناء المتاحة

| الأمر | الوصف |
|-------|--------|
| `npm run test-build` | اختبار جاهزية البناء |
| `npm run build-installer` | بناء شامل (مُوصى به) |
| `npm run clean` | تنظيف ملفات البناء السابقة |
| `npm run dist:win` | بناء للـ Windows فقط |
| `npm run portable` | إنشاء نسخة محمولة فقط |
| `npm run pack` | إنشاء مجلد التطبيق بدون مثبت |

## 📊 متطلبات النظام

### للبناء (المطور):
- **Node.js**: 18.0.0 أو أحدث
- **npm**: 8.0.0 أو أحدث
- **Windows**: 10/11 (64-bit)
- **مساحة القرص**: 2 GB فارغة
- **الذاكرة**: 8 GB RAM (مُوصى به)

### للتشغيل (العميل):
- **Windows**: 10/11 (64-bit)
- **مساحة القرص**: 500 MB
- **الذاكرة**: 4 GB RAM

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:

#### ❌ "node-gyp rebuild failed"
```bash
# الحل:
npm install --global windows-build-tools
npm run postinstall
```

#### ❌ "electron-builder not found"
```bash
# الحل:
npm install --save-dev electron-builder@latest
```

#### ❌ مشاكل better-sqlite3
```bash
# الحل:
npx electron-rebuild
```

#### ❌ مشاكل الذاكرة
```bash
# الحل:
set NODE_OPTIONS=--max-old-space-size=4096
npm run build-installer
```

### فحص السجلات:
- سجلات البناء تظهر في وحدة التحكم
- ملفات مفصلة في `dist/builder-debug.yml`

## 📦 الملفات الناتجة

### مثبت NSIS (للتوزيع العام):
- **الاسم**: `نظام-إدارة-المخازن-1.1.1-installer.exe`
- **الحجم**: ~150-200 MB
- **الميزات**: واجهة تثبيت، اختصارات تلقائية، إلغاء تثبيت

### نسخة محمولة (للاستخدام المؤقت):
- **الاسم**: `نظام-إدارة-المخازن-1.1.1-portable.exe`
- **الحجم**: ~150-200 MB
- **الميزات**: تشغيل مباشر، لا يحتاج تثبيت

## 🚀 خطوات التوزيع

### للعملاء العاديين:
1. أرسل ملف `*-installer.exe`
2. اطلب من العميل تشغيله كمدير
3. اتباع خطوات المثبت
4. التشغيل من اختصار سطح المكتب

### للاستخدام المحمول:
1. أرسل ملف `*-portable.exe`
2. وضعه في مجلد منفصل
3. تشغيل مباشر بنقرة مزدوجة

## 🔒 الأمان والتوقيع

### لتجنب تحذيرات Windows:
1. احصل على شهادة رقمية
2. أضف إعدادات التوقيع:
```json
{
  "build": {
    "win": {
      "certificateFile": "certificate.p12",
      "certificatePassword": "password"
    }
  }
}
```

## 📈 التحديثات

### لإصدار جديد:
1. زيادة `version` في `package.json`
2. تشغيل `npm run build-installer`
3. توزيع الملف الجديد

### للعملاء:
- التثبيت فوق الإصدار القديم
- البيانات محفوظة تلقائياً
- يُنصح بنسخة احتياطية

## 📞 الدعم

### معلومات مفيدة:
- رقم الإصدار من `package.json`
- سجلات الأخطاء من وحدة التحكم
- ملفات السجل في `dist/builder-debug.yml`

### للمساعدة:
- راجع `BUILD_INSTRUCTIONS.md` للتفاصيل
- راجع `DEPLOYMENT_GUIDE.md` للتوزيع
- تواصل مع فريق التطوير

---

**🎉 مبروك! أصبح لديك الآن نظام كامل لإنشاء وتوزيع تطبيق إدارة المخازن.**
