/**
 * سكريبت بناء شامل لإنشاء ملف تنفيذي قابل للتثبيت
 * يقوم بتنظيف المشروع، بناء الواجهة الأمامية، وإنشاء المثبت
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء عملية بناء نظام إدارة المخازن...\n');

// دالة لتنفيذ الأوامر مع عرض النتائج
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const result = execSync(command, { 
      stdio: 'inherit', 
      encoding: 'utf8',
      cwd: process.cwd()
    });
    console.log(`✅ ${description} - تم بنجاح\n`);
    return true;
  } catch (error) {
    console.error(`❌ فشل في ${description}:`);
    console.error(error.message);
    return false;
  }
}

// دالة للتحقق من وجود الملفات المطلوبة
function checkRequiredFiles() {
  console.log('🔍 التحقق من الملفات المطلوبة...');
  
  const requiredFiles = [
    'package.json',
    'main.js',
    'preload.js',
    'index.html',
    'src/index.js',
    'assets/icons/share_ico_socialnetwork_16174.ico'
  ];
  
  const missingFiles = [];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      missingFiles.push(file);
    }
  }
  
  if (missingFiles.length > 0) {
    console.error('❌ الملفات التالية مفقودة:');
    missingFiles.forEach(file => console.error(`   - ${file}`));
    return false;
  }
  
  console.log('✅ جميع الملفات المطلوبة موجودة\n');
  return true;
}

// دالة لإنشاء مجلدات البناء
function createBuildDirectories() {
  console.log('📁 إنشاء مجلدات البناء...');
  
  const directories = ['build', 'dist'];
  
  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`   ✅ تم إنشاء مجلد: ${dir}`);
    }
  });
  
  console.log('✅ مجلدات البناء جاهزة\n');
}

// دالة لتنظيف ملفات البناء السابقة
function cleanPreviousBuilds() {
  console.log('🧹 تنظيف ملفات البناء السابقة...');
  
  try {
    // حذف مجلد dist إذا كان موجوداً
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true });
      console.log('   ✅ تم حذف مجلد dist السابق');
    }
    
    // حذف bundle.js السابق إذا كان موجوداً
    if (fs.existsSync('bundle.js')) {
      fs.unlinkSync('bundle.js');
      console.log('   ✅ تم حذف bundle.js السابق');
    }
    
    if (fs.existsSync('bundle.js.map')) {
      fs.unlinkSync('bundle.js.map');
      console.log('   ✅ تم حذف bundle.js.map السابق');
    }
    
    console.log('✅ تم تنظيف ملفات البناء السابقة\n');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تنظيف ملفات البناء السابقة:', error.message);
    return false;
  }
}

// دالة للتحقق من صحة package.json
function validatePackageJson() {
  console.log('📋 التحقق من صحة package.json...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // التحقق من الحقول المطلوبة
    const requiredFields = ['name', 'version', 'main', 'build'];
    const missingFields = requiredFields.filter(field => !packageJson[field]);
    
    if (missingFields.length > 0) {
      console.error('❌ الحقول التالية مفقودة في package.json:');
      missingFields.forEach(field => console.error(`   - ${field}`));
      return false;
    }
    
    console.log(`   ✅ اسم التطبيق: ${packageJson.name}`);
    console.log(`   ✅ الإصدار: ${packageJson.version}`);
    console.log(`   ✅ الملف الرئيسي: ${packageJson.main}`);
    console.log('✅ package.json صحيح\n');
    return true;
  } catch (error) {
    console.error('❌ خطأ في قراءة package.json:', error.message);
    return false;
  }
}

// الدالة الرئيسية
async function main() {
  console.log('=' .repeat(60));
  console.log('🏗️  سكريبت بناء نظام إدارة المخازن');
  console.log('=' .repeat(60));
  console.log();
  
  // التحقق من الملفات المطلوبة
  if (!checkRequiredFiles()) {
    process.exit(1);
  }
  
  // التحقق من صحة package.json
  if (!validatePackageJson()) {
    process.exit(1);
  }
  
  // إنشاء مجلدات البناء
  createBuildDirectories();
  
  // تنظيف ملفات البناء السابقة
  if (!cleanPreviousBuilds()) {
    process.exit(1);
  }
  
  // تثبيت التبعيات
  if (!runCommand('npm install', 'تثبيت التبعيات')) {
    process.exit(1);
  }
  
  // بناء الواجهة الأمامية للإنتاج
  if (!runCommand('npx webpack --mode production', 'بناء الواجهة الأمامية')) {
    process.exit(1);
  }
  
  // التحقق من إنشاء bundle.js
  if (!fs.existsSync('bundle.js')) {
    console.error('❌ فشل في إنشاء bundle.js');
    process.exit(1);
  }
  
  console.log('✅ تم إنشاء bundle.js بنجاح');
  
  // بناء التطبيق وإنشاء المثبت
  if (!runCommand('npx electron-builder --win', 'إنشاء الملف التنفيذي والمثبت')) {
    process.exit(1);
  }
  
  // التحقق من نجاح البناء
  if (fs.existsSync('dist')) {
    console.log('\n🎉 تم بناء التطبيق بنجاح!');
    console.log('📁 ستجد الملفات التنفيذية في مجلد: dist/');
    
    // عرض الملفات المُنشأة
    const distFiles = fs.readdirSync('dist');
    console.log('\n📋 الملفات المُنشأة:');
    distFiles.forEach(file => {
      const filePath = path.join('dist', file);
      const stats = fs.statSync(filePath);
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
      console.log(`   📄 ${file} (${sizeInMB} MB)`);
    });
    
    console.log('\n✨ يمكنك الآن توزيع هذه الملفات على أجهزة العملاء!');
  } else {
    console.error('❌ فشل في إنشاء مجلد dist');
    process.exit(1);
  }
}

// تشغيل السكريبت
main().catch(error => {
  console.error('❌ خطأ غير متوقع:', error);
  process.exit(1);
});
