import React, { useState, useEffect, useRef } from 'react';
import {
  FaSearch,
  FaExclamationTriangle,
  FaBoxes,
  FaFilter,
  FaTimesCircle,
  FaSyncAlt,
  FaFileExport,
  FaFilePdf
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import Card from '../components/Card';
import Button from '../components/Button';
import DataTable from '../components/DataTable';
import FormattedCurrency from '../components/FormattedCurrency';
import Modal from '../components/Modal';
import { exportElementToPDF } from '../utils/pdfUtils';
import './Inventory.css';

const Inventory = () => {
  const { inventory, loading: contextLoading, setInventory, syncAllInventory, refreshInventory: contextRefreshInventory } = useApp();
  const [loading, setLoading] = useState(false); // إضافة حالة التحميل هنا
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredInventory, setFilteredInventory] = useState([]);
  const [stockFilter, setStockFilter] = useState('all');

  // مرجع للطباعة وتصدير PDF
  const inventoryTableRef = useRef(null);

  // Load inventory data when component mounts
  useEffect(() => {
    const loadInventory = async () => {
      try {
        console.log('جاري تحميل المخزون من Inventory.js');

        // بدلاً من مزامنة المخزون، نقوم فقط بتحميل البيانات الحالية
        // هذا يمنع إعادة حساب الكميات وتجاوز عمليات الإرجاع

        // استخدام window.api.invoke مباشرة للحصول على المخزون الحالي
        const currentInventory = await window.api.invoke('get-all-inventory');

        if (Array.isArray(currentInventory)) {
          console.log(`تم تحميل ${currentInventory.length} عنصر من المخزون بنجاح`);
        } else {
          console.warn('البيانات المستلمة ليست مصفوفة:', currentInventory);
        }
      } catch (err) {
        console.error('خطأ في تحميل المخزون:', err);
        setError('فشل في تحميل المخزون');
      }
    };

    loadInventory();
  }, []);

  // دالة لتحديث المخزون
  const refreshInventory = async (skipCache = true) => {
    try {
      console.log('تحديث المخزون يدويًا...');
      setLoading(true);

      // محاولة استخدام وظيفة تحديث المخزون من سياق التطبيق
      if (contextRefreshInventory) {
        console.log('استخدام وظيفة تحديث المخزون من سياق التطبيق');
        const success = await contextRefreshInventory();

        if (success) {
          console.log('تم تحديث المخزون بنجاح باستخدام سياق التطبيق');

          // إظهار رسالة نجاح
          if (window.api.notifications && window.api.notifications.show) {
            window.api.notifications.show('تم تحديث المخزون بنجاح', 'success', 3000);
          }

          setLoading(false);
          return;
        }
      }

      // إذا فشلت الطريقة الأولى، نستخدم الطريقة البديلة
      console.log('استخدام الطريقة البديلة لتحديث المخزون');

      // مسح التخزين المؤقت للمخزون
      await window.api.invoke('clear-inventory-cache');

      // الحصول على المخزون المحدث مباشرة من قاعدة البيانات
      const updatedInventory = await window.api.invoke('get-all-inventory', true);

      if (Array.isArray(updatedInventory)) {
        console.log(`تم تحديث المخزون بنجاح (${updatedInventory.length} صنف)`);

        // تحديث المخزون في السياق
        setInventory(updatedInventory);

        // إظهار رسالة نجاح
        if (window.api.notifications && window.api.notifications.show) {
          window.api.notifications.show('تم تحديث المخزون بنجاح', 'success', 3000);
        }
      } else {
        console.warn('البيانات المستلمة ليست مصفوفة:', updatedInventory);
        setError('البيانات المستلمة ليست بالتنسيق الصحيح');
      }
    } catch (err) {
      console.error('خطأ في تحديث المخزون:', err);
      setError('فشل في تحديث المخزون');

      // إظهار رسالة خطأ
      if (window.api.notifications && window.api.notifications.show) {
        window.api.notifications.show('فشل في تحديث المخزون', 'error', 3000);
      }
    } finally {
      setLoading(false);
    }
  };

  // إضافة مستمع لحدث تحديث المخزون
  useEffect(() => {
    // دالة لتحديث المخزون عند استلام حدث تحديث
    const handleInventoryUpdate = async (event) => {
      try {
        console.log('تم استلام حدث تحديث المخزون:', event.detail);

        // استدعاء دالة تحديث المخزون
        await refreshInventory(true);
      } catch (err) {
        console.error('خطأ في تحديث المخزون بعد استلام حدث التحديث:', err);
      }
    };

    // إضافة مستمع للحدث
    window.addEventListener('inventory-updated', handleInventoryUpdate);

    // إضافة مستمع لأحداث IPC
    if (window.api && window.api.on) {
      window.api.on('inventory-updated', (data) => {
        console.log('تم استلام حدث تحديث المخزون من IPC:', data);

        // إنشاء حدث مخصص لتحديث المخزون
        const updateEvent = new CustomEvent('inventory-updated', {
          detail: data
        });
        window.dispatchEvent(updateEvent);
      });
    }

    // تنظيف المستمعين عند إلغاء تحميل المكون
    return () => {
      window.removeEventListener('inventory-updated', handleInventoryUpdate);
      if (window.api && window.api.removeListener) {
        window.api.removeListener('inventory-updated', () => {
          console.log('تم إزالة مستمع حدث تحديث المخزون من IPC');
        });
      }
    };
  }, []);

  // Filter inventory based on search term and stock filter
  useEffect(() => {
    if (inventory && inventory.length > 0) {
      let filtered = [...inventory];

      // تطبيق فلتر المخزون
      if (stockFilter === 'low') {
        filtered = filtered.filter(item => item.current_quantity <= item.minimum_quantity && item.current_quantity > 0);
      } else if (stockFilter === 'out') {
        filtered = filtered.filter(item => item.current_quantity === 0);
      } else if (stockFilter === 'available') {
        filtered = filtered.filter(item => item.current_quantity > item.minimum_quantity);
      }

      // تطبيق فلتر البحث
      if (searchTerm) {
        filtered = filtered.filter(item =>
          item.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      setFilteredInventory(filtered);
    } else {
      setFilteredInventory([]);
    }
  }, [inventory, searchTerm, stockFilter]);

  // حساب إحصائيات المخزون
  const calculateStats = () => {
    if (!inventory || inventory.length === 0) return { total: 0, low: 0, out: 0 };

    const total = inventory.length;
    const low = inventory.filter(item => item.current_quantity <= item.minimum_quantity && item.current_quantity > 0).length;
    const out = inventory.filter(item => item.current_quantity === 0).length;

    return { total, low, out };
  };

  const stats = calculateStats();

  // دالة لتنسيق التاريخ (بالتنسيق الميلادي)
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${year}/${month}/${day}`;
  };

  // تم إزالة وظيفة مزامنة المخزون اليدوية



  // دالة لتصدير بيانات المخزون إلى PDF
  const exportInventoryToPDF = () => {
    try {
      if (!inventoryTableRef.current) {
        alert('لا يمكن تصدير البيانات حالياً');
        return;
      }

      // إنشاء اسم الملف
      const filename = `تقرير_المخزون_${new Date().toISOString().split('T')[0]}.pdf`;

      // تصدير العنصر إلى PDF
      exportElementToPDF(inventoryTableRef.current, filename, { orientation: 'landscape' });

      // إظهار رسالة نجاح
      if (window.api.notifications && window.api.notifications.show) {
        window.api.notifications.show('تم تصدير بيانات المخزون بنجاح', 'success', 3000);
      }
    } catch (error) {
      console.error('خطأ في تصدير بيانات المخزون:', error);
      alert('حدث خطأ أثناء تصدير بيانات المخزون');
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
        <div className="loading-text">
          جاري تحميل البيانات...
        </div>
      </div>
    );
  }

  return (
    <div className="inventory-page">
      <div className="inventory-header">
        <h1>المخزون</h1>
        <p>إدارة الأصناف والكميات المتوفرة</p>
      </div>

      {error && (
        <div className="alert alert-danger">
          {error}
        </div>
      )}

      {/* إحصائيات المخزون */}
      <Card
        title="إحصائيات المخزون"
        icon={<FaBoxes />}
        className="mb-4"
      >
        <div className="inventory-stats">
          <Card
            className="stat-card-wrapper"
            noPadding
            noShadow
          >
            <div className="stat-card">
              <div className="stat-card-icon" style={{ backgroundColor: 'rgba(26, 58, 95, 0.1)', color: 'var(--primary-color)' }}>
                <FaBoxes />
              </div>
              <div className="stat-card-value">{stats.total}</div>
              <div className="stat-card-title">إجمالي الأصناف</div>
              <div className="stat-card-subtitle">في المخزون</div>
            </div>
          </Card>

          <Card
            className="stat-card-wrapper"
            noPadding
            noShadow
          >
            <div className="stat-card">
              <div className="stat-card-icon" style={{ backgroundColor: 'rgba(243, 156, 18, 0.1)', color: 'var(--warning-color)' }}>
                <FaExclamationTriangle />
              </div>
              <div className="stat-card-value">{stats.low}</div>
              <div className="stat-card-title">أصناف تحت الحد الأدنى</div>
              <div className="stat-card-subtitle">بحاجة إلى تعبئة</div>
            </div>
          </Card>

          <Card
            className="stat-card-wrapper"
            noPadding
            noShadow
          >
            <div className="stat-card">
              <div className="stat-card-icon" style={{ backgroundColor: 'rgba(231, 76, 60, 0.1)', color: 'var(--danger-color)' }}>
                <FaTimesCircle />
              </div>
              <div className="stat-card-value">{stats.out}</div>
              <div className="stat-card-title">أصناف نفذت الكمية</div>
              <div className="stat-card-subtitle">غير متوفرة حالياً</div>
            </div>
          </Card>
        </div>
      </Card>

      {/* أدوات البحث والفلترة */}
      <Card
        title="البحث والتصفية"
        icon={<FaFilter />}
        className="search-filters-container"
      >
        <div className="search-filters">
          <div className="search-container">
            <FaSearch className="search-icon" />
            <input
              type="text"
              className="search-input"
              placeholder="بحث عن صنف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="filters">
            <div className="filter-item">
              <label className="filter-label">حالة المخزون</label>
              <select
                className="filter-select"
                value={stockFilter}
                onChange={(e) => setStockFilter(e.target.value)}
              >
                <option value="all">جميع الكميات</option>
                <option value="available">متوفر</option>
                <option value="low">تحت الحد الأدنى</option>
                <option value="out">نفذت الكمية</option>
              </select>
            </div>
          </div>
        </div>
      </Card>

      {/* جدول المخزون */}
      <Card
        title="قائمة الأصناف"
        icon={<FaBoxes />}
        className="inventory-table-container"
        actions={
          <div className="d-flex align-items-center">
            <Button
              variant="success"
              size="sm"
              icon={<FaFilePdf />}
              onClick={exportInventoryToPDF}
              className="mr-2"
            >
              تصدير PDF
            </Button>
            <span className="inventory-count mr-2">
              {filteredInventory.length} صنف
            </span>
          </div>
        }
      >
        <div ref={inventoryTableRef}>
          <DataTable
          columns={[
            {
              header: '#',
              accessor: 'index',
              cell: (row, index) => index + 1,
              style: { width: '50px' }
            },
            {
              header: 'الاسم',
              accessor: 'name',
              cell: (row) => (
                <div className="item-name">{row.name}</div>
              )
            },
            {
              header: 'وحدة القياس',
              accessor: 'unit',
              cell: (row) => (
                <div className="item-unit">{row.unit || '-'}</div>
              )
            },

            {
              header: 'الكمية الحالية',
              accessor: 'current_quantity',
              cell: (row) => {
                if (row.current_quantity === 0) {
                  return (
                    <div className="quantity-badge out">
                      <FaTimesCircle className="ml-1" />
                      نفذت الكمية
                    </div>
                  );
                } else if (row.current_quantity <= row.minimum_quantity) {
                  return (
                    <div className="quantity-badge low">
                      <FaExclamationTriangle className="ml-1" />
                      {row.current_quantity}
                    </div>
                  );
                } else {
                  return (
                    <div className="quantity-badge available">
                      {row.current_quantity}
                    </div>
                  );
                }
              }
            },
            {
              header: 'الحد الأدنى',
              accessor: 'minimum_quantity',
              cell: (row) => (
                <div className="item-min-quantity">
                  {row.minimum_quantity}
                </div>
              )
            },
            {
              header: 'متوسط السعر',
              accessor: 'avg_price',
              cell: (row) => (
                <div className="item-price">
                  {row.avg_price ? <FormattedCurrency amount={row.avg_price} /> : '-'}
                </div>
              )
            },
            {
              header: 'آخر سعر شراء',
              accessor: 'last_purchase_price',
              cell: (row) => (
                <div className="item-price last-purchase-price">
                  {row.last_purchase_price ? <FormattedCurrency amount={row.last_purchase_price} /> : '-'}
                </div>
              )
            },
            {
              header: 'سعر البيع',
              accessor: 'selling_price',
              cell: (row) => (
                <div className="item-price">
                  {row.selling_price ? <FormattedCurrency amount={row.selling_price} /> : '-'}
                </div>
              )
            },
            {
              header: 'آخر تحديث',
              accessor: 'last_updated',
              cell: (row) => (
                <div className="item-date">
                  {formatDate(row.last_updated)}
                </div>
              )
            },

          ]}
          data={filteredInventory}
          pagination={true}
          pageSize={10}
          searchable={false}
          emptyMessage={
            searchTerm || stockFilter !== 'all'
              ? 'لا توجد نتائج للبحث'
              : 'لا توجد أصناف مسجلة'
          }
        />
        </div>
      </Card>


    </div>
  );
};

export default Inventory;