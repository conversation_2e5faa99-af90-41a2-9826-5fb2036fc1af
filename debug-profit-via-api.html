<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص حساب الأرباح</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #ffe8e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            color: #d00;
        }
        .warning {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .comparison-table th {
            background: #f0f0f0;
        }
        .highlight {
            background: #ffeb3b !important;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص حساب الأرباح</h1>
        
        <button onclick="runDiagnosis()">تشغيل التشخيص</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function runDiagnosis() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>جاري التشخيص...</p>';
            
            try {
                // 1. فحص بيانات الخزينة
                const cashbox = await window.api.cashbox.get();
                
                let html = '<div class="section"><h3>📊 1. بيانات الخزينة الحالية</h3>';
                if (cashbox && cashbox.exists) {
                    html += `
                        <div class="result">
                            <p><strong>الرصيد الافتتاحي:</strong> ${cashbox.initial_balance}</p>
                            <p><strong>الرصيد الحالي:</strong> ${cashbox.current_balance}</p>
                            <p><strong>إجمالي المبيعات:</strong> ${cashbox.sales_total}</p>
                            <p><strong>إجمالي المشتريات:</strong> ${cashbox.purchases_total}</p>
                            <p><strong>إجمالي المرتجعات:</strong> ${cashbox.returns_total || 0}</p>
                            <p><strong>إجمالي مصاريف النقل:</strong> ${cashbox.transport_total || 0}</p>
                            <p><strong>إجمالي الأرباح (محفوظ):</strong> ${cashbox.profit_total}</p>
                        </div>
                    `;
                } else {
                    html += '<div class="error">❌ لا توجد خزينة</div>';
                    resultsDiv.innerHTML = html + '</div>';
                    return;
                }
                html += '</div>';
                
                // 2. حساب الأرباح بالطريقة البسيطة
                const simpleProfit = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);
                
                html += `
                    <div class="section">
                        <h3>🧮 2. حساب الأرباح بالطريقة البسيطة</h3>
                        <div class="result">
                            <p><strong>المعادلة:</strong> ${cashbox.sales_total} - ${cashbox.purchases_total} - ${cashbox.transport_total || 0} = ${simpleProfit}</p>
                        </div>
                    </div>
                `;
                
                // 3. جلب المعاملات وحساب الأرباح الفعلية
                const transactionsQuery = `
                    SELECT 
                        t.*,
                        i.avg_price,
                        i.selling_price as inventory_selling_price
                    FROM transactions t
                    LEFT JOIN inventory i ON t.item_id = i.item_id
                    WHERE t.transaction_type IN ('sale', 'return')
                        AND (t.status IS NULL OR t.status = 'active')
                    ORDER BY t.transaction_date DESC
                `;
                
                const transactionsResult = await window.api.invoke('execute-direct-query', { query: transactionsQuery });
                
                if (transactionsResult && transactionsResult.success) {
                    const transactions = transactionsResult.data;
                    
                    let totalSalesAmount = 0;
                    let totalCalculatedProfit = 0;
                    let totalReturnsAmount = 0;
                    let totalReturnProfit = 0;
                    
                    let salesDetails = '';
                    let returnsDetails = '';
                    
                    transactions.forEach((transaction, index) => {
                        const amount = parseFloat(transaction.total_price) || 0;
                        const transportCost = parseFloat(transaction.transport_cost) || 0;
                        const quantity = parseFloat(transaction.quantity) || 0;
                        const sellingPrice = parseFloat(transaction.selling_price) || 0;
                        const avgPrice = parseFloat(transaction.avg_price) || 0;
                        const savedProfit = parseFloat(transaction.profit) || 0;
                        
                        if (transaction.transaction_type === 'sale') {
                            totalSalesAmount += amount;
                            
                            let calculatedProfit = 0;
                            let profitMethod = '';
                            
                            if (savedProfit > 0) {
                                calculatedProfit = savedProfit;
                                profitMethod = `ربح محفوظ = ${savedProfit}`;
                            } else if (sellingPrice > 0 && avgPrice > 0) {
                                const basicProfit = (sellingPrice - avgPrice) * quantity;
                                calculatedProfit = basicProfit - transportCost;
                                profitMethod = `(${sellingPrice} - ${avgPrice}) × ${quantity} - ${transportCost} = ${calculatedProfit}`;
                            } else {
                                calculatedProfit = amount * 0.2;
                                profitMethod = `تقدير 20% من ${amount} = ${calculatedProfit}`;
                            }
                            
                            totalCalculatedProfit += calculatedProfit;
                            salesDetails += `<p>معاملة ${index + 1}: ${profitMethod}</p>`;
                            
                        } else if (transaction.transaction_type === 'return') {
                            totalReturnsAmount += amount;
                            const returnProfit = Math.abs(savedProfit);
                            totalReturnProfit += returnProfit;
                            returnsDetails += `<p>مرتجع ${index + 1}: مبلغ = ${amount}, ربح مخصوم = ${returnProfit}</p>`;
                        }
                    });
                    
                    const finalCalculatedProfit = totalCalculatedProfit - totalReturnProfit;
                    
                    html += `
                        <div class="section">
                            <h3>📈 3. حساب الأرباح من المعاملات الفعلية</h3>
                            <div class="result">
                                <p><strong>عدد معاملات المبيعات:</strong> ${transactions.filter(t => t.transaction_type === 'sale').length}</p>
                                <p><strong>إجمالي مبلغ المبيعات:</strong> ${totalSalesAmount}</p>
                                <p><strong>إجمالي الأرباح المحسوبة:</strong> ${totalCalculatedProfit}</p>
                                ${salesDetails}
                                
                                <p><strong>عدد معاملات المرتجعات:</strong> ${transactions.filter(t => t.transaction_type === 'return').length}</p>
                                <p><strong>إجمالي مبلغ المرتجعات:</strong> ${totalReturnsAmount}</p>
                                <p><strong>إجمالي أرباح مخصومة:</strong> ${totalReturnProfit}</p>
                                ${returnsDetails}
                                
                                <p><strong>الأرباح النهائية:</strong> ${totalCalculatedProfit} - ${totalReturnProfit} = ${finalCalculatedProfit}</p>
                            </div>
                        </div>
                    `;
                    
                    // 4. مقارنة الطرق
                    const method1 = simpleProfit;
                    const method2 = finalCalculatedProfit;
                    const method3 = cashbox.profit_total;
                    
                    html += `
                        <div class="section">
                            <h3>🎯 4. مقارنة طرق حساب الأرباح</h3>
                            <table class="comparison-table">
                                <tr>
                                    <th>الطريقة</th>
                                    <th>القيمة</th>
                                    <th>الوصف</th>
                                </tr>
                                <tr ${Math.abs(method1 - method3) < 0.01 ? 'class="highlight"' : ''}>
                                    <td>الطريقة 1</td>
                                    <td>${method1}</td>
                                    <td>المعادلة البسيطة (المبيعات - المشتريات - النقل)</td>
                                </tr>
                                <tr ${Math.abs(method2 - method3) < 0.01 ? 'class="highlight"' : ''}>
                                    <td>الطريقة 2</td>
                                    <td>${method2}</td>
                                    <td>من المعاملات الفعلية</td>
                                </tr>
                                <tr class="highlight">
                                    <td>الطريقة 3</td>
                                    <td>${method3}</td>
                                    <td>المحفوظ في قاعدة البيانات</td>
                                </tr>
                            </table>
                        </div>
                    `;
                    
                    // 5. تحليل الاختلافات
                    const diff12 = Math.abs(method1 - method2);
                    const diff13 = Math.abs(method1 - method3);
                    const diff23 = Math.abs(method2 - method3);
                    
                    html += `
                        <div class="section">
                            <h3>🔍 5. تحليل الاختلافات</h3>
                            <div class="result">
                                <p><strong>الفرق بين الطريقة 1 و 2:</strong> ${diff12}</p>
                                <p><strong>الفرق بين الطريقة 1 و 3:</strong> ${diff13}</p>
                                <p><strong>الفرق بين الطريقة 2 و 3:</strong> ${diff23}</p>
                            </div>
                    `;
                    
                    if (diff12 > 0.01) {
                        html += '<div class="warning">⚠️ هناك اختلاف بين المعادلة البسيطة والحساب من المعاملات</div>';
                    }
                    
                    if (diff13 > 0.01) {
                        html += '<div class="warning">⚠️ هناك اختلاف بين المعادلة البسيطة والقيمة المحفوظة</div>';
                    }
                    
                    if (diff23 > 0.01) {
                        html += '<div class="warning">⚠️ هناك اختلاف بين الحساب من المعاملات والقيمة المحفوظة</div>';
                    }
                    
                    html += '</div>';
                    
                    // 6. التوصيات
                    html += `
                        <div class="section">
                            <h3>💡 6. التوصيات</h3>
                    `;
                    
                    if (Math.abs(method1 - method3) > 0.01) {
                        html += `
                            <div class="warning">
                                <p>🔧 يُنصح بتشغيل دالة إصلاح حساب الأرباح</p>
                                <p>📝 القيمة الصحيحة يجب أن تكون: ${method1}</p>
                                <button onclick="fixProfits()">إصلاح حساب الأرباح</button>
                            </div>
                        `;
                    } else {
                        html += '<div class="result">✅ حساب الأرباح صحيح</div>';
                    }
                    
                    html += '</div>';
                }
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ خطأ في التشخيص: ${error.message}</div>`;
                console.error('خطأ في التشخيص:', error);
            }
        }
        
        async function fixProfits() {
            try {
                const result = await window.api.cashbox.fixProfitCalculation();
                if (result.success) {
                    alert('تم إصلاح حساب الأرباح بنجاح');
                    runDiagnosis(); // إعادة تشغيل التشخيص
                } else {
                    alert('فشل في إصلاح حساب الأرباح: ' + result.error);
                }
            } catch (error) {
                alert('خطأ في إصلاح حساب الأرباح: ' + error.message);
            }
        }
    </script>
</body>
</html>
