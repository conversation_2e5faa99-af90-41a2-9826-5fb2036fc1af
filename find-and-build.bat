@echo off
echo ========================================
echo البحث عن Node.js وبناء التطبيق
echo ========================================
echo.

REM البحث عن Node.js في المسارات الشائعة
set NODE_PATH=""
set NPM_PATH=""

echo 🔍 البحث عن Node.js...

REM المسارات الشائعة لـ Node.js
if exist "C:\Program Files\nodejs\node.exe" (
    set NODE_PATH="C:\Program Files\nodejs\node.exe"
    set NPM_PATH="C:\Program Files\nodejs\npm.cmd"
    echo ✅ تم العثور على Node.js في: C:\Program Files\nodejs\
    goto :found_node
)

if exist "C:\Program Files (x86)\nodejs\node.exe" (
    set NODE_PATH="C:\Program Files (x86)\nodejs\node.exe"
    set NPM_PATH="C:\Program Files (x86)\nodejs\npm.cmd"
    echo ✅ تم العثور على Node.js في: C:\Program Files (x86)\nodejs\
    goto :found_node
)

REM محاولة استخدام PATH
node --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set NODE_PATH="node"
    set NPM_PATH="npm"
    echo ✅ تم العثور على Node.js في PATH
    goto :found_node
)

echo ❌ لم يتم العثور على Node.js
echo.
echo يرجى التأكد من:
echo 1. تثبيت Node.js بشكل صحيح
echo 2. إعادة تشغيل PowerShell/Command Prompt
echo 3. إعادة تشغيل الكمبيوتر إذا لزم الأمر
echo.
echo في هذه الأثناء، يمكنك:
echo - استخدام التطبيق الموجود في dist-final
echo - تطبيق ترقية قاعدة البيانات باستخدام SQLite Browser
pause
exit /b 1

:found_node
echo.
echo 📋 معلومات Node.js:
%NODE_PATH% --version
%NPM_PATH% --version
echo.

REM تطبيق ترقية قاعدة البيانات
echo 🗄️ تطبيق ترقية قاعدة البيانات...
%NODE_PATH% simple-db-migration.js
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم تطبيق ترقية قاعدة البيانات بنجاح
) else (
    echo ⚠️ تحذير: قد تحتاج لتطبيق ترقية قاعدة البيانات يدوياً
    echo استخدم SQLite Browser مع ملف manual-db-migration.sql
)
echo.

REM التحقق من node_modules
if not exist "node_modules" (
    echo 📦 تثبيت dependencies...
    %NPM_PATH% install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت dependencies
        echo جرب: npm install يدوياً
        pause
        exit /b 1
    )
)

REM بناء التطبيق
echo 📦 بناء التطبيق باستخدام Webpack...
%NPM_PATH% run build 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ فشل npm run build، جرب webpack مباشرة...
    %NODE_PATH% node_modules\webpack\bin\webpack.js --mode production
)

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo.
    echo الملفات المبنية:
    if exist "bundle.js" echo   ✅ bundle.js
    if exist "bundle.js.map" echo   ✅ bundle.js.map
    echo.
    echo 🎉 ميزة إلغاء فواتير الشراء جاهزة!
    echo.
    echo لتشغيل التطبيق:
    echo   %NPM_PATH% start
    echo أو:
    echo   %NODE_PATH% main.js
) else (
    echo.
    echo ❌ فشل في بناء التطبيق
    echo.
    echo الحلول البديلة:
    echo 1. استخدم التطبيق الموجود في dist-final
    echo 2. طبق ترقية قاعدة البيانات يدوياً
    echo 3. جرب: npm install ثم npm run build
)

echo.
pause
