// فحص دور المستخدم الحالي
console.log('=== فحص دور المستخدم ===');

// في المتصفح، افتح أدوات المطور (F12) وانسخ هذا الكود:
/*
console.log('Current User Role:', localStorage.getItem('currentUserRole'));
console.log('Current User ID:', localStorage.getItem('currentUserId'));
console.log('Current User Name:', localStorage.getItem('currentUserName'));

// التحقق من الصلاحيات
const userRole = localStorage.getItem('currentUserRole');
const canMakePurchases = !['admin', 'viewer'].includes(userRole);
console.log('Can Make Purchases:', canMakePurchases);

// التحقق من وجود الدوال
console.log('handleCancelPurchase exists:', typeof handleCancelPurchase !== 'undefined');
console.log('CancellationConfirmDialog exists:', typeof CancellationConfirmDialog !== 'undefined');

// التحقق من البيانات
if (typeof window.api !== 'undefined') {
  window.api.invoke('get-transactions').then(transactions => {
    const purchases = transactions.filter(t => t.transaction_type === 'purchase');
    console.log('Total purchases:', purchases.length);
    console.log('First purchase:', purchases[0]);
    console.log('Purchase status field exists:', purchases[0] && 'status' in purchases[0]);
  }).catch(err => {
    console.error('Error getting transactions:', err);
  });
}
*/

console.log('انسخ الكود أعلاه في وحدة التحكم (Console) في المتصفح');
