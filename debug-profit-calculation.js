#!/usr/bin/env node

/**
 * سكريبت تشخيص حساب الأرباح
 * 
 * هذا السكريبت يفحص جميع طرق حساب الأرباح في النظام
 * ويوضح سبب التضارب في القيم
 */

const Database = require('better-sqlite3');
const path = require('path');

// مسار قاعدة البيانات
const dbPath = path.join(__dirname, 'wms.db');

console.log('🔍 بدء تشخيص حساب الأرباح...');
console.log('📁 مسار قاعدة البيانات:', dbPath);

async function debugProfitCalculation() {
  let db;
  
  try {
    // فتح قاعدة البيانات
    db = new Database(dbPath);
    
    console.log('✅ تم فتح قاعدة البيانات بنجاح');
    
    // 1. فحص بيانات الخزينة الحالية
    console.log('\n📊 1. بيانات الخزينة الحالية:');
    const cashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const cashbox = cashboxStmt.get();
    
    if (cashbox) {
      console.log('   - الرصيد الافتتاحي:', cashbox.initial_balance);
      console.log('   - الرصيد الحالي:', cashbox.current_balance);
      console.log('   - إجمالي المبيعات:', cashbox.sales_total);
      console.log('   - إجمالي المشتريات:', cashbox.purchases_total);
      console.log('   - إجمالي المرتجعات:', cashbox.returns_total);
      console.log('   - إجمالي مصاريف النقل:', cashbox.transport_total);
      console.log('   - إجمالي الأرباح (محفوظ):', cashbox.profit_total);
    } else {
      console.log('   ❌ لا توجد خزينة');
      return;
    }
    
    // 2. حساب الأرباح بالطريقة البسيطة (المعادلة المعروضة)
    console.log('\n🧮 2. حساب الأرباح بالطريقة البسيطة:');
    const simpleProfit = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);
    console.log(`   المعادلة: ${cashbox.sales_total} - ${cashbox.purchases_total} - ${cashbox.transport_total} = ${simpleProfit}`);
    
    // 3. حساب الأرباح من المعاملات الفعلية
    console.log('\n📈 3. حساب الأرباح من المعاملات الفعلية:');
    
    // جلب معاملات المبيعات
    const salesStmt = db.prepare(`
      SELECT 
        t.*,
        i.avg_price,
        i.selling_price as inventory_selling_price
      FROM transactions t
      LEFT JOIN inventory i ON t.item_id = i.item_id
      WHERE t.transaction_type = 'sale'
        AND (t.status IS NULL OR t.status = 'active')
    `);
    const salesTransactions = salesStmt.all();
    
    console.log(`   - عدد معاملات المبيعات: ${salesTransactions.length}`);
    
    let totalSalesAmount = 0;
    let totalCalculatedProfit = 0;
    
    salesTransactions.forEach((transaction, index) => {
      const amount = parseFloat(transaction.total_price) || 0;
      const transportCost = parseFloat(transaction.transport_cost) || 0;
      const quantity = parseFloat(transaction.quantity) || 0;
      const sellingPrice = parseFloat(transaction.selling_price) || 0;
      const avgPrice = parseFloat(transaction.avg_price) || 0;
      const savedProfit = parseFloat(transaction.profit) || 0;
      
      totalSalesAmount += amount;
      
      // حساب الربح
      let calculatedProfit = 0;
      
      if (savedProfit > 0) {
        calculatedProfit = savedProfit;
        console.log(`   معاملة ${index + 1}: ربح محفوظ = ${savedProfit}`);
      } else if (sellingPrice > 0 && avgPrice > 0) {
        const basicProfit = (sellingPrice - avgPrice) * quantity;
        calculatedProfit = basicProfit - transportCost;
        console.log(`   معاملة ${index + 1}: (${sellingPrice} - ${avgPrice}) × ${quantity} - ${transportCost} = ${calculatedProfit}`);
      } else {
        calculatedProfit = amount * 0.2; // تقدير 20%
        console.log(`   معاملة ${index + 1}: تقدير 20% من ${amount} = ${calculatedProfit}`);
      }
      
      totalCalculatedProfit += calculatedProfit;
    });
    
    console.log(`   - إجمالي مبلغ المبيعات: ${totalSalesAmount}`);
    console.log(`   - إجمالي الأرباح المحسوبة: ${totalCalculatedProfit}`);
    
    // 4. فحص معاملات المرتجعات
    console.log('\n↩️ 4. فحص معاملات المرتجعات:');
    const returnsStmt = db.prepare(`
      SELECT 
        t.*,
        i.avg_price
      FROM transactions t
      LEFT JOIN inventory i ON t.item_id = i.item_id
      WHERE t.transaction_type = 'return'
        AND (t.status IS NULL OR t.status = 'active')
    `);
    const returnTransactions = returnsStmt.all();
    
    console.log(`   - عدد معاملات المرتجعات: ${returnTransactions.length}`);
    
    let totalReturnsAmount = 0;
    let totalReturnProfit = 0;
    
    returnTransactions.forEach((transaction, index) => {
      const amount = parseFloat(transaction.total_price) || 0;
      const savedProfit = parseFloat(transaction.profit) || 0;
      
      totalReturnsAmount += amount;
      totalReturnProfit += Math.abs(savedProfit);
      
      console.log(`   مرتجع ${index + 1}: مبلغ = ${amount}, ربح مخصوم = ${Math.abs(savedProfit)}`);
    });
    
    console.log(`   - إجمالي مبلغ المرتجعات: ${totalReturnsAmount}`);
    console.log(`   - إجمالي أرباح مخصومة: ${totalReturnProfit}`);
    
    // 5. حساب الأرباح النهائية بطرق مختلفة
    console.log('\n🎯 5. مقارنة طرق حساب الأرباح:');
    
    const method1 = simpleProfit; // المعادلة البسيطة
    const method2 = totalCalculatedProfit - totalReturnProfit; // من المعاملات الفعلية
    const method3 = cashbox.profit_total; // المحفوظ في قاعدة البيانات
    
    console.log(`   الطريقة 1 (المعادلة البسيطة): ${method1}`);
    console.log(`   الطريقة 2 (من المعاملات الفعلية): ${method2}`);
    console.log(`   الطريقة 3 (المحفوظ في قاعدة البيانات): ${method3}`);
    
    // 6. تحليل الاختلافات
    console.log('\n🔍 6. تحليل الاختلافات:');
    
    const diff12 = Math.abs(method1 - method2);
    const diff13 = Math.abs(method1 - method3);
    const diff23 = Math.abs(method2 - method3);
    
    console.log(`   الفرق بين الطريقة 1 و 2: ${diff12}`);
    console.log(`   الفرق بين الطريقة 1 و 3: ${diff13}`);
    console.log(`   الفرق بين الطريقة 2 و 3: ${diff23}`);
    
    if (diff12 > 0.01) {
      console.log('   ⚠️ هناك اختلاف بين المعادلة البسيطة والحساب من المعاملات');
    }
    
    if (diff13 > 0.01) {
      console.log('   ⚠️ هناك اختلاف بين المعادلة البسيطة والقيمة المحفوظة');
    }
    
    if (diff23 > 0.01) {
      console.log('   ⚠️ هناك اختلاف بين الحساب من المعاملات والقيمة المحفوظة');
    }
    
    // 7. فحص المعاملات اليدوية
    console.log('\n💰 7. فحص المعاملات اليدوية:');
    const manualStmt = db.prepare('SELECT * FROM cashbox_transactions ORDER BY created_at DESC');
    const manualTransactions = manualStmt.all();
    
    console.log(`   - عدد المعاملات اليدوية: ${manualTransactions.length}`);
    
    let manualIncome = 0;
    let manualExpense = 0;
    
    manualTransactions.forEach((transaction, index) => {
      const amount = parseFloat(transaction.amount) || 0;
      
      if (transaction.type === 'income') {
        manualIncome += amount;
      } else if (transaction.type === 'expense') {
        manualExpense += amount;
      }
      
      console.log(`   معاملة ${index + 1}: ${transaction.type} - ${amount} - ${transaction.source} - ${transaction.notes}`);
    });
    
    console.log(`   - إجمالي الإيداعات اليدوية: ${manualIncome}`);
    console.log(`   - إجمالي السحوبات اليدوية: ${manualExpense}`);
    
    // 8. التوصيات
    console.log('\n💡 8. التوصيات:');
    
    if (Math.abs(method1 - method3) > 0.01) {
      console.log('   🔧 يُنصح بتشغيل دالة إصلاح حساب الأرباح');
      console.log('   📝 القيمة الصحيحة يجب أن تكون:', method1);
    } else {
      console.log('   ✅ حساب الأرباح صحيح');
    }
    
    console.log('\n🎉 انتهى تشخيص حساب الأرباح');
    
  } catch (error) {
    console.error('❌ خطأ في تشخيص حساب الأرباح:', error.message);
  } finally {
    if (db) {
      db.close();
      console.log('🔒 تم إغلاق قاعدة البيانات');
    }
  }
}

// تشغيل التشخيص
if (require.main === module) {
  debugProfitCalculation();
}

module.exports = { debugProfitCalculation };
