# تعليمات بناء نظام إدارة المخازن

## متطلبات البناء

### البرامج المطلوبة:
- **Node.js** (الإصدار 18 أو أحدث)
- **npm** (يأتي مع Node.js)
- **Git** (اختياري)
- **Windows 10/11** (للبناء على Windows)

### التحقق من التثبيت:
```bash
node --version    # يجب أن يكون 18.0.0 أو أحدث
npm --version     # يجب أن يكون 8.0.0 أو أحدث
```

## خطوات البناء

### 1. تحضير البيئة
```bash
# تثبيت التبعيات
npm install

# التحقق من وجود الملفات المطلوبة
npm run prebuild
```

### 2. البناء السريع (الطريقة المُوصى بها)
```bash
# تشغيل سكريبت البناء الشامل
npm run build-installer
```

هذا الأمر سيقوم بـ:
- ✅ تنظيف ملفات البناء السابقة
- ✅ التحقق من الملفات المطلوبة
- ✅ بناء الواجهة الأمامية للإنتاج
- ✅ إنشاء الملف التنفيذي
- ✅ إنشاء مثبت NSIS
- ✅ إنشاء نسخة محمولة

### 3. البناء اليدوي (خطوة بخطوة)
```bash
# تنظيف ملفات البناء السابقة
npm run clean

# بناء الواجهة الأمامية
npx webpack --mode production

# إنشاء الملف التنفيذي والمثبت
npx electron-builder --win
```

### 4. أوامر بناء إضافية
```bash
# إنشاء نسخة محمولة فقط
npm run portable

# إنشاء مجلد التطبيق بدون مثبت
npm run pack

# بناء للتطوير
npm run build
```

## الملفات الناتجة

بعد البناء الناجح، ستجد الملفات في مجلد `dist/`:

### ملفات المثبت:
- `نظام-إدارة-المخازن-1.1.1-installer.exe` - مثبت NSIS
- `نظام-إدارة-المخازن-1.1.1-portable.exe` - نسخة محمولة

### مجلدات إضافية:
- `win-unpacked/` - ملفات التطبيق غير المضغوطة
- `builder-debug.yml` - معلومات البناء للتشخيص

## استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ "node-gyp rebuild failed"
```bash
# تثبيت أدوات البناء
npm install --global windows-build-tools
# أو
npm install --global @microsoft/rush-stack-compiler-3.9
```

#### 2. خطأ "electron-builder not found"
```bash
# إعادة تثبيت electron-builder
npm uninstall electron-builder
npm install --save-dev electron-builder@latest
```

#### 3. خطأ في بناء better-sqlite3
```bash
# إعادة بناء الوحدات الأصلية
npm run postinstall
# أو
npx electron-rebuild
```

#### 4. مشاكل في الذاكرة
```bash
# زيادة حد الذاكرة لـ Node.js
set NODE_OPTIONS=--max-old-space-size=4096
npm run build-installer
```

### فحص السجلات:
- سجلات البناء تظهر في وحدة التحكم
- ملفات السجل المفصلة في `dist/builder-debug.yml`
- سجلات webpack في `bundle.js.map`

## التخصيص

### تغيير معلومات التطبيق:
عدّل الحقول التالية في `package.json`:
- `name` - اسم التطبيق
- `version` - رقم الإصدار
- `description` - وصف التطبيق
- `author` - المطور

### تغيير إعدادات المثبت:
عدّل قسم `build.nsis` في `package.json`:
- `shortcutName` - اسم الاختصار
- `menuCategory` - فئة قائمة البرامج
- `artifactName` - اسم ملف المثبت

### إضافة أيقونات مخصصة:
- ضع ملف `.ico` في `assets/icons/`
- حدّث مسار `build.win.icon` في `package.json`

## نصائح للأداء

1. **استخدم SSD** لتسريع عملية البناء
2. **أغلق برامج مكافحة الفيروسات** مؤقتاً أثناء البناء
3. **تأكد من وجود مساحة كافية** (على الأقل 2 GB)
4. **استخدم PowerShell كمدير** لتجنب مشاكل الصلاحيات

## الدعم

إذا واجهت مشاكل في البناء:
1. تأكد من تحديث Node.js و npm
2. احذف `node_modules` و `package-lock.json` وأعد التثبيت
3. تحقق من سجلات الأخطاء
4. تواصل مع فريق التطوير مع تفاصيل الخطأ
