<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل إصلاح حساب الأرباح</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #ffe8e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            color: #d00;
        }
        .warning {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .fix-button {
            background: #28a745;
            font-size: 18px;
            padding: 15px 30px;
        }
        .fix-button:hover {
            background: #218838;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .comparison-table th {
            background: #f0f0f0;
        }
        .highlight {
            background: #ffeb3b !important;
            font-weight: bold;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-size: 16px;
            color: #666;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح حساب الأرباح</h1>
        
        <div class="info">
            <h3>📋 ما سيتم إصلاحه:</h3>
            <ul>
                <li>توحيد قيم الأرباح في جميع أجزاء النظام</li>
                <li>إعادة حساب الأرباح بالمعادلة الصحيحة: المبيعات - المشتريات - مصاريف النقل</li>
                <li>الحفاظ على منطق مصاريف النقل (تُخصم عند البيع وليس المشتريات)</li>
                <li>تحديث قاعدة البيانات بالقيم الصحيحة</li>
            </ul>
        </div>

        <div class="section">
            <h3>📊 الحالة الحالية</h3>
            <div id="currentStatus">
                <button onclick="checkCurrentStatus()">فحص الحالة الحالية</button>
            </div>
        </div>

        <div class="section">
            <h3>🔧 تشغيل الإصلاح</h3>
            <div class="warning">
                <p><strong>تنبيه:</strong> سيتم إعادة حساب جميع الأرباح بناءً على البيانات الحالية في قاعدة البيانات.</p>
                <p>تأكد من عمل نسخة احتياطية قبل المتابعة.</p>
            </div>
            
            <button class="fix-button" onclick="runProfitFix()" id="fixButton">
                🚀 تشغيل إصلاح الأرباح
            </button>
        </div>

        <div class="section">
            <h3>📈 النتائج</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        let isFixing = false;

        async function checkCurrentStatus() {
            try {
                const cashbox = await window.api.cashbox.get();
                
                if (!cashbox || !cashbox.exists) {
                    document.getElementById('currentStatus').innerHTML = 
                        '<div class="error">❌ لا توجد خزينة في النظام</div>';
                    return;
                }

                const simpleProfit = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);
                const savedProfit = cashbox.profit_total || 0;
                const difference = Math.abs(simpleProfit - savedProfit);

                let statusHtml = `
                    <div class="result">
                        <h4>📊 قيم الخزينة الحالية:</h4>
                        <table class="comparison-table">
                            <tr>
                                <th>البيان</th>
                                <th>القيمة</th>
                            </tr>
                            <tr>
                                <td>إجمالي المبيعات</td>
                                <td>${cashbox.sales_total || 0} د.ل</td>
                            </tr>
                            <tr>
                                <td>إجمالي المشتريات</td>
                                <td>${cashbox.purchases_total || 0} د.ل</td>
                            </tr>
                            <tr>
                                <td>إجمالي مصاريف النقل</td>
                                <td>${cashbox.transport_total || 0} د.ل</td>
                            </tr>
                            <tr class="highlight">
                                <td>الأرباح المحفوظة</td>
                                <td>${savedProfit} د.ل</td>
                            </tr>
                            <tr class="highlight">
                                <td>الأرباح المحسوبة</td>
                                <td>${simpleProfit} د.ل</td>
                            </tr>
                            <tr>
                                <td>الفرق</td>
                                <td>${difference.toFixed(2)} د.ل</td>
                            </tr>
                        </table>
                    </div>
                `;

                if (difference > 0.01) {
                    statusHtml += `
                        <div class="warning">
                            ⚠️ هناك تضارب في قيم الأرباح! الفرق: ${difference.toFixed(2)} د.ل
                            <br>يُنصح بتشغيل الإصلاح.
                        </div>
                    `;
                } else {
                    statusHtml += `
                        <div class="result">
                            ✅ قيم الأرباح متطابقة ولا تحتاج إصلاح.
                        </div>
                    `;
                }

                document.getElementById('currentStatus').innerHTML = statusHtml;

            } catch (error) {
                document.getElementById('currentStatus').innerHTML = 
                    `<div class="error">❌ خطأ في فحص الحالة: ${error.message}</div>`;
            }
        }

        async function runProfitFix() {
            if (isFixing) return;
            
            isFixing = true;
            const fixButton = document.getElementById('fixButton');
            const resultsDiv = document.getElementById('results');
            
            fixButton.disabled = true;
            fixButton.textContent = '⏳ جاري الإصلاح...';
            
            resultsDiv.innerHTML = '<div class="loading">🔄 جاري تشغيل إصلاح الأرباح...</div>';

            try {
                // 1. فحص الحالة قبل الإصلاح
                const cashboxBefore = await window.api.cashbox.get();
                
                let stepsHtml = '<div class="step">✅ تم فحص حالة الخزينة قبل الإصلاح</div>';
                
                if (!cashboxBefore || !cashboxBefore.exists) {
                    throw new Error('لا توجد خزينة في النظام');
                }

                const profitBefore = cashboxBefore.profit_total || 0;
                const calculatedBefore = (cashboxBefore.sales_total || 0) - (cashboxBefore.purchases_total || 0) - (cashboxBefore.transport_total || 0);
                
                stepsHtml += `
                    <div class="step">
                        📊 القيم قبل الإصلاح:
                        <br>• الأرباح المحفوظة: ${profitBefore} د.ل
                        <br>• الأرباح المحسوبة: ${calculatedBefore} د.ل
                        <br>• الفرق: ${Math.abs(profitBefore - calculatedBefore).toFixed(2)} د.ل
                    </div>
                `;
                
                resultsDiv.innerHTML = stepsHtml;

                // 2. تشغيل الإصلاح
                stepsHtml += '<div class="step">⚙️ جاري تشغيل دالة إصلاح الأرباح...</div>';
                resultsDiv.innerHTML = stepsHtml;

                const fixResult = await window.api.cashbox.fixProfitCalculation();
                
                if (!fixResult.success) {
                    throw new Error(fixResult.error || 'فشل في تشغيل إصلاح الأرباح');
                }

                stepsHtml += '<div class="step">✅ تم تشغيل دالة الإصلاح بنجاح</div>';
                resultsDiv.innerHTML = stepsHtml;

                // 3. فحص النتائج بعد الإصلاح
                stepsHtml += '<div class="step">🔍 جاري فحص النتائج...</div>';
                resultsDiv.innerHTML = stepsHtml;

                // انتظار قليل للتأكد من تحديث البيانات
                await new Promise(resolve => setTimeout(resolve, 1000));

                const cashboxAfter = await window.api.cashbox.get();
                
                if (!cashboxAfter || !cashboxAfter.exists) {
                    throw new Error('فشل في الحصول على بيانات الخزينة بعد الإصلاح');
                }

                const profitAfter = cashboxAfter.profit_total || 0;
                const calculatedAfter = (cashboxAfter.sales_total || 0) - (cashboxAfter.purchases_total || 0) - (cashboxAfter.transport_total || 0);
                const differenceAfter = Math.abs(profitAfter - calculatedAfter);

                // 4. عرض النتائج النهائية
                stepsHtml += `
                    <div class="step">
                        ✅ تم الإصلاح بنجاح!
                    </div>
                    <div class="result">
                        <h4>📊 النتائج النهائية:</h4>
                        <table class="comparison-table">
                            <tr>
                                <th>البيان</th>
                                <th>قبل الإصلاح</th>
                                <th>بعد الإصلاح</th>
                            </tr>
                            <tr>
                                <td>الأرباح المحفوظة</td>
                                <td>${profitBefore} د.ل</td>
                                <td>${profitAfter} د.ل</td>
                            </tr>
                            <tr>
                                <td>الأرباح المحسوبة</td>
                                <td>${calculatedBefore} د.ل</td>
                                <td>${calculatedAfter} د.ل</td>
                            </tr>
                            <tr>
                                <td>الفرق</td>
                                <td>${Math.abs(profitBefore - calculatedBefore).toFixed(2)} د.ل</td>
                                <td>${differenceAfter.toFixed(2)} د.ل</td>
                            </tr>
                        </table>
                    </div>
                `;

                if (differenceAfter <= 0.01) {
                    stepsHtml += `
                        <div class="result">
                            🎉 <strong>تم توحيد قيم الأرباح بنجاح!</strong>
                            <br>جميع القيم أصبحت متطابقة: ${profitAfter} د.ل
                        </div>
                    `;
                } else {
                    stepsHtml += `
                        <div class="warning">
                            ⚠️ لا يزال هناك فرق طفيف: ${differenceAfter.toFixed(2)} د.ل
                            <br>قد تحتاج لإعادة تشغيل الإصلاح أو فحص البيانات يدوياً.
                        </div>
                    `;
                }

                resultsDiv.innerHTML = stepsHtml;

                // 5. تحديث الواجهة
                setTimeout(() => {
                    if (window.loadCashbox && typeof window.loadCashbox === 'function') {
                        window.loadCashbox();
                    }
                    
                    // إرسال حدث تحديث للواجهة
                    if (window.dispatchEvent) {
                        window.dispatchEvent(new CustomEvent('cashbox-updated-ui', {
                            detail: cashboxAfter
                        }));
                    }
                }, 500);

            } catch (error) {
                console.error('خطأ في إصلاح الأرباح:', error);
                resultsDiv.innerHTML = `
                    <div class="error">
                        ❌ <strong>فشل في إصلاح الأرباح:</strong>
                        <br>${error.message}
                        <br><br>
                        <button onclick="runProfitFix()">إعادة المحاولة</button>
                    </div>
                `;
            } finally {
                isFixing = false;
                fixButton.disabled = false;
                fixButton.textContent = '🚀 تشغيل إصلاح الأرباح';
            }
        }

        // فحص الحالة تلقائياً عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(checkCurrentStatus, 500);
        });
    </script>
</body>
</html>
