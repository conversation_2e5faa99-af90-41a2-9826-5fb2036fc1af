-- Migration Script: إضافة حقل آخر سعر شراء إلى جدول المخزون
-- تاريخ الإنشاء: 2025-01-18

-- التحقق من وجود الحقل أولاً
-- إذا كان الحقل موجود، فسيفشل هذا الأمر وهذا طبيعي
ALTER TABLE inventory ADD COLUMN last_purchase_price REAL DEFAULT 0;

-- ملء الحقل الجديد بآخر سعر شراء لكل صنف
UPDATE inventory 
SET last_purchase_price = (
  SELECT price 
  FROM transactions 
  WHERE transactions.item_id = inventory.item_id 
    AND transactions.transaction_type = 'purchase'
    AND (transactions.status IS NULL OR transactions.status = 'active')
  ORDER BY transactions.transaction_date DESC, transactions.id DESC
  LIMIT 1
)
WHERE EXISTS (
  SELECT 1 
  FROM transactions 
  WHERE transactions.item_id = inventory.item_id 
    AND transactions.transaction_type = 'purchase'
    AND (transactions.status IS NULL OR transactions.status = 'active')
);

-- التحقق من النتائج
SELECT 
  'Migration completed successfully' as status,
  COUNT(*) as total_items,
  COUNT(CASE WHEN last_purchase_price > 0 THEN 1 END) as items_with_last_price,
  COUNT(CASE WHEN avg_price > 0 THEN 1 END) as items_with_avg_price
FROM inventory;
