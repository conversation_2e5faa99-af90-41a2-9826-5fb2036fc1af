/**
 * خدمة إدارة التحديثات
 * توفر وظائف للتحقق من وجود تحديثات للتطبيق وقاعدة البيانات وتطبيقها
 * تدعم التحديث اليدوي للاستخدام التجاري الفوري
 */

const { app, dialog } = require('electron');
const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync, exec } = require('child_process');
const logger = require('../utils/logger');
const AdmZip = require('adm-zip');
const crypto = require('crypto');
const Database = require('better-sqlite3');

// معلومات التحديث
const updateInfo = {
  currentVersion: null, // سيتم تعيينها عند التهيئة
  latestVersion: null,
  releaseNotes: null,
  updateUrl: null,
  updateAvailable: false,
  lastCheckTime: null,
  updateFilePath: null
};

// معلومات تحديث قاعدة البيانات
const dbUpdateInfo = {
  currentVersion: '1.0',
  latestVersion: null,
  changes: [],
  updateAvailable: false,
  lastCheckTime: null,
  updateFilePath: null
};

// مسارات التحديث - سيتم تعيينها عند التهيئة
let updatePaths = {
  appUpdatesDir: null,
  dbUpdatesDir: null,
  backupsDir: null,
  tempDir: null
};

// تهيئة UpdateManager
function initialize() {
  try {
    // تعيين الإصدار الحالي
    updateInfo.currentVersion = app ? app.getVersion() : '1.1.1';

    // تعيين مسارات التحديث
    if (app) {
      updatePaths.appUpdatesDir = path.join(app.getPath('userData'), 'updates', 'app');
      updatePaths.dbUpdatesDir = path.join(app.getPath('userData'), 'updates', 'database');
      updatePaths.backupsDir = path.join(app.getPath('userData'), 'backups');
      updatePaths.tempDir = path.join(app.getPath('temp'), 'wms-updates');
    } else {
      // مسارات افتراضية إذا لم يكن app متاح
      const userDataPath = process.env.APPDATA || path.join(require('os').homedir(), '.config');
      updatePaths.appUpdatesDir = path.join(userDataPath, 'wms', 'updates', 'app');
      updatePaths.dbUpdatesDir = path.join(userDataPath, 'wms', 'updates', 'database');
      updatePaths.backupsDir = path.join(userDataPath, 'wms', 'backups');
      updatePaths.tempDir = path.join(require('os').tmpdir(), 'wms-updates');
    }

    // إنشاء المجلدات
    ensureDirectoriesExist();

    logger.info('تم تهيئة UpdateManager بنجاح');
  } catch (error) {
    logger.error('خطأ في تهيئة UpdateManager:', error);
  }
}

// إنشاء المجلدات إذا لم تكن موجودة
function ensureDirectoriesExist() {
  if (!updatePaths.appUpdatesDir) return; // لم يتم التهيئة بعد

  [updatePaths.appUpdatesDir, updatePaths.dbUpdatesDir, updatePaths.backupsDir, updatePaths.tempDir].forEach(dir => {
    if (dir && !fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.info(`تم إنشاء مجلد: ${dir}`);
    }
  });
}

/**
 * التحقق من وجود تحديثات للتطبيق
 * @param {Object} options - خيارات التحقق
 * @returns {Promise<Object>} - معلومات التحديث
 */
async function checkAppUpdates(options = {}) {
  try {
    logger.info('التحقق من وجود تحديثات للتطبيق...');

    // تحديث وقت آخر فحص
    updateInfo.lastCheckTime = new Date().toISOString();

    // في حالة التحديث اليدوي، نتحقق من وجود ملفات تحديث في مجلد التحديثات
    const updateFiles = fs.readdirSync(updatePaths.appUpdatesDir)
      .filter(file => file.endsWith('.zip') || file.endsWith('.exe'))
      .map(file => path.join(updatePaths.appUpdatesDir, file))
      .sort((a, b) => fs.statSync(b).mtime.getTime() - fs.statSync(a).mtime.getTime()); // ترتيب حسب الأحدث

    if (updateFiles.length > 0) {
      const updateFile = updateFiles[0]; // أحدث ملف تحديث

      try {
        // استخراج معلومات الإصدار من اسم الملف
        // نفترض أن اسم الملف يحتوي على رقم الإصدار بتنسيق app-update-X.Y.Z.zip
        const fileNameMatch = path.basename(updateFile).match(/app-update-(\d+\.\d+\.\d+)/i);
        const fileVersion = fileNameMatch ? fileNameMatch[1] : null;

        if (fileVersion && fileVersion !== updateInfo.currentVersion) {
          updateInfo.latestVersion = fileVersion;
          updateInfo.updateAvailable = true;
          updateInfo.updateFilePath = updateFile;
          updateInfo.releaseNotes = `تحديث جديد متاح! الإصدار ${fileVersion}\n\nملف التحديث: ${path.basename(updateFile)}`;
        } else {
          updateInfo.latestVersion = updateInfo.currentVersion;
          updateInfo.updateAvailable = false;
          updateInfo.releaseNotes = 'لا توجد تحديثات متاحة حاليًا.';
          updateInfo.updateFilePath = null;
        }
      } catch (err) {
        logger.warn('خطأ في استخراج معلومات الإصدار من ملف التحديث:', err);
        updateInfo.latestVersion = updateInfo.currentVersion;
        updateInfo.updateAvailable = false;
        updateInfo.releaseNotes = 'لا توجد تحديثات متاحة حاليًا.';
        updateInfo.updateFilePath = null;
      }
    } else {
      updateInfo.latestVersion = updateInfo.currentVersion;
      updateInfo.updateAvailable = false;
      updateInfo.releaseNotes = 'لا توجد تحديثات متاحة حاليًا.';
      updateInfo.updateFilePath = null;
    }

    // إذا كان هناك تحديث متاح (للاختبار)
    if (options.simulateUpdate) {
      updateInfo.latestVersion = '1.2.0';
      updateInfo.updateAvailable = true;
      updateInfo.releaseNotes = 'تحديث جديد متاح! الإصدار 1.2.0\n\n- تحسينات في الأداء\n- إصلاح مشكلات في واجهة المستخدم\n- إضافة ميزات جديدة';
      updateInfo.updateFilePath = path.join(updatePaths.appUpdatesDir, 'app-update-1.2.0.zip');
    }

    logger.info('اكتمل التحقق من وجود تحديثات للتطبيق:', {
      currentVersion: updateInfo.currentVersion,
      latestVersion: updateInfo.latestVersion,
      updateAvailable: updateInfo.updateAvailable,
      updateFilePath: updateInfo.updateFilePath
    });

    return {
      currentVersion: updateInfo.currentVersion,
      latestVersion: updateInfo.latestVersion,
      releaseNotes: updateInfo.releaseNotes,
      updateFilePath: updateInfo.updateFilePath,
      available: updateInfo.updateAvailable,
      lastCheckTime: updateInfo.lastCheckTime
    };
  } catch (error) {
    logger.error('خطأ في التحقق من وجود تحديثات للتطبيق:', error);
    throw error;
  }
}

/**
 * اختيار ملف تحديث للتطبيق
 * @returns {Promise<Object>} - معلومات ملف التحديث
 */
async function selectAppUpdateFile() {
  try {
    logger.info('فتح مربع حوار لاختيار ملف تحديث للتطبيق...');

    const result = await dialog.showOpenDialog({
      title: 'اختر ملف تحديث التطبيق',
      defaultPath: app.getPath('downloads'),
      filters: [
        { name: 'ملفات التحديث', extensions: ['zip', 'exe'] }
      ],
      properties: ['openFile']
    });

    if (result.canceled || result.filePaths.length === 0) {
      logger.info('تم إلغاء اختيار ملف التحديث');
      return { canceled: true };
    }

    const filePath = result.filePaths[0];
    logger.info('تم اختيار ملف التحديث:', filePath);

    // نسخ ملف التحديث إلى مجلد التحديثات
    const fileName = path.basename(filePath);
    const destPath = path.join(updatePaths.appUpdatesDir, fileName);

    // نسخ الملف إذا لم يكن موجودًا بالفعل
    if (filePath !== destPath) {
      fs.copyFileSync(filePath, destPath);
      logger.info('تم نسخ ملف التحديث إلى مجلد التحديثات:', destPath);
    }

    // استخراج معلومات الإصدار من اسم الملف
    const fileNameMatch = fileName.match(/app-update-(\d+\.\d+\.\d+)/i);
    const fileVersion = fileNameMatch ? fileNameMatch[1] : 'غير معروف';

    // تحديث معلومات التحديث
    updateInfo.latestVersion = fileVersion;
    updateInfo.updateAvailable = true;
    updateInfo.updateFilePath = destPath;
    updateInfo.releaseNotes = `تحديث جديد متاح! الإصدار ${fileVersion}\n\nملف التحديث: ${fileName}`;

    return {
      filePath: destPath,
      fileName: fileName,
      version: fileVersion,
      canceled: false
    };
  } catch (error) {
    logger.error('خطأ في اختيار ملف تحديث للتطبيق:', error);
    throw error;
  }
}

/**
 * تنزيل وتثبيت تحديث التطبيق
 * @param {Function} progressCallback - دالة لإرسال تقدم التنزيل
 * @returns {Promise<Object>} - نتيجة التثبيت
 */
async function downloadAndInstallUpdate(progressCallback) {
  try {
    logger.info('بدء تنزيل وتثبيت التحديث...');

    if (!updateInfo.updateAvailable || !updateInfo.updateFilePath) {
      throw new Error('لا توجد تحديثات متاحة للتثبيت');
    }

    // التحقق من وجود ملف التحديث
    if (!fs.existsSync(updateInfo.updateFilePath)) {
      throw new Error(`ملف التحديث غير موجود: ${updateInfo.updateFilePath}`);
    }

    // إنشاء نسخة احتياطية قبل التحديث
    const backupPath = await createAppBackup();
    logger.info('تم إنشاء نسخة احتياطية من التطبيق قبل التحديث:', backupPath);

    // إرسال تقدم البدء
    if (progressCallback) {
      progressCallback(5);
    }

    // تحديد نوع ملف التحديث
    const isZipFile = updateInfo.updateFilePath.toLowerCase().endsWith('.zip');
    const isExeFile = updateInfo.updateFilePath.toLowerCase().endsWith('.exe');

    let installResult = null;

    if (isZipFile) {
      // تثبيت التحديث من ملف ZIP
      installResult = await installUpdateFromZip(updateInfo.updateFilePath, progressCallback);
    } else if (isExeFile) {
      // تثبيت التحديث من ملف EXE
      installResult = await installUpdateFromExe(updateInfo.updateFilePath, progressCallback);
    } else {
      throw new Error(`نوع ملف التحديث غير مدعوم: ${path.extname(updateInfo.updateFilePath)}`);
    }

    logger.info('اكتمل تثبيت التحديث بنجاح:', installResult);

    // إرسال تقدم الانتهاء
    if (progressCallback) {
      progressCallback(100);
    }

    return {
      success: true,
      message: 'تم تثبيت التحديث بنجاح. سيتم إعادة تشغيل التطبيق لتطبيق التحديث.',
      ...installResult
    };
  } catch (error) {
    logger.error('خطأ في تثبيت التحديث:', error);
    throw error;
  }
}

/**
 * تثبيت التحديث من ملف ZIP
 * @param {string} zipFilePath - مسار ملف ZIP
 * @param {Function} progressCallback - دالة لإرسال تقدم التثبيت
 * @returns {Promise<Object>} - نتيجة التثبيت
 */
async function installUpdateFromZip(zipFilePath, progressCallback) {
  try {
    logger.info('بدء تثبيت التحديث من ملف ZIP:', zipFilePath);

    // إرسال تقدم البدء
    if (progressCallback) {
      progressCallback(10);
    }

    // إنشاء مجلد مؤقت لاستخراج الملفات
    const extractDir = path.join(updatePaths.tempDir, `update-${Date.now()}`);
    if (!fs.existsSync(extractDir)) {
      fs.mkdirSync(extractDir, { recursive: true });
    }

    // استخراج ملف ZIP
    const zip = new AdmZip(zipFilePath);
    zip.extractAllTo(extractDir, true);

    // إرسال تقدم الاستخراج
    if (progressCallback) {
      progressCallback(40);
    }

    logger.info('تم استخراج ملف ZIP إلى:', extractDir);

    // البحث عن ملف التثبيت (setup.exe أو install.exe) في المجلد المستخرج
    const setupFiles = fs.readdirSync(extractDir)
      .filter(file => file.toLowerCase().includes('setup') || file.toLowerCase().includes('install'))
      .filter(file => file.toLowerCase().endsWith('.exe'));

    if (setupFiles.length > 0) {
      // تشغيل ملف التثبيت
      const setupFilePath = path.join(extractDir, setupFiles[0]);
      logger.info('تشغيل ملف التثبيت:', setupFilePath);

      // إرسال تقدم بدء التثبيت
      if (progressCallback) {
        progressCallback(50);
      }

      // تشغيل ملف التثبيت
      exec(`"${setupFilePath}"`, (error, stdout, stderr) => {
        if (error) {
          logger.error('خطأ في تشغيل ملف التثبيت:', error);
        } else {
          logger.info('تم تشغيل ملف التثبيت بنجاح');
        }
      });

      // إرسال تقدم انتهاء التثبيت
      if (progressCallback) {
        progressCallback(90);
      }

      return {
        success: true,
        message: 'تم تشغيل ملف التثبيت بنجاح. يرجى اتباع التعليمات على الشاشة لإكمال التثبيت.',
        setupFilePath: setupFilePath
      };
    } else {
      // نسخ الملفات مباشرة إلى مجلد التطبيق
      logger.info('لم يتم العثور على ملف تثبيت. سيتم نسخ الملفات مباشرة إلى مجلد التطبيق.');

      // إرسال تقدم بدء النسخ
      if (progressCallback) {
        progressCallback(60);
      }

      // الحصول على مجلد التطبيق
      const appDir = path.dirname(app.getAppPath());

      // نسخ الملفات من المجلد المستخرج إلى مجلد التطبيق
      copyFolderRecursiveSync(extractDir, appDir);

      // إرسال تقدم انتهاء النسخ
      if (progressCallback) {
        progressCallback(90);
      }

      logger.info('تم نسخ ملفات التحديث إلى مجلد التطبيق بنجاح');

      // إعادة تشغيل التطبيق لتطبيق التحديث
      setTimeout(() => {
        app.relaunch();
        app.exit(0);
      }, 3000);

      return {
        success: true,
        message: 'تم نسخ ملفات التحديث بنجاح. سيتم إعادة تشغيل التطبيق لتطبيق التحديث.'
      };
    }
  } catch (error) {
    logger.error('خطأ في تثبيت التحديث من ملف ZIP:', error);
    throw error;
  }
}

/**
 * تثبيت التحديث من ملف EXE
 * @param {string} exeFilePath - مسار ملف EXE
 * @param {Function} progressCallback - دالة لإرسال تقدم التثبيت
 * @returns {Promise<Object>} - نتيجة التثبيت
 */
async function installUpdateFromExe(exeFilePath, progressCallback) {
  try {
    logger.info('بدء تثبيت التحديث من ملف EXE:', exeFilePath);

    // إرسال تقدم البدء
    if (progressCallback) {
      progressCallback(20);
    }

    // تشغيل ملف التثبيت
    exec(`"${exeFilePath}"`, (error, stdout, stderr) => {
      if (error) {
        logger.error('خطأ في تشغيل ملف التثبيت:', error);
      } else {
        logger.info('تم تشغيل ملف التثبيت بنجاح');
      }
    });

    // إرسال تقدم انتهاء التثبيت
    if (progressCallback) {
      progressCallback(90);
    }

    return {
      success: true,
      message: 'تم تشغيل ملف التثبيت بنجاح. يرجى اتباع التعليمات على الشاشة لإكمال التثبيت.',
      setupFilePath: exeFilePath
    };
  } catch (error) {
    logger.error('خطأ في تثبيت التحديث من ملف EXE:', error);
    throw error;
  }
}

/**
 * إنشاء نسخة احتياطية من التطبيق
 * @returns {Promise<string>} - مسار النسخة الاحتياطية
 */
async function createAppBackup() {
  try {
    logger.info('إنشاء نسخة احتياطية من التطبيق...');

    // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجودًا
    if (!fs.existsSync(updatePaths.backupsDir)) {
      fs.mkdirSync(updatePaths.backupsDir, { recursive: true });
    }

    // إنشاء اسم ملف النسخة الاحتياطية بناءً على التاريخ والوقت
    const now = new Date();
    const timestamp = now.toISOString().replace(/:/g, '-').replace(/\..+/, '');
    const backupFileName = `app_backup_${timestamp}.zip`;
    const backupPath = path.join(updatePaths.backupsDir, backupFileName);

    // الحصول على مجلد التطبيق
    const appDir = path.dirname(app.getAppPath());

    // إنشاء ملف ZIP للنسخة الاحتياطية
    const zip = new AdmZip();

    // إضافة ملفات التطبيق إلى ملف ZIP
    addFolderToZip(zip, appDir, '');

    // حفظ ملف ZIP
    zip.writeZip(backupPath);

    logger.info('تم إنشاء نسخة احتياطية من التطبيق بنجاح:', backupPath);

    return backupPath;
  } catch (error) {
    logger.error('خطأ في إنشاء نسخة احتياطية من التطبيق:', error);
    throw error;
  }
}

/**
 * إضافة مجلد إلى ملف ZIP
 * @param {AdmZip} zip - كائن AdmZip
 * @param {string} folderPath - مسار المجلد
 * @param {string} zipPath - مسار داخل ملف ZIP
 */
function addFolderToZip(zip, folderPath, zipPath) {
  const files = fs.readdirSync(folderPath);

  for (const file of files) {
    const filePath = path.join(folderPath, file);
    const stat = fs.statSync(filePath);

    // تجاهل المجلدات والملفات المؤقتة
    if (file === 'node_modules' || file === 'temp' || file.startsWith('.')) {
      continue;
    }

    if (stat.isDirectory()) {
      // إضافة المجلد إلى ملف ZIP
      const newZipPath = path.join(zipPath, file);
      zip.addFile(newZipPath + '/', Buffer.alloc(0));

      // إضافة محتويات المجلد بشكل متكرر
      addFolderToZip(zip, filePath, newZipPath);
    } else {
      // إضافة الملف إلى ملف ZIP
      const fileData = fs.readFileSync(filePath);
      zip.addFile(path.join(zipPath, file), fileData);
    }
  }
}

/**
 * نسخ مجلد بشكل متكرر
 * @param {string} source - مسار المصدر
 * @param {string} target - مسار الهدف
 */
function copyFolderRecursiveSync(source, target) {
  // إنشاء مجلد الهدف إذا لم يكن موجودًا
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target, { recursive: true });
  }

  // قراءة محتويات المجلد
  const files = fs.readdirSync(source);

  // نسخ كل ملف ومجلد
  for (const file of files) {
    const sourcePath = path.join(source, file);
    const targetPath = path.join(target, file);

    // تجاهل المجلدات والملفات المؤقتة
    if (file === 'node_modules' || file === 'temp' || file.startsWith('.')) {
      continue;
    }

    const stat = fs.statSync(sourcePath);

    if (stat.isDirectory()) {
      // نسخ المجلد بشكل متكرر
      copyFolderRecursiveSync(sourcePath, targetPath);
    } else {
      // نسخ الملف
      fs.copyFileSync(sourcePath, targetPath);
    }
  }
}

/**
 * التحقق من وجود تحديثات لقاعدة البيانات
 * @param {Object} options - خيارات التحقق
 * @returns {Promise<Object>} - معلومات تحديث قاعدة البيانات
 */
async function checkDatabaseUpdates(options = {}) {
  try {
    logger.info('التحقق من وجود تحديثات لقاعدة البيانات...');

    // تحديث وقت آخر فحص
    dbUpdateInfo.lastCheckTime = new Date().toISOString();

    // في حالة التحديث اليدوي، نتحقق من وجود ملفات تحديث في مجلد التحديثات
    const updateFiles = fs.readdirSync(updatePaths.dbUpdatesDir)
      .filter(file => file.endsWith('.sql') || file.endsWith('.zip'))
      .map(file => path.join(updatePaths.dbUpdatesDir, file))
      .sort((a, b) => fs.statSync(b).mtime.getTime() - fs.statSync(a).mtime.getTime()); // ترتيب حسب الأحدث

    if (updateFiles.length > 0) {
      const updateFile = updateFiles[0]; // أحدث ملف تحديث

      try {
        // استخراج معلومات الإصدار من اسم الملف
        // نفترض أن اسم الملف يحتوي على رقم الإصدار بتنسيق db-update-X.Y.sql أو db-update-X.Y.zip
        const fileNameMatch = path.basename(updateFile).match(/db-update-(\d+\.\d+)/i);
        const fileVersion = fileNameMatch ? fileNameMatch[1] : null;

        if (fileVersion && fileVersion !== dbUpdateInfo.currentVersion) {
          // قراءة محتوى ملف التحديث للتحقق من التغييرات
          let changes = [];

          if (updateFile.endsWith('.sql')) {
            // قراءة ملف SQL
            const sqlContent = fs.readFileSync(updateFile, 'utf8');

            // استخراج التعليقات من ملف SQL
            const commentRegex = /--\s*(.*)/g;
            let match;
            while ((match = commentRegex.exec(sqlContent)) !== null) {
              changes.push(match[1].trim());
            }
          } else if (updateFile.endsWith('.zip')) {
            // قراءة ملف ZIP
            const zip = new AdmZip(updateFile);
            const changelogEntry = zip.getEntry('changelog.txt');

            if (changelogEntry) {
              const changelogContent = zip.readAsText(changelogEntry);
              changes = changelogContent.split('\n').filter(line => line.trim() !== '');
            }
          }

          dbUpdateInfo.latestVersion = fileVersion;
          dbUpdateInfo.updateAvailable = true;
          dbUpdateInfo.updateFilePath = updateFile;
          dbUpdateInfo.changes = changes.length > 0 ? changes : ['تحديث قاعدة البيانات إلى الإصدار ' + fileVersion];
        } else {
          dbUpdateInfo.latestVersion = dbUpdateInfo.currentVersion;
          dbUpdateInfo.updateAvailable = false;
          dbUpdateInfo.changes = [];
          dbUpdateInfo.updateFilePath = null;
        }
      } catch (err) {
        logger.warn('خطأ في استخراج معلومات الإصدار من ملف تحديث قاعدة البيانات:', err);
        dbUpdateInfo.latestVersion = dbUpdateInfo.currentVersion;
        dbUpdateInfo.updateAvailable = false;
        dbUpdateInfo.changes = [];
        dbUpdateInfo.updateFilePath = null;
      }
    } else {
      dbUpdateInfo.latestVersion = dbUpdateInfo.currentVersion;
      dbUpdateInfo.updateAvailable = false;
      dbUpdateInfo.changes = [];
      dbUpdateInfo.updateFilePath = null;
    }

    // إذا كان هناك تحديث متاح (للاختبار)
    if (options.simulateUpdate) {
      dbUpdateInfo.latestVersion = '1.1';
      dbUpdateInfo.updateAvailable = true;
      dbUpdateInfo.changes = [
        'إضافة جدول جديد للإشعارات',
        'تحسين أداء الاستعلامات',
        'إضافة فهارس جديدة لتحسين السرعة'
      ];
      dbUpdateInfo.updateFilePath = path.join(updatePaths.dbUpdatesDir, 'db-update-1.1.sql');
    }

    logger.info('اكتمل التحقق من وجود تحديثات لقاعدة البيانات:', {
      currentVersion: dbUpdateInfo.currentVersion,
      latestVersion: dbUpdateInfo.latestVersion,
      updateAvailable: dbUpdateInfo.updateAvailable,
      updateFilePath: dbUpdateInfo.updateFilePath
    });

    return {
      currentVersion: dbUpdateInfo.currentVersion,
      latestVersion: dbUpdateInfo.latestVersion,
      changes: dbUpdateInfo.changes,
      updateFilePath: dbUpdateInfo.updateFilePath,
      available: dbUpdateInfo.updateAvailable,
      lastCheckTime: dbUpdateInfo.lastCheckTime
    };
  } catch (error) {
    logger.error('خطأ في التحقق من وجود تحديثات لقاعدة البيانات:', error);
    throw error;
  }
}

/**
 * اختيار ملف تحديث لقاعدة البيانات
 * @returns {Promise<Object>} - معلومات ملف التحديث
 */
async function selectDatabaseUpdateFile() {
  try {
    logger.info('فتح مربع حوار لاختيار ملف تحديث لقاعدة البيانات...');

    const result = await dialog.showOpenDialog({
      title: 'اختر ملف تحديث قاعدة البيانات',
      defaultPath: app.getPath('downloads'),
      filters: [
        { name: 'ملفات تحديث قاعدة البيانات', extensions: ['sql', 'zip'] }
      ],
      properties: ['openFile']
    });

    if (result.canceled || result.filePaths.length === 0) {
      logger.info('تم إلغاء اختيار ملف تحديث قاعدة البيانات');
      return { canceled: true };
    }

    const filePath = result.filePaths[0];
    logger.info('تم اختيار ملف تحديث قاعدة البيانات:', filePath);

    // نسخ ملف التحديث إلى مجلد التحديثات
    const fileName = path.basename(filePath);
    const destPath = path.join(updatePaths.dbUpdatesDir, fileName);

    // نسخ الملف إذا لم يكن موجودًا بالفعل
    if (filePath !== destPath) {
      fs.copyFileSync(filePath, destPath);
      logger.info('تم نسخ ملف تحديث قاعدة البيانات إلى مجلد التحديثات:', destPath);
    }

    // استخراج معلومات الإصدار من اسم الملف
    const fileNameMatch = fileName.match(/db-update-(\d+\.\d+)/i);
    const fileVersion = fileNameMatch ? fileNameMatch[1] : 'غير معروف';

    // قراءة محتوى ملف التحديث للتحقق من التغييرات
    let changes = [];

    if (destPath.endsWith('.sql')) {
      // قراءة ملف SQL
      const sqlContent = fs.readFileSync(destPath, 'utf8');

      // استخراج التعليقات من ملف SQL
      const commentRegex = /--\s*(.*)/g;
      let match;
      while ((match = commentRegex.exec(sqlContent)) !== null) {
        changes.push(match[1].trim());
      }
    } else if (destPath.endsWith('.zip')) {
      // قراءة ملف ZIP
      const zip = new AdmZip(destPath);
      const changelogEntry = zip.getEntry('changelog.txt');

      if (changelogEntry) {
        const changelogContent = zip.readAsText(changelogEntry);
        changes = changelogContent.split('\n').filter(line => line.trim() !== '');
      }
    }

    // تحديث معلومات تحديث قاعدة البيانات
    dbUpdateInfo.latestVersion = fileVersion;
    dbUpdateInfo.updateAvailable = true;
    dbUpdateInfo.updateFilePath = destPath;
    dbUpdateInfo.changes = changes.length > 0 ? changes : ['تحديث قاعدة البيانات إلى الإصدار ' + fileVersion];

    return {
      filePath: destPath,
      fileName: fileName,
      version: fileVersion,
      changes: dbUpdateInfo.changes,
      canceled: false
    };
  } catch (error) {
    logger.error('خطأ في اختيار ملف تحديث لقاعدة البيانات:', error);
    throw error;
  }
}

/**
 * تطبيق تحديثات قاعدة البيانات
 * @param {Function} progressCallback - دالة لإرسال تقدم التطبيق
 * @returns {Promise<Object>} - نتيجة التطبيق
 */
async function applyDatabaseUpdates(progressCallback) {
  try {
    logger.info('بدء تطبيق تحديثات قاعدة البيانات...');

    if (!dbUpdateInfo.updateAvailable || !dbUpdateInfo.updateFilePath) {
      throw new Error('لا توجد تحديثات متاحة لقاعدة البيانات');
    }

    // التحقق من وجود ملف التحديث
    if (!fs.existsSync(dbUpdateInfo.updateFilePath)) {
      throw new Error(`ملف تحديث قاعدة البيانات غير موجود: ${dbUpdateInfo.updateFilePath}`);
    }

    // إنشاء نسخة احتياطية قبل تطبيق التحديثات
    const backupPath = await createDatabaseBackup();
    logger.info('تم إنشاء نسخة احتياطية من قاعدة البيانات قبل التحديث:', backupPath);

    // إرسال تقدم البدء
    if (progressCallback) {
      progressCallback(10);
    }

    // تحديد نوع ملف التحديث
    const isSqlFile = dbUpdateInfo.updateFilePath.toLowerCase().endsWith('.sql');
    const isZipFile = dbUpdateInfo.updateFilePath.toLowerCase().endsWith('.zip');

    let updateResult = null;

    if (isSqlFile) {
      // تطبيق التحديث من ملف SQL
      updateResult = await applyDatabaseUpdateFromSql(dbUpdateInfo.updateFilePath, progressCallback);
    } else if (isZipFile) {
      // تطبيق التحديث من ملف ZIP
      updateResult = await applyDatabaseUpdateFromZip(dbUpdateInfo.updateFilePath, progressCallback);
    } else {
      throw new Error(`نوع ملف تحديث قاعدة البيانات غير مدعوم: ${path.extname(dbUpdateInfo.updateFilePath)}`);
    }

    // تحديث إصدار قاعدة البيانات
    dbUpdateInfo.currentVersion = dbUpdateInfo.latestVersion;
    dbUpdateInfo.updateAvailable = false;

    logger.info('اكتمل تطبيق تحديثات قاعدة البيانات بنجاح:', updateResult);

    // إرسال تقدم الانتهاء
    if (progressCallback) {
      progressCallback(100);
    }

    return {
      success: true,
      message: 'تم تطبيق تحديثات قاعدة البيانات بنجاح',
      newVersion: dbUpdateInfo.currentVersion,
      backupPath: backupPath,
      ...updateResult
    };
  } catch (error) {
    logger.error('خطأ في تطبيق تحديثات قاعدة البيانات:', error);
    throw error;
  }
}

/**
 * تطبيق تحديث قاعدة البيانات من ملف SQL
 * @param {string} sqlFilePath - مسار ملف SQL
 * @param {Function} progressCallback - دالة لإرسال تقدم التطبيق
 * @returns {Promise<Object>} - نتيجة التطبيق
 */
async function applyDatabaseUpdateFromSql(sqlFilePath, progressCallback) {
  try {
    logger.info('بدء تطبيق تحديث قاعدة البيانات من ملف SQL:', sqlFilePath);

    // إرسال تقدم البدء
    if (progressCallback) {
      progressCallback(20);
    }

    // قراءة محتوى ملف SQL
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // تقسيم محتوى ملف SQL إلى أوامر منفصلة
    const sqlCommands = sqlContent
      .replace(/--.*$/gm, '') // إزالة التعليقات
      .split(';')
      .filter(cmd => cmd.trim() !== '');

    // الحصول على مسار قاعدة البيانات
    const dbPath = path.join(app.getPath('userData'), 'wms-database', 'warehouse.db');

    // فتح اتصال بقاعدة البيانات
    const db = new Database(dbPath);

    // تنفيذ الأوامر واحدًا تلو الآخر
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i].trim();

      if (command) {
        try {
          // تنفيذ الأمر
          db.exec(command);

          // إرسال تقدم التنفيذ
          if (progressCallback) {
            const progress = 20 + Math.floor(70 * (i + 1) / sqlCommands.length);
            progressCallback(progress);
          }

          logger.info(`تم تنفيذ الأمر SQL رقم ${i + 1} من ${sqlCommands.length} بنجاح`);
        } catch (err) {
          logger.error(`خطأ في تنفيذ الأمر SQL رقم ${i + 1}:`, err);
          throw err;
        }
      }
    }

    // إغلاق اتصال قاعدة البيانات
    db.close();

    logger.info('تم تطبيق تحديث قاعدة البيانات من ملف SQL بنجاح');

    return {
      success: true,
      message: 'تم تطبيق تحديث قاعدة البيانات من ملف SQL بنجاح',
      commandsExecuted: sqlCommands.length
    };
  } catch (error) {
    logger.error('خطأ في تطبيق تحديث قاعدة البيانات من ملف SQL:', error);
    throw error;
  }
}

/**
 * تطبيق تحديث قاعدة البيانات من ملف ZIP
 * @param {string} zipFilePath - مسار ملف ZIP
 * @param {Function} progressCallback - دالة لإرسال تقدم التطبيق
 * @returns {Promise<Object>} - نتيجة التطبيق
 */
async function applyDatabaseUpdateFromZip(zipFilePath, progressCallback) {
  try {
    logger.info('بدء تطبيق تحديث قاعدة البيانات من ملف ZIP:', zipFilePath);

    // إرسال تقدم البدء
    if (progressCallback) {
      progressCallback(20);
    }

    // إنشاء مجلد مؤقت لاستخراج الملفات
    const extractDir = path.join(updatePaths.tempDir, `db-update-${Date.now()}`);
    if (!fs.existsSync(extractDir)) {
      fs.mkdirSync(extractDir, { recursive: true });
    }

    // استخراج ملف ZIP
    const zip = new AdmZip(zipFilePath);
    zip.extractAllTo(extractDir, true);

    // إرسال تقدم الاستخراج
    if (progressCallback) {
      progressCallback(30);
    }

    logger.info('تم استخراج ملف ZIP إلى:', extractDir);

    // البحث عن ملفات SQL في المجلد المستخرج
    const sqlFiles = fs.readdirSync(extractDir)
      .filter(file => file.toLowerCase().endsWith('.sql'))
      .sort(); // ترتيب الملفات أبجديًا

    if (sqlFiles.length === 0) {
      throw new Error('لم يتم العثور على ملفات SQL في ملف ZIP');
    }

    // الحصول على مسار قاعدة البيانات
    const dbPath = path.join(app.getPath('userData'), 'wms-database', 'warehouse.db');

    // فتح اتصال بقاعدة البيانات
    const db = new Database(dbPath);

    // تنفيذ ملفات SQL واحدًا تلو الآخر
    for (let i = 0; i < sqlFiles.length; i++) {
      const sqlFilePath = path.join(extractDir, sqlFiles[i]);

      try {
        // قراءة محتوى ملف SQL
        const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

        // تقسيم محتوى ملف SQL إلى أوامر منفصلة
        const sqlCommands = sqlContent
          .replace(/--.*$/gm, '') // إزالة التعليقات
          .split(';')
          .filter(cmd => cmd.trim() !== '');

        // تنفيذ الأوامر واحدًا تلو الآخر
        for (let j = 0; j < sqlCommands.length; j++) {
          const command = sqlCommands[j].trim();

          if (command) {
            try {
              // تنفيذ الأمر
              db.exec(command);

              logger.info(`تم تنفيذ الأمر SQL رقم ${j + 1} من ${sqlCommands.length} في الملف ${sqlFiles[i]} بنجاح`);
            } catch (err) {
              logger.error(`خطأ في تنفيذ الأمر SQL رقم ${j + 1} في الملف ${sqlFiles[i]}:`, err);
              throw err;
            }
          }
        }

        // إرسال تقدم التنفيذ
        if (progressCallback) {
          const progress = 30 + Math.floor(60 * (i + 1) / sqlFiles.length);
          progressCallback(progress);
        }

        logger.info(`تم تنفيذ ملف SQL ${sqlFiles[i]} بنجاح`);
      } catch (err) {
        logger.error(`خطأ في تنفيذ ملف SQL ${sqlFiles[i]}:`, err);
        throw err;
      }
    }

    // إغلاق اتصال قاعدة البيانات
    db.close();

    logger.info('تم تطبيق تحديث قاعدة البيانات من ملف ZIP بنجاح');

    return {
      success: true,
      message: 'تم تطبيق تحديث قاعدة البيانات من ملف ZIP بنجاح',
      filesExecuted: sqlFiles.length
    };
  } catch (error) {
    logger.error('خطأ في تطبيق تحديث قاعدة البيانات من ملف ZIP:', error);
    throw error;
  }
}

/**
 * إنشاء نسخة احتياطية من قاعدة البيانات
 * @returns {Promise<string>} - مسار النسخة الاحتياطية
 */
async function createDatabaseBackup() {
  try {
    logger.info('إنشاء نسخة احتياطية من قاعدة البيانات...');

    // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجودًا
    if (!fs.existsSync(updatePaths.backupsDir)) {
      fs.mkdirSync(updatePaths.backupsDir, { recursive: true });
    }

    // إنشاء اسم ملف النسخة الاحتياطية بناءً على التاريخ والوقت
    const now = new Date();
    const timestamp = now.toISOString().replace(/:/g, '-').replace(/\..+/, '');
    const backupFileName = `db_backup_${timestamp}.db`;
    const backupPath = path.join(updatePaths.backupsDir, backupFileName);

    // نسخ ملف قاعدة البيانات
    const dbPath = path.join(app.getPath('userData'), 'wms-database', 'warehouse.db');
    fs.copyFileSync(dbPath, backupPath);

    logger.info('تم إنشاء نسخة احتياطية من قاعدة البيانات بنجاح:', backupPath);

    return backupPath;
  } catch (error) {
    logger.error('خطأ في إنشاء نسخة احتياطية من قاعدة البيانات:', error);
    throw error;
  }
}

/**
 * تعيين إعدادات التحديث التلقائي
 * @param {Object} settings - إعدادات التحديث
 * @returns {Promise<Object>} - نتيجة العملية
 */
async function setAutoUpdateSettings(settings) {
  try {
    logger.info('تعيين إعدادات التحديث التلقائي:', settings);

    // حفظ إعدادات التحديث التلقائي في قاعدة البيانات
    const dbPath = path.join(app.getPath('userData'), 'wms-database', 'warehouse.db');
    const db = new Database(dbPath);

    try {
      // التحقق من وجود جدول الإعدادات
      const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'").get();

      if (!tableExists) {
        // إنشاء جدول الإعدادات إذا لم يكن موجودًا
        db.exec(`
          CREATE TABLE IF NOT EXISTS settings (
            key TEXT PRIMARY KEY,
            value TEXT,
            updated_at TEXT
          )
        `);
      }

      // حفظ إعدادات التحديث التلقائي
      const stmt = db.prepare("INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, ?)");
      stmt.run('autoUpdateEnabled', settings.enabled ? '1' : '0', new Date().toISOString());

      logger.info('تم حفظ إعدادات التحديث التلقائي في قاعدة البيانات');
    } finally {
      // إغلاق اتصال قاعدة البيانات
      db.close();
    }

    return {
      success: true,
      message: 'تم تعيين إعدادات التحديث التلقائي بنجاح',
      settings: settings
    };
  } catch (error) {
    logger.error('خطأ في تعيين إعدادات التحديث التلقائي:', error);
    throw error;
  }
}

/**
 * الحصول على إعدادات التحديث التلقائي
 * @returns {Promise<Object>} - إعدادات التحديث التلقائي
 */
async function getAutoUpdateSettings() {
  try {
    logger.info('الحصول على إعدادات التحديث التلقائي...');

    // الحصول على إعدادات التحديث التلقائي من قاعدة البيانات
    const dbPath = path.join(app.getPath('userData'), 'wms-database', 'warehouse.db');
    const db = new Database(dbPath);

    try {
      // التحقق من وجود جدول الإعدادات
      const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'").get();

      if (!tableExists) {
        // إعادة القيمة الافتراضية إذا لم يكن جدول الإعدادات موجودًا
        return { enabled: false };
      }

      // الحصول على إعدادات التحديث التلقائي
      const row = db.prepare("SELECT value FROM settings WHERE key = 'autoUpdateEnabled'").get();

      return {
        enabled: row ? row.value === '1' : false
      };
    } finally {
      // إغلاق اتصال قاعدة البيانات
      db.close();
    }
  } catch (error) {
    logger.error('خطأ في الحصول على إعدادات التحديث التلقائي:', error);
    return { enabled: false };
  }
}

/**
 * استعادة النسخة الاحتياطية من قاعدة البيانات
 * @param {string} backupPath - مسار النسخة الاحتياطية
 * @returns {Promise<Object>} - نتيجة الاستعادة
 */
async function restoreDatabaseBackup(backupPath) {
  try {
    logger.info('بدء استعادة النسخة الاحتياطية من قاعدة البيانات:', backupPath);

    // التحقق من وجود ملف النسخة الاحتياطية
    if (!fs.existsSync(backupPath)) {
      throw new Error(`ملف النسخة الاحتياطية غير موجود: ${backupPath}`);
    }

    // الحصول على مسار قاعدة البيانات
    const dbPath = path.join(app.getPath('userData'), 'wms-database', 'warehouse.db');

    // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
    const now = new Date();
    const timestamp = now.toISOString().replace(/:/g, '-').replace(/\..+/, '');
    const currentBackupFileName = `db_backup_before_restore_${timestamp}.db`;
    const currentBackupPath = path.join(updatePaths.backupsDir, currentBackupFileName);

    // نسخ ملف قاعدة البيانات الحالية
    fs.copyFileSync(dbPath, currentBackupPath);
    logger.info('تم إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة:', currentBackupPath);

    // استعادة النسخة الاحتياطية
    fs.copyFileSync(backupPath, dbPath);
    logger.info('تم استعادة النسخة الاحتياطية بنجاح');

    return {
      success: true,
      message: 'تم استعادة النسخة الاحتياطية بنجاح',
      backupPath: backupPath,
      currentBackupPath: currentBackupPath
    };
  } catch (error) {
    logger.error('خطأ في استعادة النسخة الاحتياطية من قاعدة البيانات:', error);
    throw error;
  }
}

/**
 * الحصول على قائمة النسخ الاحتياطية
 * @returns {Promise<Array>} - قائمة النسخ الاحتياطية
 */
async function getBackupsList() {
  try {
    logger.info('الحصول على قائمة النسخ الاحتياطية...');

    // التحقق من وجود مجلد النسخ الاحتياطية
    if (!fs.existsSync(updatePaths.backupsDir)) {
      return [];
    }

    // قراءة محتويات مجلد النسخ الاحتياطية
    const files = fs.readdirSync(updatePaths.backupsDir)
      .filter(file => file.endsWith('.db') || file.endsWith('.zip'))
      .map(file => {
        const filePath = path.join(updatePaths.backupsDir, file);
        const stats = fs.statSync(filePath);

        return {
          name: file,
          path: filePath,
          size: stats.size,
          date: stats.mtime,
          type: file.endsWith('.db') ? 'database' : 'application'
        };
      })
      .sort((a, b) => b.date.getTime() - a.date.getTime()); // ترتيب حسب الأحدث

    logger.info(`تم العثور على ${files.length} نسخة احتياطية`);

    return files;
  } catch (error) {
    logger.error('خطأ في الحصول على قائمة النسخ الاحتياطية:', error);
    return [];
  }
}

/**
 * حذف نسخة احتياطية
 * @param {string} backupPath - مسار النسخة الاحتياطية
 * @returns {Promise<Object>} - نتيجة الحذف
 */
async function deleteBackup(backupPath) {
  try {
    logger.info('حذف النسخة الاحتياطية:', backupPath);

    // التحقق من وجود ملف النسخة الاحتياطية
    if (!fs.existsSync(backupPath)) {
      throw new Error(`ملف النسخة الاحتياطية غير موجود: ${backupPath}`);
    }

    // حذف ملف النسخة الاحتياطية
    fs.unlinkSync(backupPath);
    logger.info('تم حذف النسخة الاحتياطية بنجاح');

    return {
      success: true,
      message: 'تم حذف النسخة الاحتياطية بنجاح',
      backupPath: backupPath
    };
  } catch (error) {
    logger.error('خطأ في حذف النسخة الاحتياطية:', error);
    throw error;
  }
}

// تصدير الوظائف
module.exports = {
  // دالة التهيئة
  initialize,

  // وظائف تحديث التطبيق
  checkAppUpdates,
  selectAppUpdateFile,
  downloadAndInstallUpdate,

  // وظائف تحديث قاعدة البيانات
  checkDatabaseUpdates,
  selectDatabaseUpdateFile,
  applyDatabaseUpdates,

  // وظائف النسخ الاحتياطية
  createDatabaseBackup,
  createAppBackup,
  restoreDatabaseBackup,
  getBackupsList,
  deleteBackup,

  // وظائف الإعدادات
  setAutoUpdateSettings,
  getAutoUpdateSettings
};
