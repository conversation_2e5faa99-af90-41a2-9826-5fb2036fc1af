#!/usr/bin/env node

/**
 * اختبار سريع لحالة الخزينة قبل وبعد عمليات الشراء والإلغاء
 */

const Database = require('better-sqlite3');
const path = require('path');

// مسار قاعدة البيانات
const dbPath = path.join(__dirname, 'wms.db');

console.log('💰 اختبار حالة الخزينة...');

try {
  const db = new Database(dbPath);
  
  // الحصول على حالة الخزينة الحالية
  const cashbox = db.prepare('SELECT * FROM cashbox LIMIT 1').get();
  
  if (!cashbox) {
    console.log('❌ لا توجد خزينة في النظام');
    process.exit(1);
  }
  
  console.log('\n📊 حالة الخزينة الحالية:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log(`🏦 الرصيد الافتتاحي: ${cashbox.initial_balance || 0}`);
  console.log(`💵 الرصيد الحالي: ${cashbox.current_balance || 0}`);
  console.log(`📈 إجمالي المبيعات: ${cashbox.sales_total || 0}`);
  console.log(`📉 إجمالي المشتريات: ${cashbox.purchases_total || 0}`);
  console.log(`🔄 إجمالي المرتجعات: ${cashbox.returns_total || 0}`);
  console.log(`🚚 إجمالي مصاريف النقل: ${cashbox.transport_total || 0}`);
  console.log(`💰 إجمالي الأرباح: ${cashbox.profit_total || 0}`);
  console.log(`⏰ آخر تحديث: ${cashbox.updated_at || 'غير محدد'}`);
  
  // حساب الرصيد المتوقع
  const expectedBalance = (cashbox.initial_balance || 0) + 
                         (cashbox.sales_total || 0) - 
                         (cashbox.purchases_total || 0) - 
                         (cashbox.returns_total || 0) - 
                         (cashbox.transport_total || 0);
  
  console.log('\n🧮 التحقق من صحة الحسابات:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log(`📊 الرصيد المحسوب: ${expectedBalance}`);
  console.log(`💵 الرصيد الفعلي: ${cashbox.current_balance || 0}`);
  
  const balanceDifference = (cashbox.current_balance || 0) - expectedBalance;
  if (Math.abs(balanceDifference) < 0.01) {
    console.log('✅ الحسابات صحيحة');
  } else {
    console.log(`❌ يوجد فرق في الحسابات: ${balanceDifference}`);
  }
  
  // الحصول على آخر المعاملات
  console.log('\n📋 آخر 5 معاملات شراء:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  const recentPurchases = db.prepare(`
    SELECT t.*, i.name as item_name,
           tc.cancelled_at,
           CASE 
             WHEN tc.id IS NOT NULL THEN 'ملغاة'
             ELSE 'نشطة'
           END as status
    FROM transactions t
    LEFT JOIN items i ON t.item_id = i.id
    LEFT JOIN transaction_cancellations tc ON t.id = tc.transaction_id
    WHERE t.transaction_type = 'purchase'
    ORDER BY t.transaction_date DESC
    LIMIT 5
  `).all();
  
  if (recentPurchases.length > 0) {
    recentPurchases.forEach((purchase, index) => {
      console.log(`${index + 1}. ID: ${purchase.id} | ${purchase.item_name} | الكمية: ${purchase.quantity} | السعر: ${purchase.total_price} | النقل: ${purchase.transport_cost || 0} | الحالة: ${purchase.status}`);
    });
  } else {
    console.log('لا توجد معاملات شراء');
  }
  
  // الحصول على آخر معاملات الخزينة
  console.log('\n💳 آخر 5 معاملات خزينة:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  const recentCashboxTransactions = db.prepare(`
    SELECT * FROM cashbox_transactions
    ORDER BY created_at DESC
    LIMIT 5
  `).all();
  
  if (recentCashboxTransactions.length > 0) {
    recentCashboxTransactions.forEach((transaction, index) => {
      console.log(`${index + 1}. ${transaction.type} | ${transaction.amount} | ${transaction.source} | ${transaction.notes}`);
    });
  } else {
    console.log('لا توجد معاملات خزينة');
  }
  
  // إحصائيات سريعة
  console.log('\n📈 إحصائيات سريعة:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  const totalTransactions = db.prepare('SELECT COUNT(*) as count FROM transactions').get();
  const activePurchases = db.prepare(`
    SELECT COUNT(*) as count FROM transactions t
    LEFT JOIN transaction_cancellations tc ON t.id = tc.transaction_id
    WHERE t.transaction_type = 'purchase' AND tc.id IS NULL
  `).get();
  const cancelledPurchases = db.prepare(`
    SELECT COUNT(*) as count FROM transactions t
    INNER JOIN transaction_cancellations tc ON t.id = tc.transaction_id
    WHERE t.transaction_type = 'purchase'
  `).get();
  
  console.log(`📊 إجمالي المعاملات: ${totalTransactions.count}`);
  console.log(`✅ فواتير الشراء النشطة: ${activePurchases.count}`);
  console.log(`❌ فواتير الشراء الملغاة: ${cancelledPurchases.count}`);
  
  db.close();
  
  console.log('\n🎯 نصائح للاختبار:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('1. قم بإجراء عملية شراء جديدة وراقب تغيير الأرقام');
  console.log('2. ألغِ فاتورة شراء وتأكد من عكس التأثير');
  console.log('3. تحقق من أن مصاريف النقل تُحسب بشكل صحيح');
  console.log('4. راقب وحدة التحكم للرسائل التشخيصية');
  
  console.log('\n✅ تم إنهاء فحص حالة الخزينة بنجاح!');
  
} catch (error) {
  console.error('❌ خطأ في فحص حالة الخزينة:', error.message);
  process.exit(1);
}
