@echo off
chcp 65001 >nul
echo ========================================
echo تجميع التطبيق للبيئة الإنتاجية
echo Warehouse Management System v1.1.1
echo ========================================
echo.

REM الانتقال لمجلد المشروع
cd /d "%~dp0"

REM التحقق من Node.js
echo 🔍 التحقق من Node.js...
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Node.js غير متوفر
    echo يرجى تثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
node --version
npm --version
echo.

REM تنظيف الملفات القديمة
echo 🧹 تنظيف الملفات القديمة...
if exist "bundle.js" del "bundle.js"
if exist "bundle.js.map" del "bundle.js.map"
if exist "dist" rmdir /s /q "dist"
if exist "dist-final" rmdir /s /q "dist-final"
echo ✅ تم التنظيف
echo.

REM تثبيت/تحديث Dependencies
echo 📦 تثبيت Dependencies...
npm install
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تثبيت Dependencies
    pause
    exit /b 1
)
echo ✅ تم تثبيت Dependencies
echo.

REM تطبيق ترقية قاعدة البيانات
echo 🗄️ تطبيق ترقية قاعدة البيانات...
node simple-db-migration.js
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم تطبيق ترقية قاعدة البيانات
) else (
    echo ⚠️ تحذير: قد تحتاج لتطبيق ترقية قاعدة البيانات يدوياً
    echo استخدم SQLite Browser مع ملف manual-db-migration.sql
)
echo.

REM بناء التطبيق باستخدام Webpack
echo 📦 بناء التطبيق باستخدام Webpack (Production Mode)...
npx webpack --mode production --progress
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء Webpack
    echo جرب: node node_modules\webpack\bin\webpack.js --mode production
    pause
    exit /b 1
)
echo ✅ تم بناء Webpack بنجاح
echo.

REM التحقق من الملفات المبنية
echo 🔍 التحقق من الملفات المبنية...
if not exist "bundle.js" (
    echo ❌ bundle.js غير موجود
    pause
    exit /b 1
)
echo ✅ bundle.js موجود
echo.

REM بناء التطبيق باستخدام Electron Builder
echo 🏗️ بناء التطبيق باستخدام Electron Builder...
npm run dist
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء Electron Builder
    echo جرب: npx electron-builder
    pause
    exit /b 1
)
echo ✅ تم بناء التطبيق بنجاح
echo.

REM عرض النتائج
echo 🎉 تم تجميع التطبيق بنجاح!
echo.
echo 📁 الملفات المبنية:
if exist "dist-final" (
    echo   ✅ مجلد dist-final
    if exist "dist-final\win-unpacked" echo   ✅ التطبيق القابل للتشغيل
    if exist "dist-final\*.exe" echo   ✅ ملف المثبت
)
if exist "bundle.js" echo   ✅ bundle.js
if exist "bundle.js.map" echo   ✅ bundle.js.map
echo.

echo 📋 الميزات الجديدة المضافة:
echo   ✅ إلغاء فواتير الشراء
echo   ✅ سجل تدقيق المعاملات
echo   ✅ فلترة حسب حالة الفاتورة
echo   ✅ صلاحيات المستخدمين
echo   ✅ حوار تأكيد الإلغاء
echo.

echo 🚀 يمكنك الآن:
echo   1. تشغيل التطبيق من: dist-final\win-unpacked\
echo   2. توزيع ملف المثبت: dist-final\*.exe
echo   3. اختبار الميزات الجديدة
echo.

echo 📝 ملاحظات مهمة:
echo   - تأكد من عمل نسخة احتياطية من قاعدة البيانات
echo   - اختبر الميزات الجديدة قبل النشر
echo   - راجع دليل الاختبار في QUICK_TEST_STEPS.md
echo.

pause
