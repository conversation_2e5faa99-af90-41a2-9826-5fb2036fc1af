# دليل نشر نظام إدارة المخازن

## نظرة عامة

هذا الدليل يوضح كيفية إنشاء وتوزيع ملفات التثبيت لنظام إدارة المخازن على أجهزة العملاء.

## الملفات الناتجة من البناء

بعد تشغيل `npm run build-installer` بنجاح، ستحصل على الملفات التالية في مجلد `dist/`:

### 1. مثبت NSIS (مُوصى به للتوزيع)
```
نظام-إدارة-المخازن-1.1.1-installer.exe
```
- **الحجم**: ~150-200 MB
- **النوع**: مثبت تفاعلي مع واجهة مستخدم
- **الميزات**:
  - ✅ واجهة تثبيت سهلة الاستخدام
  - ✅ إنشاء اختصارات تلقائياً
  - ✅ تسجيل في قائمة البرامج
  - ✅ إمكانية إلغاء التثبيت
  - ✅ إعداد قاعدة البيانات تلقائياً

### 2. نسخة محمولة
```
نظام-إدارة-المخازن-1.1.1-portable.exe
```
- **الحجم**: ~150-200 MB
- **النوع**: تطبيق محمول لا يحتاج تثبيت
- **الميزات**:
  - ✅ تشغيل مباشر بدون تثبيت
  - ✅ يمكن تشغيله من USB
  - ✅ لا يترك آثار في النظام
  - ⚠️ يحتاج إعداد يدوي لقاعدة البيانات

### 3. مجلد التطبيق (للمطورين)
```
win-unpacked/
```
- مجلد يحتوي على جميع ملفات التطبيق غير المضغوطة
- مفيد للاختبار والتطوير

## خطوات التوزيع

### للعملاء العاديين (مُوصى به):

1. **إرسال مثبت NSIS**:
   ```
   نظام-إدارة-المخازن-1.1.1-installer.exe
   ```

2. **تعليمات للعميل**:
   - انقر نقراً مزدوجاً على الملف
   - اتبع خطوات المثبت
   - اختر مجلد التثبيت (اختياري)
   - انتظر انتهاء التثبيت
   - شغل التطبيق من اختصار سطح المكتب

### للاستخدام المحمول:

1. **إرسال النسخة المحمولة**:
   ```
   نظام-إدارة-المخازن-1.1.1-portable.exe
   ```

2. **تعليمات للعميل**:
   - ضع الملف في مجلد منفصل
   - انقر نقراً مزدوجاً لتشغيل التطبيق
   - سيتم إنشاء قاعدة البيانات في نفس المجلد

## متطلبات النظام للعملاء

### الحد الأدنى:
- **نظام التشغيل**: Windows 10 (64-bit)
- **الذاكرة**: 4 GB RAM
- **المساحة**: 500 MB مساحة فارغة
- **المعالج**: Intel/AMD 64-bit

### مُوصى به:
- **نظام التشغيل**: Windows 11 (64-bit)
- **الذاكرة**: 8 GB RAM
- **المساحة**: 2 GB مساحة فارغة
- **دقة الشاشة**: 1920x1080

## مواقع الملفات بعد التثبيت

### التطبيق الرئيسي:
```
C:\Users\<USER>\AppData\Local\Programs\نظام إدارة المخازن\
```

### قاعدة البيانات:
```
C:\Users\<USER>\AppData\Roaming\warehouse-management-system\wms-database\
```

### النسخ الاحتياطية:
```
C:\Users\<USER>\AppData\Roaming\warehouse-management-system\wms-database\backups\
```

### السجلات:
```
C:\Users\<USER>\AppData\Roaming\warehouse-management-system\logs\
```

## استكشاف أخطاء التثبيت

### مشاكل شائعة:

#### 1. "Windows protected your PC"
**الحل**:
- انقر على "More info"
- انقر على "Run anyway"
- أو: انقر بالزر الأيمن → Properties → Unblock

#### 2. "App can't run on this PC"
**الحل**:
- تأكد من أن النظام 64-bit
- تحديث Windows إلى أحدث إصدار

#### 3. مشاكل الصلاحيات
**الحل**:
- انقر بالزر الأيمن على المثبت
- اختر "Run as administrator"

#### 4. مكافح الفيروسات يحجب التطبيق
**الحل**:
- أضف التطبيق لقائمة الاستثناءات
- أو عطل مكافح الفيروسات مؤقتاً أثناء التثبيت

## التحديثات

### لإصدار تحديث جديد:

1. **زيادة رقم الإصدار** في `package.json`:
   ```json
   "version": "1.1.2"
   ```

2. **إعادة البناء**:
   ```bash
   npm run build-installer
   ```

3. **توزيع الملف الجديد** على العملاء

### للعملاء الحاليين:
- يمكن تثبيت الإصدار الجديد فوق القديم
- ستبقى البيانات محفوظة
- يُنصح بعمل نسخة احتياطية قبل التحديث

## الأمان

### التوقيع الرقمي (اختياري):
لتجنب تحذيرات Windows، يمكن توقيع الملفات رقمياً:

1. **الحصول على شهادة رقمية** من جهة معتمدة
2. **إضافة الشهادة** لإعدادات البناء:
   ```json
   "win": {
     "certificateFile": "path/to/certificate.p12",
     "certificatePassword": "password"
   }
   ```

### فحص الفيروسات:
- فحص الملفات قبل التوزيع
- رفع عينة لـ VirusTotal للتأكد

## النسخ الاحتياطية والاستعادة

### للعملاء:
1. **النسخ الاحتياطية التلقائية** تتم يومياً
2. **النسخ اليدوية** من قائمة الإعدادات
3. **الاستعادة** من نفس القائمة

### للمطورين:
- احتفظ بنسخة من ملفات البناء
- وثق التغييرات في كل إصدار
- اختبر التثبيت على أجهزة مختلفة

## الدعم الفني

### معلومات مفيدة للدعم:
- رقم إصدار التطبيق
- نظام التشغيل
- رسائل الخطأ (إن وجدت)
- ملفات السجل

### ملفات السجل المهمة:
```
%APPDATA%\warehouse-management-system\logs\errors.log
%APPDATA%\warehouse-management-system\logs\system.log
```

## قائمة التحقق قبل التوزيع

- [ ] اختبار التثبيت على جهاز نظيف
- [ ] اختبار جميع الوظائف الأساسية
- [ ] التأكد من عمل قاعدة البيانات
- [ ] فحص الفيروسات
- [ ] اختبار إلغاء التثبيت
- [ ] توثيق التغييرات الجديدة
- [ ] إعداد تعليمات للعملاء
