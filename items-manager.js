/**
 * وحدة إدارة الأصناف الجديدة
 * تقوم هذه الوحدة بإدارة جميع عمليات الأصناف بشكل موحد
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

// مرجع لقاعدة البيانات (سيتم الحصول عليه من مدير قاعدة البيانات)
let db = null;

/**
 * تهيئة وحدة إدارة الأصناف
 */
function initialize() {
  try {
    logSystem('جاري تهيئة وحدة إدارة الأصناف الجديدة...', 'info');

    // الحصول على اتصال قاعدة البيانات من مدير قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    logSystem('تم تهيئة وحدة إدارة الأصناف الجديدة بنجاح', 'info');
    return true;
  } catch (error) {
    logError(error, 'initialize - new-items-manager');
    return false;
  }
}

// تخزين مؤقت للأصناف
let itemsCache = null;
let itemsCacheTimestamp = 0;
const ITEMS_CACHE_EXPIRY = 5 * 60 * 1000; // 5 دقائق

/**
 * الحصول على جميع الأصناف
 * @param {boolean} forceRefresh - إجبار تحديث البيانات من قاعدة البيانات
 * @returns {Array} - قائمة الأصناف
 */
function getAllItems(forceRefresh = false) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من وجود البيانات في التخزين المؤقت
    if (!forceRefresh &&
        itemsCache &&
        (Date.now() - itemsCacheTimestamp < ITEMS_CACHE_EXPIRY)) {
      // تسجيل فقط إذا كان هناك تحديث إجباري
      if (forceRefresh) {
        logSystem(`استخدام بيانات الأصناف من التخزين المؤقت (${itemsCache.length} صنف)`, 'info');
      }
      return itemsCache;
    }

    // تسجيل فقط إذا كان هناك تحديث إجباري
    if (forceRefresh) {
      logSystem(`جلب بيانات الأصناف من قاعدة البيانات`, 'info');
    }

    // استعلام محسن للحصول على الأصناف مع معلومات المخزون
    const stmt = db.prepare(`
      SELECT
        i.id,
        i.name,
        i.unit,
        inv.current_quantity,
        inv.minimum_quantity,
        inv.avg_price,
        inv.last_purchase_price,
        inv.selling_price,
        inv.last_updated
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      ORDER BY i.name
    `);

    const items = stmt.all();

    // تحديث التخزين المؤقت
    itemsCache = items;
    itemsCacheTimestamp = Date.now();

    // تسجيل عدد النتائج للتشخيص (فقط إذا كان هناك تحديث إجباري)
    if (forceRefresh) {
      logSystem(`تم استرجاع ${items.length} صنف من قاعدة البيانات`, 'info');
    }

    return items;
  } catch (error) {
    logError(error, 'getAllItems');
    return [];
  }
}

/**
 * الحصول على صنف بواسطة المعرف
 * @param {number} itemId - معرف الصنف
 * @returns {Object|null} - بيانات الصنف
 */
function getItemById(itemId) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    const numericItemId = Number(itemId);
    if (isNaN(numericItemId) || numericItemId <= 0) {
      throw new Error(`معرف الصنف غير صالح: ${itemId}`);
    }

    // استعلام محسن للحصول على الصنف مع معلومات المخزون
    const stmt = db.prepare(`
      SELECT
        i.id,
        i.name,
        i.unit,
        inv.current_quantity,
        inv.minimum_quantity,
        inv.avg_price,
        inv.selling_price,
        inv.last_updated
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      WHERE i.id = ?
    `);

    const item = stmt.get(numericItemId);

    // تسجيل نتيجة البحث للتشخيص
    if (item) {
      logSystem(`تم العثور على الصنف بالمعرف: ${numericItemId}`, 'info');
    } else {
      logSystem(`لم يتم العثور على الصنف بالمعرف: ${numericItemId}`, 'warning');
    }

    return item || null;
  } catch (error) {
    logError(error, 'getItemById');
    return null;
  }
}

/**
 * البحث عن الأصناف
 * @param {string} searchTerm - مصطلح البحث
 * @returns {Array} - قائمة الأصناف المطابقة
 */
function searchItems(searchTerm) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    if (!searchTerm || searchTerm.trim() === '') {
      return getAllItems();
    }

    // استعلام محسن للبحث عن الأصناف مع معلومات المخزون
    const stmt = db.prepare(`
      SELECT
        i.id,
        i.name,
        i.unit,
        inv.current_quantity,
        inv.minimum_quantity,
        inv.avg_price,
        inv.last_purchase_price,
        inv.selling_price,
        inv.last_updated
      FROM items i
      LEFT JOIN inventory inv ON i.id = inv.item_id
      WHERE i.name LIKE ?
      ORDER BY i.name
    `);

    const items = stmt.all(`%${searchTerm}%`);

    // تسجيل عدد النتائج للتشخيص
    logSystem(`تم العثور على ${items.length} صنف مطابق لمصطلح البحث: ${searchTerm}`, 'info');

    return items;
  } catch (error) {
    logError(error, 'searchItems');
    return [];
  }
}

/**
 * إضافة صنف جديد
 * @param {Object} item - بيانات الصنف
 * @returns {Object} - نتيجة العملية
 */
function addItem(item) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحقق من صحة البيانات
    if (!item.name || item.name.trim() === '') {
      return { success: false, error: 'اسم الصنف مطلوب' };
    }

    // استدعاء نظام الأحداث
    const eventSystem = require('./event-system');

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من عدم وجود صنف بنفس الاسم
      const checkStmt = db.prepare('SELECT COUNT(*) as count FROM items WHERE name = ?');
      const { count } = checkStmt.get(item.name);

      if (count > 0) {
        throw new Error('يوجد صنف بنفس الاسم بالفعل');
      }

      // إضافة الصنف
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT INTO items (name, unit)
        VALUES (?, ?)
      `);

      const result = insertStmt.run(
        item.name,
        item.unit || 'قطعة'
      );

      const itemId = result.lastInsertRowid;

      // الحصول على الصنف المضاف
      const getItemStmt = db.prepare('SELECT id, name, unit FROM items WHERE id = ?');
      const newItem = getItemStmt.get(itemId);

      // إضافة سجل في المخزون للصنف الجديد
      const initialQuantity = item.initial_quantity ? parseInt(item.initial_quantity) : 0;
      const avgPrice = item.avg_price ? parseFloat(item.avg_price) : 0;
      const sellingPrice = item.selling_price ? parseFloat(item.selling_price) : 0;
      const minimumQuantity = item.minimum_quantity ? parseInt(item.minimum_quantity) : 0;

      const insertInventoryStmt = db.prepare(`
        INSERT INTO inventory (
          item_id, current_quantity, minimum_quantity, avg_price, last_purchase_price, selling_price, last_updated
        )
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      insertInventoryStmt.run(
        itemId,
        initialQuantity,
        minimumQuantity,
        avgPrice,
        0, // last_purchase_price - يبدأ بصفر للأصناف الجديدة
        sellingPrice,
        now
      );

      logSystem(`تم إضافة سجل في المخزون للصنف الجديد: ${newItem.name} (${newItem.id})`, 'info');

      // إرسال إشعار بإضافة صنف جديد
      logSystem(`إرسال إشعار بإضافة صنف جديد: ${newItem.name} (${newItem.id})`, 'info');
      eventSystem.notifyItemAdded({
        id: newItem.id,
        name: newItem.name,
        unit: newItem.unit,
        initial_quantity: initialQuantity,
        avg_price: avgPrice,
        selling_price: sellingPrice,
        minimum_quantity: minimumQuantity,
        operation: 'add-item'
      });

      // إرسال إشعار بتحديث المخزون
      eventSystem.notifyInventoryUpdated({
        itemId: itemId,
        name: newItem.name,
        current_quantity: initialQuantity,
        avg_price: avgPrice,
        selling_price: sellingPrice,
        minimum_quantity: minimumQuantity,
        operation: 'add-item'
      });

      // مسح التخزين المؤقت للأصناف
      clearItemsCache('add-item', itemId);

      // إرسال إشعار بالحاجة للتحديث
      eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
        target: 'items',
        timestamp: new Date().toISOString()
      }, true);

      return {
        success: true,
        item: {
          ...newItem,
          initial_quantity: initialQuantity,
          avg_price: avgPrice,
          selling_price: sellingPrice,
          minimum_quantity: minimumQuantity
        }
      };
    })();
  } catch (error) {
    logError(error, 'addItem');
    return { success: false, error: error.message };
  }
}

/**
 * تحديث صنف موجود
 * @param {number} itemId - معرف الصنف
 * @param {Object} updates - التحديثات المطلوبة
 * @param {string} userRole - دور المستخدم الحالي
 * @returns {Object} - نتيجة العملية
 */
function updateItem(itemId, updates, userRole) {
  try {
    logSystem(`محاولة تحديث الصنف بالمعرف: ${itemId} بواسطة مستخدم بدور: ${userRole}`, 'info');
    logSystem(`بيانات التحديث: ${JSON.stringify(updates)}`, 'info');

    // التحقق من صلاحيات المستخدم
    if (userRole === 'viewer') {
      logSystem(`رفض تحديث الصنف: المستخدم بدور مشاهد لا يملك الصلاحية`, 'warning');
      return {
        success: false,
        error: 'ليس لديك صلاحية لتعديل الأصناف. المشاهد يمكنه فقط عرض البيانات.'
      };
    }

    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحويل إلى رقم
    const numericItemId = Number(itemId);
    if (isNaN(numericItemId) || numericItemId <= 0) {
      return { success: false, error: `معرف الصنف غير صالح: ${itemId}` };
    }

    // التحقق من وجود تحديثات
    if (!updates) {
      logSystem('لم يتم تحديد أي تحديثات (updates هو null أو undefined)', 'error');

      // إنشاء كائن تحديثات افتراضي بدلاً من إرجاع خطأ
      updates = {};
      logSystem('تم إنشاء كائن تحديثات افتراضي فارغ', 'info');
    }

    // التأكد من أن التحديثات هي كائن وليست قيمة فارغة
    if (typeof updates !== 'object' || Array.isArray(updates)) {
      logSystem(`نوع التحديثات غير صالح: ${typeof updates}، تحويل إلى كائن`, 'warning');

      // تحويل التحديثات إلى كائن إذا لم تكن كائنًا
      updates = {};
    }

    // التأكد من أن التحديثات تحتوي على حقول
    if (Object.keys(updates).length === 0) {
      logSystem('كائن التحديثات فارغ، سيتم استخدام القيم الحالية', 'warning');
      // سنستمر في العملية ونستخدم القيم الحالية
    }

    logSystem(`نوع التحديثات: ${typeof updates}`, 'info');
    logSystem(`محتوى التحديثات: ${JSON.stringify(updates)}`, 'info');

    // الحصول على الصنف الحالي من قاعدة البيانات
    const currentItemStmt = db.prepare('SELECT * FROM items WHERE id = ?');
    const currentItem = currentItemStmt.get(numericItemId);

    if (!currentItem) {
      logSystem(`الصنف غير موجود: ${numericItemId}`, 'error');
      return { success: false, error: `الصنف غير موجود: ${numericItemId}` };
    }

    // إضافة القيم الحالية إلى التحديثات إذا لم تكن موجودة
    if (!updates.name || updates.name.trim() === '') {
      updates.name = currentItem.name;
      logSystem(`استخدام الاسم الحالي كتحديث: ${updates.name}`, 'info');
    }

    if (!updates.unit || updates.unit.trim() === '') {
      updates.unit = currentItem.unit || 'قطعة';
      logSystem(`استخدام وحدة القياس الحالية كتحديث: ${updates.unit}`, 'info');
    }

    // تأكد من أن القيم ليست فارغة
    updates.name = updates.name.trim();
    updates.unit = updates.unit.trim();

    // إذا كانت القيم فارغة بعد التنظيف، استخدم القيم الافتراضية
    if (updates.name === '') {
      updates.name = currentItem.name || 'صنف بدون اسم';
    }

    if (updates.unit === '') {
      updates.unit = currentItem.unit || 'قطعة';
    }

    logSystem(`التحديثات النهائية: ${JSON.stringify(updates)}`, 'info');

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // التحقق من وجود الصنف
      const checkItemStmt = db.prepare('SELECT * FROM items WHERE id = ?');
      const item = checkItemStmt.get(numericItemId);

      if (!item) {
        logSystem(`الصنف غير موجود: ${numericItemId}`, 'error');
        throw new Error(`الصنف غير موجود: ${numericItemId}`);
      }

      logSystem(`تم العثور على الصنف: ${JSON.stringify(item)}`, 'info');

      // التحقق من عدم وجود صنف آخر بنفس الاسم
      if (updates.name && updates.name !== item.name) {
        const checkNameStmt = db.prepare('SELECT COUNT(*) as count FROM items WHERE name = ? AND id != ?');
        const { count } = checkNameStmt.get(updates.name, numericItemId);

        if (count > 0) {
          logSystem(`يوجد صنف آخر بنفس الاسم: ${updates.name}`, 'error');
          throw new Error('يوجد صنف آخر بنفس الاسم بالفعل');
        }
      }

      // تحديث الصنف
      let updateFields = [];
      let updateParams = [];

      // إضافة الاسم إذا كان موجودًا
      if (updates.name !== undefined) {
        updateFields.push('name = ?');
        updateParams.push(updates.name);
        logSystem(`إضافة تحديث الاسم: ${updates.name}`, 'info');
      }

      // إضافة وحدة القياس إذا كانت موجودة
      if (updates.unit !== undefined) {
        updateFields.push('unit = ?');
        updateParams.push(updates.unit);
        logSystem(`إضافة تحديث وحدة القياس: ${updates.unit}`, 'info');
      }

      // إضافة حقل updated_at
      updateFields.push('updated_at = ?');
      updateParams.push(new Date().toISOString());
      logSystem(`إضافة تحديث وقت التحديث: ${new Date().toISOString()}`, 'info');

      // إضافة معرف الصنف كمعلمة أخيرة
      updateParams.push(numericItemId);

      // التحقق من وجود حقول للتحديث
      if (updateFields.length === 0) {
        logSystem(`لا توجد حقول للتحديث`, 'warning');
        throw new Error('لا توجد حقول للتحديث');
      }

      logSystem(`تم إضافة حقول التحديث: ${updateFields.join(', ')}`, 'info');

      // بناء استعلام التحديث
      const updateQuery = `
        UPDATE items
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      logSystem(`استعلام التحديث: ${updateQuery}`, 'info');
      logSystem(`معلمات التحديث: ${JSON.stringify(updateParams)}`, 'info');

      // تنفيذ استعلام التحديث
      let updateResult;
      try {
        const updateStmt = db.prepare(updateQuery);
        updateResult = updateStmt.run(...updateParams);
        logSystem(`نتيجة التحديث: ${JSON.stringify(updateResult)}`, 'info');
      } catch (sqlError) {
        logError(sqlError, 'updateItem - SQL error');
        throw new Error(`فشل في تنفيذ استعلام التحديث: ${sqlError.message}`);
      }

      // التحقق من نجاح التحديث
      if (!updateResult) {
        logSystem(`فشل في تنفيذ استعلام التحديث`, 'warning');
        throw new Error('فشل في تنفيذ استعلام التحديث');
      }

      // إذا كانت البيانات لم تتغير، لا نعتبر ذلك خطأ
      if (updateResult.changes === 0) {
        logSystem(`لم تتغير البيانات (لم يتم تحديث أي سجل)`, 'info');
        // لا نرمي خطأ هنا، بل نستمر في العملية
      }

      // الحصول على الصنف المحدث
      const getItemStmt = db.prepare('SELECT id, name, unit FROM items WHERE id = ?');
      const updatedItem = getItemStmt.get(numericItemId);

      if (!updatedItem) {
        logSystem(`لم يتم العثور على الصنف بعد التحديث: ${numericItemId}`, 'error');
        throw new Error('لم يتم العثور على الصنف بعد التحديث');
      }

      logSystem(`الصنف بعد التحديث: ${JSON.stringify(updatedItem)}`, 'info');

      // استدعاء نظام الأحداث لإرسال إشعار بتحديث الصنف
      try {
        const eventSystem = require('./event-system');

        // استخدام دالة notifyItemUpdated لإرسال إشعار بتحديث الصنف
        eventSystem.notifyItemUpdated({
          id: numericItemId,
          name: updatedItem.name,
          unit: updatedItem.unit,
          timestamp: new Date().toISOString()
        });

        // إرسال إشعار إضافي بالحاجة للتحديث مع تجاهل التخزين المؤقت
        eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
          target: 'items',
          operation: 'update-item',
          itemId: numericItemId,
          timestamp: new Date().toISOString()
        }, true);

        // إرسال إشعار إضافي بعد فترة قصيرة للتأكد من تحديث واجهة المستخدم
        setTimeout(() => {
          eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
            target: 'items',
            operation: 'update-item-delayed',
            itemId: numericItemId,
            timestamp: new Date().toISOString()
          }, true);
        }, 1000);

        logSystem(`تم إرسال إشعار بتحديث الصنف: ${numericItemId}`, 'info');
      } catch (eventError) {
        logError(eventError, 'updateItem - event system');
        // لا نريد إيقاف العملية إذا فشل إرسال الإشعار
      }

      // مسح التخزين المؤقت للأصناف
      clearItemsCache('update-item', numericItemId);

      return {
        success: true,
        item: updatedItem
      };
    })();
  } catch (error) {
    logError(error, 'updateItem');
    return { success: false, error: error.message };
  }
}

/**
 * حذف صنف
 * @param {number} itemId - معرف الصنف
 * @param {boolean} forceDelete - إجبار الحذف حتى مع وجود معاملات بيع (للمدير فقط)
 * @param {string} userRole - دور المستخدم الحالي
 * @returns {Object} - نتيجة العملية
 */
function deleteItem(itemId, forceDelete = false, userRole) {
  try {
    logSystem(`محاولة حذف الصنف بالمعرف: ${itemId} بواسطة مستخدم بدور: ${userRole}`, 'info');

    // التحقق من صلاحيات المستخدم
    if (userRole === 'viewer' || userRole === 'employee') {
      logSystem(`رفض حذف الصنف: المستخدم بدور ${userRole} لا يملك الصلاحية`, 'warning');
      return {
        success: false,
        error: `ليس لديك صلاحية لحذف الأصناف. المستخدم بدور ${userRole} لا يمكنه حذف الأصناف.`
      };
    }

    // إذا كان المستخدم مديرًا، يمكنه حذف الأصناف المحمية
    if (userRole === 'admin') {
      logSystem(`المستخدم مدير ويمكنه حذف الأصناف المحمية`, 'info');
      forceDelete = true;
    }

    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحويل إلى رقم
    const numericItemId = Number(itemId);
    if (isNaN(numericItemId) || numericItemId <= 0) {
      logSystem(`معرف الصنف غير صالح: ${itemId}`, 'error');
      return { success: false, error: `معرف الصنف غير صالح: ${itemId}` };
    }

    // تسجيل معلومات عن عملية الحذف
    logSystem(`محاولة حذف الصنف: ${numericItemId}, forceDelete: ${forceDelete}`, 'info');
    logSystem(`نوع معلمة forceDelete: ${typeof forceDelete}, القيمة: ${forceDelete}`, 'info');

    // تحويل معلمة forceDelete إلى قيمة منطقية صريحة
    forceDelete = forceDelete === true;
    logSystem(`معلمة forceDelete بعد التحويل: ${forceDelete}`, 'info');

    // بدء معاملة قاعدة البيانات
    try {
      return db.transaction(() => {
        // التحقق من وجود الصنف
        const checkItemStmt = db.prepare('SELECT * FROM items WHERE id = ?');
        const item = checkItemStmt.get(numericItemId);

        if (!item) {
          logSystem(`الصنف غير موجود: ${numericItemId}`, 'error');
          throw new Error(`الصنف غير موجود: ${numericItemId}`);
        }

        // التحقق من عدم وجود معاملات مرتبطة بالصنف
        const checkTransactionsStmt = db.prepare('SELECT COUNT(*) as count FROM transactions WHERE item_id = ?');
        const { count } = checkTransactionsStmt.get(numericItemId);

        // التحقق من وجود معاملات بيع مرتبطة بالصنف
        const salesCheckStmt = db.prepare(`
          SELECT COUNT(*) as count FROM transactions
          WHERE item_id = ? AND (transaction_type = 'sale' OR transaction_type = 'withdrawal')
        `);

        const { count: salesCount } = salesCheckStmt.get(numericItemId);

        logSystem(`عدد معاملات البيع المرتبطة بالصنف: ${salesCount}`, 'info');
        logSystem(`إجمالي عدد المعاملات المرتبطة بالصنف: ${count}`, 'info');

        // إذا كان هناك معاملات بيع ولم يتم تفعيل خيار forceDelete
        if (salesCount > 0 && !forceDelete) {
          logSystem(`لا يمكن حذف الصنف لأنه مرتبط بـ ${salesCount} معاملة بيع وforceDelete = ${forceDelete}`, 'warning');
          throw new Error(`لا يمكن حذف الصنف لأنه مرتبط بـ ${salesCount} معاملة بيع. فقط المدير يمكنه حذف الأصناف المحمية.`);
        }

        // إذا كان هناك معاملات غير معاملات البيع ولم يتم تفعيل خيار forceDelete
        if (count > salesCount && !forceDelete) {
          logSystem(`لا يمكن حذف الصنف لأنه مرتبط بـ ${count - salesCount} معاملة أخرى وforceDelete = ${forceDelete}`, 'warning');
          throw new Error(`لا يمكن حذف الصنف لأنه مرتبط بـ ${count - salesCount} معاملة أخرى`);
        }

        // إذا تم تفعيل خيار forceDelete، نقوم بحذف المعاملات المرتبطة أولاً
        if (forceDelete && count > 0) {
          logSystem(`حذف ${count} معاملة مرتبطة بالصنف ${numericItemId} (${item.name}) بواسطة المدير (forceDelete = ${forceDelete})`, 'warning');

          // حذف المعاملات المرتبطة بالصنف
          let deleteResult;
          try {
            const deleteTransactionsStmt = db.prepare('DELETE FROM transactions WHERE item_id = ?');
            deleteResult = deleteTransactionsStmt.run(numericItemId);
            logSystem(`تم حذف ${deleteResult.changes} معاملة مرتبطة بالصنف`, 'info');
          } catch (sqlError) {
            logError(sqlError, 'deleteItem - delete transactions SQL error');
            throw new Error(`فشل في حذف المعاملات المرتبطة بالصنف: ${sqlError.message}`);
          }
        }

        // حذف سجل المخزون أولاً (سيتم حذفه تلقائيًا بسبب قيد المفتاح الأجنبي)
        let inventoryResult;
        try {
          const deleteInventoryStmt = db.prepare('DELETE FROM inventory WHERE item_id = ?');
          inventoryResult = deleteInventoryStmt.run(numericItemId);
          logSystem(`تم حذف ${inventoryResult.changes} سجل من المخزون للصنف`, 'info');
        } catch (sqlError) {
          logError(sqlError, 'deleteItem - delete inventory SQL error');
          throw new Error(`فشل في حذف سجلات المخزون المرتبطة بالصنف: ${sqlError.message}`);
        }

        // حذف الصنف
        let result;
        try {
          const deleteItemStmt = db.prepare('DELETE FROM items WHERE id = ?');
          result = deleteItemStmt.run(numericItemId);
          logSystem(`نتيجة حذف الصنف: ${JSON.stringify(result)}`, 'info');
        } catch (sqlError) {
          logError(sqlError, 'deleteItem - SQL error');
          throw new Error(`فشل في تنفيذ استعلام حذف الصنف: ${sqlError.message}`);
        }

        // التحقق من نجاح عملية الحذف
        if (!result || result.changes === 0) {
          logSystem(`فشل في حذف الصنف: لم يتم تحديث أي سجل`, 'error');
          throw new Error(`فشل في حذف الصنف: لم يتم تحديث أي سجل`);
        }

        logSystem(`تم حذف الصنف بنجاح: ${numericItemId} (${item.name})`, 'info');
        return {
          success: true,
          itemId: numericItemId,
          itemName: item.name,
          deletedTransactions: count > 0 ? count : 0
        };
      })();
    } catch (transactionError) {
      logError(transactionError, 'deleteItem - transaction');
      return {
        success: false,
        error: transactionError.message,
        itemId: numericItemId
      };
    }
  } catch (error) {
    logError(error, 'deleteItem');
    return {
      success: false,
      error: error.message,
      itemId: itemId
    };
  }
}

/**
 * مسح التخزين المؤقت للأصناف
 * @param {string} operation - نوع العملية التي تسببت في مسح التخزين المؤقت (اختياري)
 * @param {number} itemId - معرف الصنف المتأثر (اختياري)
 * @returns {boolean} - نجاح العملية
 */
function clearItemsCache(operation = '', itemId = null) {
  try {
    // مسح التخزين المؤقت للأصناف
    itemsCache = null;
    itemsCacheTimestamp = 0;

    return true;
  } catch (error) {
    logError(error, 'clearItemsCache');
    return false;
  }
}

// تصدير الوظائف
/**
 * تحديث سعر البيع للصنف
 * @param {number} itemId - معرف الصنف
 * @param {number} sellingPrice - سعر البيع الجديد
 * @returns {Object} - نتيجة العملية
 */
function updateItemSellingPrice(itemId, sellingPrice) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    // التحويل إلى رقم
    const numericItemId = Number(itemId);
    if (isNaN(numericItemId) || numericItemId <= 0) {
      return { success: false, error: `معرف الصنف غير صالح: ${itemId}` };
    }

    // التحويل إلى رقم
    const numericSellingPrice = Number(sellingPrice);
    if (isNaN(numericSellingPrice) || numericSellingPrice < 0) {
      return { success: false, error: `سعر البيع غير صالح: ${sellingPrice}` };
    }

    // التحقق من وجود الصنف
    const checkItemStmt = db.prepare('SELECT * FROM items WHERE id = ?');
    const item = checkItemStmt.get(numericItemId);

    if (!item) {
      logSystem(`الصنف غير موجود: ${numericItemId}`, 'error');
      return { success: false, error: `الصنف غير موجود: ${numericItemId}` };
    }

    // استدعاء نظام الأحداث
    const eventSystem = require('./event-system');

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // تحديث سعر البيع في جدول المخزون
      const updateStmt = db.prepare(`
        UPDATE inventory
        SET selling_price = ?, last_updated = ?
        WHERE item_id = ?
      `);

      const now = new Date().toISOString();
      const result = updateStmt.run(numericSellingPrice, now, numericItemId);

      // التحقق من نجاح التحديث
      if (result.changes === 0) {
        // إذا لم يتم تحديث أي سجل، قد يكون السبب أن الصنف ليس له سجل في المخزون
        // نقوم بإنشاء سجل جديد في المخزون
        const insertStmt = db.prepare(`
          INSERT INTO inventory (item_id, current_quantity, minimum_quantity, avg_price, selling_price, last_updated)
          VALUES (?, 0, 0, 0, ?, ?)
        `);

        insertStmt.run(numericItemId, numericSellingPrice, now);
        logSystem(`تم إنشاء سجل جديد في المخزون للصنف: ${item.name} (${numericItemId})`, 'info');
      }

      // الحصول على معلومات المخزون المحدثة
      const getInventoryStmt = db.prepare(`
        SELECT * FROM inventory WHERE item_id = ?
      `);
      const updatedInventory = getInventoryStmt.get(numericItemId);

      // مسح التخزين المؤقت للأصناف
      clearItemsCache('update-selling-price', numericItemId);

      // إرسال إشعار بتحديث المخزون
      eventSystem.notifyInventoryUpdated({
        itemId: numericItemId,
        name: item.name,
        selling_price: numericSellingPrice,
        operation: 'update-selling-price'
      });

      // إرسال إشعار بالحاجة للتحديث
      eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
        target: 'items',
        timestamp: now
      }, true);

      return {
        success: true,
        item: {
          id: numericItemId,
          name: item.name,
          selling_price: numericSellingPrice
        }
      };
    })();
  } catch (error) {
    logError(error, 'updateItemSellingPrice');
    return { success: false, error: error.message };
  }
}

module.exports = {
  initialize,
  getAllItems,
  getItemById,
  searchItems,
  addItem,
  updateItem,
  deleteItem,
  updateItemSellingPrice,
  clearItemsCache
};
