import React, { useState, useEffect, useRef } from 'react';
import {
  FaFileExport,
  FaChartBar,
  FaUndo,
  FaBoxes,
  FaMoneyBillWave,
  FaExclamationTriangle,
  FaPrint,
  FaArrowCircleDown,
  FaArrowCircleUp,
  FaUserFriends,
  FaUser,
  FaShoppingCart,
  FaCalendarAlt,
  FaSearch,
  FaFileInvoice,
  FaPercentage,
  FaInfoCircle,
  FaSync,
  FaChartPie,
  FaList,
  FaCheckCircle,
  FaExclamationCircle,
  FaReceipt,
  FaPhone
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import ReportExporter from '../components/ReportExporter';
import Card from '../components/Card';
import Button from '../components/Button';
import DataTable from '../components/DataTable';
import FormattedCurrency from '../components/FormattedCurrency';
import { getCustomerNameById } from '../utils/customerUtils';
import { updateProfitValuesInDatabase } from '../utils/profitUpdater';
import { formatCurrency } from '../utils/formatCurrency';
import { fixAllProfitsInDatabase, fixCashboxProfitsFromTransactions } from '../utils/fixProfitsInDatabase';
import FinancialSalesReport from '../components/FinancialSalesReport';
import UnifiedCustomerReports from '../components/UnifiedCustomerReports';
import './Reports.css';
import './ReportsEnhanced.css';
import ReportButton from '../components/ReportButton';

const Reports = () => {
  const { inventory, transactions, customers, loading } = useApp();
  const [activeTab, setActiveTab] = useState('inventory');
  const [dateRange, setDateRange] = useState('all');
  const [filteredTransactions, setFilteredTransactions] = useState([]);
  const [selectedRegularCustomer, setSelectedRegularCustomer] = useState('');
  const [regularCustomers, setRegularCustomers] = useState([]);
  const [subCustomerSales, setSubCustomerSales] = useState([]);
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });
  const [profits, setProfits] = useState({ quarterly: 0, halfYearly: 0, threeQuarters: 0, yearly: 0 });
  const [isUpdatingProfits, setIsUpdatingProfits] = useState(false);

  // حالات للتقارير المتخصصة الجديدة
  const [topSellingItems, setTopSellingItems] = useState([]);
  const [topCustomers, setTopCustomers] = useState([]);
  const [lowStockItems, setLowStockItems] = useState([]);
  const [cashboxReport, setCashboxReport] = useState(null);
  const [customerInvoices, setCustomerInvoices] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [returnTransactions, setReturnTransactions] = useState([]);
  const [isLoadingSpecialReport, setIsLoadingSpecialReport] = useState(false);

  const reportRef = useRef(null);
  const alertTimerRef = useRef(null);
  const isMountedRef = useRef(true);

  // وظيفة تنظيف المؤقتات والمراجع
  useEffect(() => {
    // تعيين أن المكون محمل
    isMountedRef.current = true;

    // وظيفة التنظيف عند إزالة المكون
    return () => {
      // تعيين أن المكون لم يعد محملاً
      isMountedRef.current = false;

      // إلغاء أي مؤقتات نشطة
      if (alertTimerRef.current) {
        clearTimeout(alertTimerRef.current);
        alertTimerRef.current = null;
      }
    };
  }, []);

  // دالة مساعدة للتحقق من أن المكون لا يزال محملاً قبل تحديث الحالة
  const safeSetState = (setter, value) => {
    if (isMountedRef.current) {
      setter(value);
    }
  };

  // تصفية المعاملات حسب النطاق الزمني
  useEffect(() => {
    if (!transactions || transactions.length === 0) {
      setFilteredTransactions([]);
      return;
    }

    let filtered = [...transactions];

    // تم إزالة خيارات الشهر الحالي والفترة المخصصة
    if (dateRange === 'quarter') {
      const now = new Date();
      const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);

      filtered = filtered.filter(t => {
        const transactionDate = new Date(t.transaction_date);
        return transactionDate >= quarterStart;
      });
    } else if (dateRange === 'halfYear') {
      const now = new Date();
      const halfYearStart = new Date(now.getFullYear(), now.getMonth() - 6, 1);

      filtered = filtered.filter(t => {
        const transactionDate = new Date(t.transaction_date);
        return transactionDate >= halfYearStart;
      });
    } else if (dateRange === 'threeQuarters') {
      const now = new Date();
      const threeQuartersStart = new Date(now.getFullYear(), now.getMonth() - 9, 1);

      filtered = filtered.filter(t => {
        const transactionDate = new Date(t.transaction_date);
        return transactionDate >= threeQuartersStart;
      });
    } else if (dateRange === 'year') {
      const now = new Date();
      const yearStart = new Date(now.getFullYear(), 0, 1);

      filtered = filtered.filter(t => {
        const transactionDate = new Date(t.transaction_date);
        return transactionDate >= yearStart;
      });
    }

    setFilteredTransactions(filtered);
  }, [transactions, dateRange]);

  // تحميل التقارير المتخصصة عند تغيير التبويب
  useEffect(() => {
    const loadSpecializedReport = async () => {
      // تحميل التقرير المناسب بناءً على التبويب النشط
      switch (activeTab) {
        case 'topSellingItems':
          await loadTopSellingItemsReport();
          break;
        case 'topCustomers':
          await loadTopCustomersReport();
          break;
        case 'lowStock':
          await loadLowStockReport();
          break;
        case 'inventoryDetailed':
          // تحميل جميع التقارير المطلوبة للتبويب الشامل
          safeSetState(setIsLoadingSpecialReport, true);
          try {
            console.log('جاري تحميل بيانات التقرير الشامل للمخزون والأصناف...');

            // تحميل جميع البيانات المطلوبة بالتوازي
            await Promise.all([
              loadTopSellingItemsReport(),
              loadLowStockReport(),
              // تحميل تقرير المخزون
              (async () => {
                try {
                  console.log('جاري الحصول على تقرير المخزون من الخادم...');
                  const result = await window.api.invoke('get-inventory-report');
                  if (result && result.inventory) {
                    if (typeof setInventory === 'function') {
                      setInventory(result.inventory);
                      console.log('تم تحديث بيانات المخزون من الخادم:', result.inventory.length);
                    }
                  }
                } catch (error) {
                  console.error('خطأ في الحصول على تقرير المخزون:', error);
                }
              })()
            ]);

            console.log('تم تحميل جميع بيانات التقرير الشامل بنجاح');
            showAlert('success', 'تم تحميل بيانات التقرير الشامل بنجاح');
          } catch (error) {
            console.error('خطأ في تحميل بيانات التقرير الشامل للمخزون والأصناف:', error);
            showAlert('danger', 'حدث خطأ أثناء تحميل بيانات التقرير الشامل للمخزون والأصناف');
          } finally {
            safeSetState(setIsLoadingSpecialReport, false);
          }
          break;
        case 'cashbox':
          await loadCashboxReport();
          break;
        case 'financialSales':
          // تحميل تقرير الخزينة للتقارير المالية
          await loadCashboxReport();
          break;
        case 'customerInvoices':
          if (selectedCustomer) {
            await loadCustomerInvoices(selectedCustomer);
          }
          break;
        case 'returns':
          // تحميل تقرير الإرجاعات تلقائياً
          await loadReturnTransactionsReport();
          break;
        case 'unifiedCustomerReports':
          // لا نحتاج لتحميل البيانات هنا لأن المكون الجديد يقوم بذلك بنفسه
          console.log('تم تحميل تقرير العملاء الموحد');
          break;
        default:
          // لا تحميل للتقارير الأخرى
          break;
      }
    };

    loadSpecializedReport();
  }, [activeTab, dateRange, selectedCustomer]);

  // تحميل فواتير العملاء
  const loadCustomerInvoices = async (customerId) => {
    try {
      safeSetState(setIsLoadingSpecialReport, true);

      // إنشاء فلاتر بناءً على النطاق الزمني المحدد
      const filters = createDateFilters();
      filters.customerId = customerId;

      console.log('استدعاء get-customer-invoices مع الفلاتر:', filters);

      // استدعاء واجهة العملاء الجديدة
      const result = await window.api.customers.getInvoices(filters);

      if (result && result.invoices) {
        safeSetState(setCustomerInvoices, result.invoices);
        console.log('تم تحميل فواتير العميل:', result);
      } else {
        console.warn('لم يتم العثور على فواتير للعميل في النتيجة:', result);
        // استخدام مصفوفة فارغة في حالة عدم وجود نتيجة
        safeSetState(setCustomerInvoices, []);
      }
    } catch (error) {
      console.error('خطأ في تحميل فواتير العميل:', error);
      showAlert('danger', 'حدث خطأ أثناء تحميل فواتير العميل');
      safeSetState(setCustomerInvoices, []);
    } finally {
      safeSetState(setIsLoadingSpecialReport, false);
    }
  };

  // تحميل تقرير الإرجاعات
  const loadReturnTransactionsReport = async () => {
    try {
      safeSetState(setIsLoadingSpecialReport, true);

      // إنشاء فلاتر بناءً على النطاق الزمني المحدد
      const filters = createDateFilters();

      console.log('استدعاء get-return-transactions-report مع الفلاتر:', filters);

      // استدعاء واجهة التقارير الجديدة
      const result = await window.api.invoke('get-return-transactions-report', filters);

      if (result && result.returnTransactions) {
        safeSetState(setReturnTransactions, result.returnTransactions);
        console.log('تم تحميل تقرير الإرجاعات:', result);
      } else {
        console.warn('لم يتم العثور على بيانات الإرجاعات في النتيجة:', result);
        // استخدام مصفوفة فارغة في حالة عدم وجود نتيجة
        safeSetState(setReturnTransactions, []);
      }
    } catch (error) {
      console.error('خطأ في تحميل تقرير الإرجاعات:', error);
      showAlert('danger', 'حدث خطأ أثناء تحميل تقرير الإرجاعات');
      safeSetState(setReturnTransactions, []);
    } finally {
      safeSetState(setIsLoadingSpecialReport, false);
    }
  };

  // دالة تحديث تقرير الأرباح (متاحة للاستدعاء من الخارج)
  const fetchProfitsReport = async () => {
    try {
      console.log('جاري الحصول على تقرير الأرباح من الخادم...');

      // تحديث قيم الربح في قاعدة البيانات تلقائيًا (المزامنة التلقائية)
      try {
        safeSetState(setIsUpdatingProfits, true);
        console.log('جاري تحديث قيم الربح في قاعدة البيانات تلقائيًا...');
        await updateProfitValuesInDatabase(window.api, (value) => safeSetState(setIsUpdatingProfits, value), showAlert);

        // تصحيح قيم الربح السالبة
        try {
          // استخدام window.api.invoke إذا كان متاحًا، وإلا استخدام window.invokeChannel
          const invokeFunction = window.api && window.api.invoke ? window.api.invoke : window.invokeChannel;

          if (invokeFunction) {
            const fixResult = await invokeFunction('fix-negative-profits');
            if (fixResult && fixResult.success) {
              console.log(`تم تصحيح ${fixResult.updatedCount} قيمة ربح سالبة`);
            }
          } else {
            console.error('لا توجد وظيفة invoke متاحة لاستدعاء fix-negative-profits');
          }
        } catch (fixError) {
          console.error('خطأ في استدعاء fix-negative-profits:', fixError);
        }
      } catch (updateError) {
        console.error('خطأ في التحديث التلقائي لقيم الربح:', updateError);
        // نستمر في التنفيذ حتى لو فشل التحديث التلقائي
      } finally {
        safeSetState(setIsUpdatingProfits, false);
      }

      // إنشاء فلاتر بناءً على النطاق الزمني المحدد
      const filters = {};

      if (dateRange === 'quarter') {
        const now = new Date();
        const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        filters.startDate = quarterStart.toISOString();
      } else if (dateRange === 'halfYear') {
        const now = new Date();
        const halfYearStart = new Date(now.getFullYear(), now.getMonth() - 6, 1);
        filters.startDate = halfYearStart.toISOString();
      } else if (dateRange === 'threeQuarters') {
        const now = new Date();
        const threeQuartersStart = new Date(now.getFullYear(), now.getMonth() - 9, 1);
        filters.startDate = threeQuartersStart.toISOString();
      } else if (dateRange === 'year') {
        const now = new Date();
        const yearStart = new Date(now.getFullYear(), 0, 1);
        filters.startDate = yearStart.toISOString();
      }

      // إضافة معلمة لإجبار التحديث وتجاوز التخزين المؤقت
      filters.forceRefresh = true;

      console.log('استدعاء get-profits-report مع الفلاتر:', filters);

      // استدعاء دالة حساب الأرباح من الخادم
      const result = await window.api.invoke('get-profits-report', filters);

      if (result && result.stats) {
        // التحقق من وجود رسالة خطأ
        if (result.error) {
          console.error('خطأ في تقرير الأرباح من الخادم:', result.error);
          showAlert('warning', `خطأ في تقرير الأرباح: ${result.error}`);

          // استخدام الحساب المحلي في حالة الخطأ
          const localProfits = calculateLocalProfits();
          safeSetState(setProfits, localProfits);
          console.log('تم تحديث الأرباح محلياً بعد الخطأ:', localProfits);
          return;
        }

        // تحديث الأرباح من نتيجة الخادم
        // تقسيم الأرباح حسب الفترات الزمنية المختلفة
        let quarterlyProfit = 0;
        let halfYearlyProfit = 0;
        let threeQuartersProfit = 0;
        let yearlyProfit = result.stats.totalProfit || 0;

        // حساب الأرباح للفترات المختلفة
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth();

        // تصفية المعاملات حسب الفترات الزمنية
        if (result.transactions && result.transactions.length > 0) {
          // دالة مساعدة لحساب الربح مع مصاريف النقل
          const calculateProfitWithTransport = (transaction) => {
            // إذا كان الربح محسوباً بالفعل ويتضمن مصاريف النقل، استخدمه
            if (transaction.profit && transaction.profit > 0) {
              return transaction.profit;
            }

            // إذا لم يكن محسوباً، احسبه بناءً على سعر البيع وسعر التكلفة
            if (transaction.selling_price > 0 && transaction.avg_price > 0) {
              // حساب الربح الأساسي
              const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;

              // خصم مصاريف النقل إذا كانت متوفرة
              const transportCost = transaction.transport_cost || 0;
              const finalProfit = basicProfit - transportCost;

              return Math.max(0, finalProfit);
            }

            // إذا لم تكن البيانات متوفرة، استخدم تقدير 20%
            return transaction.selling_price * transaction.quantity * 0.2;
          };

          // الربع سنوي
          const quarterlyTransactions = result.transactions.filter(t => {
            const transactionDate = new Date(t.transaction_date);
            return (
              transactionDate.getFullYear() === currentYear &&
              transactionDate.getMonth() >= currentMonth - 3 &&
              transactionDate.getMonth() <= currentMonth
            );
          });

          quarterlyProfit = quarterlyTransactions.reduce((sum, t) => {
            if (t.transaction_type === 'sale') {
              return sum + calculateProfitWithTransport(t);
            } else if (t.transaction_type === 'return') {
              return sum - Math.max(0, calculateProfitWithTransport(t));
            }
            return sum;
          }, 0);

          // النصف سنوي
          const halfYearlyTransactions = result.transactions.filter(t => {
            const transactionDate = new Date(t.transaction_date);
            return (
              transactionDate.getFullYear() === currentYear &&
              transactionDate.getMonth() >= currentMonth - 6 &&
              transactionDate.getMonth() <= currentMonth
            );
          });

          halfYearlyProfit = halfYearlyTransactions.reduce((sum, t) => {
            if (t.transaction_type === 'sale') {
              return sum + calculateProfitWithTransport(t);
            } else if (t.transaction_type === 'return') {
              return sum - Math.max(0, calculateProfitWithTransport(t));
            }
            return sum;
          }, 0);

          // ثلاثة أرباع السنة
          const threeQuartersTransactions = result.transactions.filter(t => {
            const transactionDate = new Date(t.transaction_date);
            return (
              transactionDate.getFullYear() === currentYear &&
              transactionDate.getMonth() >= currentMonth - 9 &&
              transactionDate.getMonth() <= currentMonth
            );
          });

          threeQuartersProfit = threeQuartersTransactions.reduce((sum, t) => {
            if (t.transaction_type === 'sale') {
              return sum + calculateProfitWithTransport(t);
            } else if (t.transaction_type === 'return') {
              return sum - Math.max(0, calculateProfitWithTransport(t));
            }
            return sum;
          }, 0);
        }

        safeSetState(setProfits, {
          quarterly: quarterlyProfit,
          halfYearly: halfYearlyProfit,
          threeQuarters: threeQuartersProfit,
          yearly: yearlyProfit
        });

        console.log('تم تحديث الأرباح من الخادم:', {
          quarterly: quarterlyProfit,
          halfYearly: halfYearlyProfit,
          threeQuarters: threeQuartersProfit,
          yearly: yearlyProfit
        });
      } else {
        console.warn('لم يتم العثور على إحصائيات الأرباح في النتيجة:', result);

        // إذا لم تكن هناك نتيجة من الخادم، استخدم الحساب المحلي
        const localProfits = calculateLocalProfits();
        safeSetState(setProfits, localProfits);
        console.log('تم تحديث الأرباح محلياً:', localProfits);
      }
    } catch (error) {
      console.error('خطأ في الحصول على تقرير الأرباح:', error);
      showAlert('danger', 'حدث خطأ أثناء تحميل تقرير الأرباح');

      // في حالة الخطأ، استخدم الحساب المحلي
      const localProfits = calculateLocalProfits();
      safeSetState(setProfits, localProfits);
      console.log('تم تحديث الأرباح محلياً بعد الخطأ:', localProfits);
    }
    };

  // دالة إصلاح الأرباح في قاعدة البيانات (إعادة حساب كل معاملة)
  const handleFixProfits = async () => {
    try {
      safeSetState(setIsUpdatingProfits, true);
      showAlert('info', 'جاري إصلاح الأرباح في قاعدة البيانات...');

      const result = await fixAllProfitsInDatabase();

      if (result.success) {
        showAlert('success', `تم إصلاح ${result.updatedCount} معاملة. الفرق: ${result.difference.toFixed(2)} د.ل`);
        // إعادة تحميل التقارير
        await fetchProfitsReport();
        // إعادة تحميل البيانات
        window.location.reload();
      } else {
        showAlert('danger', `فشل في إصلاح الأرباح: ${result.error}`);
      }
    } catch (error) {
      console.error('خطأ في إصلاح الأرباح:', error);
      showAlert('danger', 'حدث خطأ أثناء إصلاح الأرباح');
    } finally {
      safeSetState(setIsUpdatingProfits, false);
    }
  };

  // دالة إصلاح الأرباح في الخزينة من المعاملات الفعلية (أسرع)
  const handleFixCashboxProfits = async () => {
    try {
      safeSetState(setIsUpdatingProfits, true);
      showAlert('info', 'جاري إصلاح الأرباح في الخزينة...');

      const result = await fixCashboxProfitsFromTransactions();

      if (result.success) {
        showAlert('success', `تم إصلاح الأرباح. القيمة الجديدة: ${result.newProfit.toFixed(2)} د.ل (الفرق: ${result.difference.toFixed(2)})`);
        // إعادة تحميل التقارير
        await fetchProfitsReport();
        // إعادة تحميل البيانات
        window.location.reload();
      } else {
        showAlert('danger', `فشل في إصلاح الأرباح: ${result.error}`);
      }
    } catch (error) {
      console.error('خطأ في إصلاح الأرباح:', error);
      showAlert('danger', 'حدث خطأ أثناء إصلاح الأرباح');
    } finally {
      safeSetState(setIsUpdatingProfits, false);
    }
  };

  // تصدير دالة تحديث الأرباح للاستخدام الخارجي
  window.fetchProfitsReport = fetchProfitsReport;
  window.fixProfits = handleFixProfits;
  window.fixCashboxProfits = handleFixCashboxProfits;

  // تحديث الأرباح عند تغيير المعاملات المصفاة أو النطاق الزمني
  useEffect(() => {
    fetchProfitsReport();
  }, [filteredTransactions, dateRange]);

  // إضافة مستمع للحدث الخاص بتحديث الأرباح
  useEffect(() => {
    // دالة معالجة حدث تحديث الأرباح
    const handleProfitsUpdated = (event) => {
      console.log('تم استلام حدث تحديث الأرباح في صفحة التقارير:', event.detail);
      // تحديث تقرير الأرباح
      fetchProfitsReport();
    };

    // دالة معالجة حدث التحديث التلقائي للأرباح
    const handleAutoProfitsUpdated = (event) => {
      console.log('تم استلام حدث التحديث التلقائي للأرباح في صفحة التقارير:', event.detail);
      // تحديث تقرير الأرباح
      fetchProfitsReport();
    };

    // دالة معالجة حدث التحديث المباشر
    const handleDirectUpdate = (event) => {
      console.log('تم استلام حدث التحديث المباشر في صفحة التقارير:', event.detail);
      // تحديث تقرير الأرباح
      fetchProfitsReport();
    };

    // دالة معالجة حدث إضافة معاملة
    const handleTransactionAdded = (event) => {
      console.log('تم استلام حدث إضافة معاملة في صفحة التقارير:', event.detail);
      // تحديث تقرير الأرباح
      fetchProfitsReport();
    };

    // دالة معالجة حدث تحديث المعاملات
    const handleTransactionsRefreshed = (event) => {
      console.log('تم استلام حدث تحديث المعاملات في صفحة التقارير:', event.detail);
      // تحديث تقرير الأرباح
      fetchProfitsReport();
    };

    // تسجيل المستمعين
    window.addEventListener('profits-updated', handleProfitsUpdated);
    window.addEventListener('auto-profits-updated', handleAutoProfitsUpdated);
    window.addEventListener('direct-update', handleDirectUpdate);
    window.addEventListener('transaction-added', handleTransactionAdded);
    window.addEventListener('transactions-refreshed', handleTransactionsRefreshed);

    // إزالة المستمعين عند تفكيك المكون
    return () => {
      window.removeEventListener('profits-updated', handleProfitsUpdated);
      window.removeEventListener('auto-profits-updated', handleAutoProfitsUpdated);
      window.removeEventListener('direct-update', handleDirectUpdate);
      window.removeEventListener('transaction-added', handleTransactionAdded);
      window.removeEventListener('transactions-refreshed', handleTransactionsRefreshed);
    };
  }, []);

  // إضافة مستمع لتحديث تقرير الخزينة عند تحديث الخزينة
  useEffect(() => {
    const handleCashboxUpdate = () => {
      console.log('تم تحديث الخزينة، جاري تحديث تقرير الخزينة...');
      if (activeTab === 'financialSales') {
        loadCashboxReport();
      }
    };

    // إضافة مستمع للأحداث
    window.addEventListener('cashbox-updated-ui', handleCashboxUpdate);

    // تنظيف المستمع عند إلغاء تحميل المكون
    return () => {
      window.removeEventListener('cashbox-updated-ui', handleCashboxUpdate);
    };
  }, [activeTab]);

  // دالة تحديث قيم الربح (متاحة للاستدعاء من الخارج)
  const updateProfitValues = async () => {
    try {
      console.log('جاري تحديث قيم الربح...');
      setIsUpdatingProfits(true);

      // استدعاء وظيفة تحديث قيم الربح
      const updateResult = await updateProfitValuesInDatabase(window.api, setIsUpdatingProfits, showAlert);
      console.log('نتيجة تحديث قيم الربح:', updateResult);

      // تصحيح قيم الربح السالبة
      try {
        // استخدام window.api.invoke إذا كان متاحًا، وإلا استخدام window.invokeChannel
        const invokeFunction = window.api && window.api.invoke ? window.api.invoke : window.invokeChannel;

        if (invokeFunction) {
          const fixResult = await invokeFunction('fix-negative-profits');
          if (fixResult && fixResult.success) {
            console.log(`تم تصحيح ${fixResult.updatedCount} قيمة ربح سالبة`);
          }
        } else {
          console.error('لا توجد وظيفة invoke متاحة لاستدعاء fix-negative-profits');
        }
      } catch (fixError) {
        console.error('خطأ في تصحيح قيم الربح السالبة:', fixError);
      }

      // تحديث تقرير الأرباح بعد التحديث
      await fetchProfitsReport();

      // إرسال حدث تحديث الأرباح لتحديث جميع الواجهات
      const event = new CustomEvent('profits-updated', {
        detail: {
          success: true,
          message: 'تم تحديث قيم الربح بنجاح',
          timestamp: new Date().toISOString()
        }
      });
      window.dispatchEvent(event);

      console.log('تم تحديث قيم الربح بنجاح');
      return { success: true };
    } catch (error) {
      console.error('خطأ في تحديث قيم الربح:', error);
      return { success: false, error: error.message };
    } finally {
      setIsUpdatingProfits(false);
    }
  };

  // تصدير دالة تحديث قيم الربح للاستخدام الخارجي
  window.updateProfitValues = updateProfitValues;

  // دالة تشخيص الأرباح
  const diagnoseProfits = async () => {
    try {
      console.log('🔍 بدء تشخيص الأرباح...');
      const result = await window.electronAPI.invoke('diagnose-profits');

      if (result.success) {
        console.log('✅ تم تشخيص الأرباح بنجاح');
        console.log('📊 نتائج التشخيص:', result.data);

        const { cashbox, salesSummary, salesDetails } = result.data;

        // عرض بيانات الخزينة
        console.log('\n💰 بيانات الخزينة:');
        if (cashbox) {
          console.log(`   - الرصيد الافتتاحي: ${cashbox.initial_balance || 0}`);
          console.log(`   - الرصيد الحالي: ${cashbox.current_balance || 0}`);
          console.log(`   - إجمالي الأرباح: ${cashbox.profit_total || 0}`);
          console.log(`   - إجمالي المبيعات: ${cashbox.sales_total || 0}`);
          console.log(`   - إجمالي المشتريات: ${cashbox.purchases_total || 0}`);
        }

        // عرض ملخص معاملات البيع
        console.log('\n📈 ملخص معاملات البيع:');
        if (salesSummary) {
          console.log(`   - عدد معاملات البيع: ${salesSummary.count || 0}`);
          console.log(`   - إجمالي الأرباح من المعاملات: ${salesSummary.total_profit || 0}`);
          console.log(`   - إجمالي قيمة المبيعات: ${salesSummary.total_sales || 0}`);
        }

        // عرض تفاصيل معاملات البيع
        console.log('\n🔍 تفاصيل آخر معاملات البيع:');
        if (salesDetails && salesDetails.length > 0) {
          salesDetails.forEach((sale, index) => {
            console.log(`   ${index + 1}. معاملة ${sale.id}:`);
            console.log(`      - الصنف: ${sale.item_id}`);
            console.log(`      - الكمية: ${sale.quantity}`);
            console.log(`      - سعر البيع: ${sale.selling_price}`);
            console.log(`      - إجمالي السعر: ${sale.total_price}`);
            console.log(`      - الربح: ${sale.profit || 0}`);
            console.log(`      - التاريخ: ${sale.transaction_date}`);
          });
        } else {
          console.log('   لا توجد معاملات بيع');
        }

        // تحليل المشكلة
        console.log('\n🔍 تحليل المشكلة:');
        if (salesSummary?.count === 0) {
          console.log('❌ المشكلة: لا توجد معاملات بيع في النظام');
        } else if (salesSummary?.total_profit === 0 || salesSummary?.total_profit === null) {
          console.log('❌ المشكلة: معاملات البيع موجودة لكن الأرباح = 0');
        } else {
          const difference = Math.abs((salesSummary?.total_profit || 0) - (cashbox?.profit_total || 0));
          if (difference > 0.01) {
            console.log('❌ المشكلة: عدم تطابق الأرباح المحسوبة مع المحفوظة');
            console.log(`   - الفرق: ${difference}`);
          } else {
            console.log('✅ النظام يبدو سليماً');
          }
        }

        return result.data;
      } else {
        console.error('❌ فشل في تشخيص الأرباح:', result.error);
        return null;
      }
    } catch (error) {
      console.error('❌ خطأ في تشخيص الأرباح:', error);
      return null;
    }
  };

  // تصدير دالة التشخيص للاستخدام الخارجي
  window.diagnoseProfits = diagnoseProfits;

  // حساب الأرباح محلياً
  const calculateLocalProfits = () => {
    if (!filteredTransactions || filteredTransactions.length === 0) {
      return { quarterly: 0, halfYearly: 0, threeQuarters: 0, yearly: 0 };
    }

    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    // دالة مساعدة لحساب الربح مع خصم مصاريف النقل (تم إصلاحها)
    const calculateProfitWithTransport = (transaction) => {
      // أولوية عالية: استخدام القيمة المحفوظة في قاعدة البيانات إذا كانت متوفرة
      if (transaction.profit !== undefined && transaction.profit !== null && !isNaN(transaction.profit)) {
        console.log(`[PROFIT-FIX-REPORTS] استخدام الربح المحفوظ في قاعدة البيانات: ${transaction.profit} للمعاملة ${transaction.id}`);
        return Number(transaction.profit);
      }

      // إذا لم تكن القيمة محفوظة، احسبها بناءً على سعر البيع وسعر التكلفة
      if (transaction.selling_price > 0 && transaction.avg_price > 0) {
        // حساب الربح الأساسي
        const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;

        // خصم مصاريف النقل إذا كانت متوفرة
        const transportCost = transaction.transport_cost || 0;
        const finalProfit = basicProfit - transportCost;

        console.log(`[PROFIT-FIX-REPORTS] حساب الربح من البيانات: (${transaction.selling_price} - ${transaction.avg_price}) × ${transaction.quantity} - ${transportCost} = ${finalProfit}`);
        return Math.max(0, finalProfit);
      }

      // إذا لم تكن البيانات متوفرة، استخدم تقدير 20%
      const estimatedProfit = transaction.selling_price * transaction.quantity * 0.2;
      console.log(`[PROFIT-FIX-REPORTS] استخدام تقدير الربح: ${estimatedProfit} للمعاملة ${transaction.id}`);
      return estimatedProfit;
    };

    // تصفية معاملات البيع والإرجاع
    const salesTransactions = filteredTransactions.filter(t => t.transaction_type === 'sale');
    const returnTransactions = filteredTransactions.filter(t => t.transaction_type === 'return');

    // Helper to filter by period
    const filterByPeriod = (transactions, months) => {
      return transactions.filter(t => {
        const transactionDate = new Date(t.transaction_date);
        return (
          transactionDate.getFullYear() === currentYear &&
          transactionDate.getMonth() >= currentMonth - months &&
          transactionDate.getMonth() <= currentMonth
        );
      });
    };

    // Profits for each period (تم إصلاحها لتطرح مصاريف النقل)
    const quarterlyProfit = filterByPeriod(salesTransactions, 3).reduce((sum, t) => sum + calculateProfitWithTransport(t), 0)
      - filterByPeriod(returnTransactions, 3).reduce((sum, t) => sum + Math.max(0, calculateProfitWithTransport(t)), 0);
    const halfYearlyProfit = filterByPeriod(salesTransactions, 6).reduce((sum, t) => sum + calculateProfitWithTransport(t), 0)
      - filterByPeriod(returnTransactions, 6).reduce((sum, t) => sum + Math.max(0, calculateProfitWithTransport(t)), 0);
    const threeQuartersProfit = filterByPeriod(salesTransactions, 9).reduce((sum, t) => sum + calculateProfitWithTransport(t), 0)
      - filterByPeriod(returnTransactions, 9).reduce((sum, t) => sum + Math.max(0, calculateProfitWithTransport(t)), 0);
    const yearlyProfit = salesTransactions.filter(t => {
      const transactionDate = new Date(t.transaction_date);
      return transactionDate.getFullYear() === currentYear;
    }).reduce((sum, t) => sum + calculateProfitWithTransport(t), 0)
      - returnTransactions.filter(t => {
        const transactionDate = new Date(t.transaction_date);
        return transactionDate.getFullYear() === currentYear;
      }).reduce((sum, t) => sum + Math.max(0, calculateProfitWithTransport(t)), 0);

    // استخدام قيمة الربح من الخزينة إذا كانت متوفرة
    if (cashboxReport && cashboxReport.cashbox && cashboxReport.cashbox.profit_total !== undefined) {
      console.log('استخدام قيمة الربح من الخزينة في التقارير:', cashboxReport.cashbox.profit_total);

      // تحديث قيم الأرباح بناءً على نسبة الربح في الخزينة
      const totalProfitFromTransactions = salesTransactions.reduce((total, t) => total + (t.profit || 0), 0)
        - returnTransactions.reduce((total, t) => total + (t.profit || 0), 0);

      if (totalProfitFromTransactions > 0) {
        const profitRatio = cashboxReport.cashbox.profit_total / totalProfitFromTransactions;
        return {
          quarterly: quarterlyProfit * profitRatio,
          halfYearly: halfYearlyProfit * profitRatio,
          threeQuarters: threeQuartersProfit * profitRatio,
          yearly: cashboxReport.cashbox.profit_total
        };
      }
      return {
        quarterly: cashboxReport.cashbox.profit_total / 4,
        halfYearly: cashboxReport.cashbox.profit_total / 2,
        threeQuarters: cashboxReport.cashbox.profit_total * 0.75,
        yearly: cashboxReport.cashbox.profit_total
      };
    }

    return {
      quarterly: quarterlyProfit,
      halfYearly: halfYearlyProfit,
      threeQuarters: threeQuartersProfit,
      yearly: yearlyProfit
    };
  };

  // حساب إحصائيات المخزون
  const calculateInventoryStats = () => {
    if (!inventory || inventory.length === 0) return { total: 0, low: 0, out: 0, value: 0 };

    const total = inventory.length;
    const low = inventory.filter(item => item.current_quantity <= item.minimum_quantity && item.current_quantity > 0).length;
    const out = inventory.filter(item => item.current_quantity === 0).length;
    const value = inventory.reduce((sum, item) => sum + (item.current_quantity * item.avg_price), 0);

    return { total, low, out, value };
  };

  // حساب إحصائيات المعاملات
  const calculateTransactionStats = () => {
    if (!filteredTransactions || filteredTransactions.length === 0) {
      return {
        purchases: { count: 0, value: 0 },
        sales: { count: 0, value: 0, profit: 0 },
        returns: { count: 0, value: 0, profit: 0 }
      };
    }

    // دالة مساعدة لحساب الربح مع خصم مصاريف النقل (تم إصلاحها)
    const calculateProfitWithTransportLocal = (transaction) => {
      // أولوية عالية: استخدام القيمة المحفوظة في قاعدة البيانات إذا كانت متوفرة
      if (transaction.profit !== undefined && transaction.profit !== null && !isNaN(transaction.profit)) {
        console.log(`[PROFIT-FIX-LOCAL] استخدام الربح المحفوظ في قاعدة البيانات: ${transaction.profit} للمعاملة ${transaction.id}`);
        return Number(transaction.profit);
      }

      // إذا لم تكن القيمة محفوظة، احسبها بناءً على سعر البيع وسعر التكلفة
      if (transaction.selling_price > 0 && transaction.avg_price > 0) {
        // حساب الربح الأساسي
        const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;

        // خصم مصاريف النقل إذا كانت متوفرة
        const transportCost = transaction.transport_cost || 0;
        const finalProfit = basicProfit - transportCost;

        console.log(`[PROFIT-FIX-LOCAL] حساب الربح من البيانات: (${transaction.selling_price} - ${transaction.avg_price}) × ${transaction.quantity} - ${transportCost} = ${finalProfit}`);
        return Math.max(0, finalProfit);
      }

      // إذا لم تكن البيانات متوفرة، استخدم تقدير 20%
      const estimatedProfit = transaction.selling_price * transaction.quantity * 0.2;
      console.log(`[PROFIT-FIX-LOCAL] استخدام تقدير الربح: ${estimatedProfit} للمعاملة ${transaction.id}`);
      return estimatedProfit;
    };

    const purchases = filteredTransactions.filter(t => t.transaction_type === 'purchase');
    const sales = filteredTransactions.filter(t => t.transaction_type === 'sale');
    const returns = filteredTransactions.filter(t => t.transaction_type === 'return');

    const purchasesCount = purchases.length;
    const purchasesValue = purchases.reduce((sum, t) => sum + (t.total_price || 0), 0);

    const salesCount = sales.length;
    const salesValue = sales.reduce((sum, t) => sum + (t.total_price || 0), 0);
    const salesProfit = sales.reduce((sum, t) => sum + calculateProfitWithTransportLocal(t), 0);

    const returnsCount = returns.length;
    // Returns are negative for sales and profit
    const returnsValue = returns.reduce((sum, t) => sum + (t.total_price || 0), 0);
    const returnsProfit = returns.reduce((sum, t) => sum + Math.max(0, calculateProfitWithTransportLocal(t)), 0);

    // Adjust sales and profit by subtracting returns
    return {
      purchases: { count: purchasesCount, value: purchasesValue },
      sales: {
        count: salesCount - returnsCount,
        value: salesValue - returnsValue,
        profit: salesProfit - returnsProfit
      },
      returns: { count: returnsCount, value: returnsValue, profit: returnsProfit }
    };
  };

  // تحميل تقرير الأصناف الأكثر مبيعًا من الخادم
  const loadTopSellingItemsReport = async () => {
    try {
      safeSetState(setIsLoadingSpecialReport, true);

      // إنشاء فلاتر بناءً على النطاق الزمني المحدد
      const filters = createDateFilters();

      console.log('استدعاء get-top-selling-items-report مع الفلاتر:', filters);

      // استدعاء واجهة التقارير الجديدة
      const result = await window.api.reports.getTopSellingItemsReport(filters);

      if (result && result.items) {
        safeSetState(setTopSellingItems, result.items);
        console.log('تم تحميل تقرير الأصناف الأكثر مبيعًا:', result);
      } else {
        console.warn('لم يتم العثور على بيانات الأصناف الأكثر مبيعًا في النتيجة:', result);
        // استخدام الحساب المحلي في حالة عدم وجود نتيجة
        safeSetState(setTopSellingItems, calculateTopSellingItemsLocally());
      }
    } catch (error) {
      console.error('خطأ في تحميل تقرير الأصناف الأكثر مبيعًا:', error);
      showAlert('danger', 'حدث خطأ أثناء تحميل تقرير الأصناف الأكثر مبيعًا');
      // استخدام الحساب المحلي في حالة الخطأ
      safeSetState(setTopSellingItems, calculateTopSellingItemsLocally());
    } finally {
      safeSetState(setIsLoadingSpecialReport, false);
    }
  };

  // تحميل تقرير العملاء الأكثر شراءً من الخادم
  const loadTopCustomersReport = async () => {
    try {
      safeSetState(setIsLoadingSpecialReport, true);

      // إنشاء فلاتر بناءً على النطاق الزمني المحدد
      const filters = createDateFilters();

      console.log('استدعاء get-top-customers-report مع الفلاتر:', filters);

      // استدعاء واجهة التقارير الجديدة
      const result = await window.api.reports.getTopCustomersReport(filters);

      if (result && result.customers) {
        safeSetState(setTopCustomers, result.customers);
        console.log('تم تحميل تقرير العملاء الأكثر شراءً:', result);
      } else {
        console.warn('لم يتم العثور على بيانات العملاء الأكثر شراءً في النتيجة:', result);
        // استخدام الحساب المحلي في حالة عدم وجود نتيجة
        safeSetState(setTopCustomers, []);
      }
    } catch (error) {
      console.error('خطأ في تحميل تقرير العملاء الأكثر شراءً:', error);
      showAlert('danger', 'حدث خطأ أثناء تحميل تقرير العملاء الأكثر شراءً');
      // استخدام الحساب المحلي في حالة الخطأ
      safeSetState(setTopCustomers, []);
    } finally {
      safeSetState(setIsLoadingSpecialReport, false);
    }
  };

  // تحميل تقرير الأصناف التي تحتاج إلى إعادة طلب من الخادم
  const loadLowStockReport = async () => {
    try {
      safeSetState(setIsLoadingSpecialReport, true);

      const options = {
        includeZeroQuantity: true // تضمين الأصناف ذات الكمية الصفرية
      };

      console.log('استدعاء get-low-stock-report مع الخيارات:', options);

      // استدعاء واجهة التقارير الجديدة
      const result = await window.api.reports.getLowStockReport(options);

      if (result && result.items) {
        safeSetState(setLowStockItems, result.items);
        console.log('تم تحميل تقرير الأصناف التي تحتاج إلى إعادة طلب:', result);
      } else {
        console.warn('لم يتم العثور على بيانات الأصناف التي تحتاج إلى إعادة طلب في النتيجة:', result);
        // استخدام الحساب المحلي في حالة عدم وجود نتيجة
        safeSetState(setLowStockItems, []);
      }
    } catch (error) {
      console.error('خطأ في تحميل تقرير الأصناف التي تحتاج إلى إعادة طلب:', error);
      showAlert('danger', 'حدث خطأ أثناء تحميل تقرير الأصناف التي تحتاج إلى إعادة طلب');
      // استخدام الحساب المحلي في حالة الخطأ
      safeSetState(setLowStockItems, []);
    } finally {
      safeSetState(setIsLoadingSpecialReport, false);
    }
  };

  // تحميل تقرير الخزينة من الخادم
  const loadCashboxReport = async () => {
    try {
      safeSetState(setIsLoadingSpecialReport, true);

      // إنشاء فلاتر بناءً على النطاق الزمني المحدد
      const filters = {
        ...createDateFilters(),
        groupBySource: true, // تجميع المعاملات حسب المصدر
        groupByDate: true // تجميع المعاملات حسب التاريخ
      };

      console.log('استدعاء get-cashbox-report مع الفلاتر:', filters);

      // استدعاء واجهة التقارير الجديدة
      const result = await window.api.reports.getCashboxReport(filters);

      if (result && result.cashbox) {
        safeSetState(setCashboxReport, result);
        console.log('تم تحميل تقرير الخزينة:', result);
      } else {
        console.warn('لم يتم العثور على بيانات الخزينة في النتيجة:', result);
        // استخدام قيم افتراضية في حالة عدم وجود نتيجة
        safeSetState(setCashboxReport, null);
      }
    } catch (error) {
      console.error('خطأ في تحميل تقرير الخزينة:', error);
      showAlert('danger', 'حدث خطأ أثناء تحميل تقرير الخزينة');
      // استخدام قيم افتراضية في حالة الخطأ
      safeSetState(setCashboxReport, null);
    } finally {
      safeSetState(setIsLoadingSpecialReport, false);
    }
  };

  // تحديث تقرير الخزينة
  const handleCashboxReportUpdate = (updatedReport) => {
    if (updatedReport) {
      safeSetState(setCashboxReport, updatedReport);
      console.log('تم تحديث تقرير الخزينة:', updatedReport);
    }
  };

  // إنشاء فلاتر التاريخ بناءً على النطاق الزمني المحدد
  const createDateFilters = () => {
    const filters = {};

    if (dateRange === 'quarter') {
      const now = new Date();
      const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
      filters.startDate = quarterStart.toISOString();
    } else if (dateRange === 'halfYear') {
      const now = new Date();
      const halfYearStart = new Date(now.getFullYear(), now.getMonth() - 6, 1);
      filters.startDate = halfYearStart.toISOString();
    } else if (dateRange === 'threeQuarters') {
      const now = new Date();
      const threeQuartersStart = new Date(now.getFullYear(), now.getMonth() - 9, 1);
      filters.startDate = threeQuartersStart.toISOString();
    } else if (dateRange === 'year') {
      const now = new Date();
      const yearStart = new Date(now.getFullYear(), 0, 1);
      filters.startDate = yearStart.toISOString();
    }

    return filters;
  };

  // حساب إحصائيات الأصناف الأكثر مبيعًا محلياً (احتياطي)
  const calculateTopSellingItemsLocally = () => {
    if (!filteredTransactions || filteredTransactions.length === 0) return [];

    // دالة مساعدة لحساب الربح بدون خصم مصاريف النقل
    const calculateProfitWithoutTransportForTopItems = (transaction) => {
      // إذا كان الربح محسوباً بالفعل، استخدمه
      if (transaction.profit && transaction.profit > 0) {
        return transaction.profit;
      }

      // إذا لم يكن محسوباً، احسبه بناءً على سعر البيع وسعر التكلفة
      if (transaction.selling_price > 0 && transaction.avg_price > 0) {
        // حساب الربح الأساسي بدون خصم مصاريف النقل
        // مصاريف النقل تم خصمها بالفعل من الرصيد الحالي في عمليات الشراء
        const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;

        return Math.max(0, basicProfit);
      }

      // إذا لم تكن البيانات متوفرة، استخدم تقدير 20%
      return transaction.selling_price * transaction.quantity * 0.2;
    };

    const sales = filteredTransactions.filter(t => t.transaction_type === 'sale');

    // تجميع المبيعات حسب الصنف
    const itemSales = {};
    sales.forEach(sale => {
      if (!itemSales[sale.item_id]) {
        itemSales[sale.item_id] = {
          item_id: sale.item_id,
          item_name: sale.item_name,
          total_quantity: 0,
          total_sales: 0,
          total_profit: 0
        };
      }

      itemSales[sale.item_id].total_quantity += sale.quantity;
      itemSales[sale.item_id].total_sales += sale.total_price;
      itemSales[sale.item_id].total_profit += calculateProfitWithoutTransportForTopItems(sale);
    });

    // تحويل إلى مصفوفة وترتيب حسب الكمية
    return Object.values(itemSales)
      .sort((a, b) => b.total_quantity - a.total_quantity)
      .slice(0, 10); // أعلى 10 أصناف
  };

  // حساب إحصائيات الأصناف الأكثر مبيعًا
  const calculateTopSellingItems = () => {
    if (!filteredTransactions || filteredTransactions.length === 0) return [];

    const sales = filteredTransactions.filter(t => t.transaction_type === 'sale');

    // تجميع المبيعات حسب الصنف
    const itemSales = {};
    sales.forEach(sale => {
      if (!itemSales[sale.item_id]) {
        itemSales[sale.item_id] = {
          item_id: sale.item_id,
          item_name: sale.item_name,
          quantity: 0,
          value: 0,
          profit: 0
        };
      }

      itemSales[sale.item_id].quantity += sale.quantity;
      itemSales[sale.item_id].value += sale.total_price;
      itemSales[sale.item_id].profit += (sale.profit || 0);
    });

    // تحويل إلى مصفوفة وترتيب حسب الكمية
    return Object.values(itemSales)
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5); // أعلى 5 أصناف
  };

  // حساب إحصائيات الأصناف الأكثر ربحًا
  const calculateMostProfitableItems = () => {
    if (!filteredTransactions || filteredTransactions.length === 0) return [];

    const sales = filteredTransactions.filter(t => t.transaction_type === 'sale');

    // تجميع المبيعات حسب الصنف
    const itemSales = {};
    sales.forEach(sale => {
      if (!itemSales[sale.item_id]) {
        itemSales[sale.item_id] = {
          item_id: sale.item_id,
          item_name: sale.item_name,
          quantity: 0,
          value: 0,
          profit: 0
        };
      }

      itemSales[sale.item_id].quantity += sale.quantity;
      itemSales[sale.item_id].value += sale.total_price;
      itemSales[sale.item_id].profit += (sale.profit || 0);
    });

    // تحويل إلى مصفوفة وترتيب حسب الربح
    return Object.values(itemSales)
      .sort((a, b) => b.profit - a.profit)
      .slice(0, 5); // أعلى 5 أصناف
  };

  // تصدير التقرير كملف CSV
  const exportToCSV = () => {
    try {
      let csvContent = '';
      let fileName = '';

      if (activeTab === 'inventory') {
        // تصدير تقرير المخزون
        csvContent = 'الرقم,الاسم,وحدة القياس,الكمية المتوفرة,الحد الأدنى,متوسط السعر,سعر البيع,آخر تحديث\n';

        inventory.forEach((item, index) => {
          const date = new Date(item.last_updated);
          const formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
          csvContent += `${index + 1},${item.name},${item.unit || ''},${item.current_quantity},${item.minimum_quantity},${item.avg_price},${item.selling_price},${formattedDate}\n`;
        });

        fileName = 'تقرير_المخزون.csv';
      } else if (activeTab === 'transactions') {
        // تصدير تقرير المعاملات
        csvContent = 'التاريخ,نوع المعاملة,الصنف,الكمية,السعر,الإجمالي,الربح,التفاصيل\n';

        filteredTransactions.forEach(t => {
          const date = new Date(t.transaction_date);
          const formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
          let type = '';
          let details = '';

          if (t.transaction_type === 'purchase') {
            type = 'شراء';
            details = '';
          } else if (t.transaction_type === 'sale') {
            type = 'بيع للعميل';
            // استخدام الوظيفة المساعدة للحصول على اسم العميل
            const customerName = getCustomerNameById(t.customer, customers);
            details = `إلى ${customerName}`;
          } else if (t.transaction_type === 'return') {
            type = 'إرجاع من العميل';
            // استخدام الوظيفة المساعدة للحصول على اسم العميل
            const customerName = getCustomerNameById(t.customer, customers);
            details = `من ${customerName}`;
          } else if (t.transaction_type === 'receiving') {
            type = 'استلام';
            details = `من ${t.source || 'غير محدد'}`;
          } else if (t.transaction_type === 'withdrawal') {
            type = 'صرف';
            details = `إلى ${t.source || 'غير محدد'}`;
          }

          // For returns, show negative values for quantity, total, and profit
          let quantity = t.quantity;
          let total_price = t.total_price;
          let profit = t.profit;
          if (t.transaction_type === 'return') {
            quantity = -Math.abs(t.quantity);
            total_price = -Math.abs(t.total_price || 0);
            profit = -Math.abs(t.profit || 0);
          }

          csvContent += `${formattedDate},${type},${t.item_name},${quantity},${t.price || ''},${total_price || ''},${profit || ''},${details}\n`;
        });

        fileName = 'تقرير_المعاملات.csv';
      } else if (activeTab === 'profits') {
        // تصدير تقرير الأرباح
        const transactionStats = calculateTransactionStats();

        csvContent = 'الفترة,إجمالي المبيعات,إجمالي المشتريات,إجمالي الربح\n';
        csvContent += `سنوي,${transactionStats.sales.value},${transactionStats.purchases.value},${profitValues.yearly}\n`;
        csvContent += `ربع سنوي,,,${profitValues.quarterly}\n`;
        csvContent += `نصف سنوي,,,${profitValues.halfYearly}\n`;
        csvContent += `ثلاثة أرباع سنوي,,,${profitValues.threeQuarters}\n`;

        fileName = 'تقرير_الأرباح.csv';
      }

      // إنشاء ملف CSV وتنزيله
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      link.setAttribute('href', url);
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showAlert('success', 'تم تصدير التقرير بنجاح');
    } catch (err) {
      console.error('Error exporting report:', err);
      showAlert('danger', 'فشل في تصدير التقرير');
    }
  };

  // طباعة التقرير المفصل الشامل
  const printComprehensiveReport = async () => {
    try {
      safeSetState(setIsLoadingSpecialReport, true);
      showAlert('info', 'جاري تحميل وإعداد التقرير المفصل الشامل...');

      // تحميل جميع البيانات المطلوبة للتقرير الشامل
      const filters = createDateFilters();

      // تحميل تقرير المخزون
      const inventoryResult = await window.api.invoke('get-inventory-report');

      // تحميل تقرير المعاملات
      const transactionsResult = await window.api.invoke('get-transactions-report', filters);

      // تحميل تقرير الأرباح
      const profitsResult = await window.api.invoke('get-profits-report', filters);

      // تحميل تقرير الإرجاعات
      const returnsResult = await window.api.invoke('get-return-transactions-report', filters);

      // تحميل تقرير الأصناف الأكثر مبيعًا
      const topSellingItemsResult = await window.api.reports.getTopSellingItemsReport(filters);

      // تحميل تقرير العملاء الأكثر شراءً
      const topCustomersResult = await window.api.reports.getTopCustomersReport(filters);

      // تحميل تقرير الأصناف التي تحتاج إعادة طلب
      const lowStockResult = await window.api.reports.getLowStockReport({ includeZeroQuantity: true });

      // إنشاء نافذة الطباعة
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        showAlert('danger', 'يرجى السماح بالنوافذ المنبثقة لطباعة التقرير');
        setIsLoadingSpecialReport(false);
        return;
      }

      // الحصول على إعدادات النظام
      const appSettings = window.appSettings || {};
      const companyName = appSettings.systemName || 'Hgrop - اتش قروب';
      const companyAddress = appSettings.address || 'للتصميم و تصنيع الأثاث والديكورات';
      const companyPhone = appSettings.phone || '';
      const companyLogo = appSettings.logoUrl || '';

      // إعداد محتوى التقرير
      let reportContent = '';

      // إضافة قسم تقرير المخزون
      if (inventoryResult && inventoryResult.inventory) {
        const inventoryStats = calculateInventoryStats();
        reportContent += generateInventoryReportHTML(inventoryResult.inventory, inventoryStats);
      }

      // إضافة قسم تقرير المعاملات
      if (transactionsResult && transactionsResult.transactions) {
        const transactionStats = calculateTransactionStats();
        reportContent += generateTransactionsReportHTML(transactionsResult.transactions, transactionStats);
      }

      // إضافة قسم تقرير الأرباح
      if (profitsResult && profitsResult.stats) {
        reportContent += generateProfitsReportHTML(profitValues);
      }

      // إضافة قسم تقرير الإرجاعات
      if (returnsResult && returnsResult.returnTransactions) {
        reportContent += generateReturnsReportHTML(returnsResult.returnTransactions);
      }

      // إضافة قسم تقرير الأصناف الأكثر مبيعًا
      if (topSellingItemsResult && topSellingItemsResult.items) {
        reportContent += generateTopSellingItemsReportHTML(topSellingItemsResult.items);
      }

      // إضافة قسم تقرير العملاء الأكثر شراءً
      if (topCustomersResult && topCustomersResult.customers) {
        reportContent += generateTopCustomersReportHTML(topCustomersResult.customers);
      }

      // إضافة قسم تقرير الأصناف التي تحتاج إعادة طلب
      if (lowStockResult && lowStockResult.items) {
        reportContent += generateLowStockReportHTML(lowStockResult.items);
      }

      // إعداد صفحة الطباعة
      printWindow.document.open();
      printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
          <head>
            <meta charset="UTF-8">
            <title>التقرير المفصل الشامل</title>
            <style>
              @media print {
                @page {
                  size: A4;
                  margin: 1cm;
                }
              }

              body {
                font-family: Arial, sans-serif;
                direction: rtl;
                padding: 20px;
                color: #333;
                line-height: 1.5;
              }

              .report-container {
                max-width: 1200px;
                margin: 0 auto;
              }

              .report-header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #1a3a5f;
              }

              .company-logo {
                max-width: 150px;
                max-height: 80px;
                margin-bottom: 10px;
              }

              .company-name {
                font-size: 24px;
                font-weight: bold;
                margin: 0;
                color: #1a3a5f;
              }

              .company-address {
                font-size: 14px;
                margin: 5px 0;
              }

              .report-title {
                font-size: 22px;
                font-weight: bold;
                margin: 15px 0 5px;
                color: #1a3a5f;
              }

              .report-date {
                font-size: 14px;
                margin-bottom: 10px;
                color: #666;
              }

              .section {
                margin-bottom: 30px;
                page-break-inside: avoid;
              }

              .section-title {
                font-size: 18px;
                font-weight: bold;
                margin: 0 0 15px;
                padding-bottom: 5px;
                border-bottom: 1px solid #ddd;
                color: #1a3a5f;
              }

              .stats-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;
                margin-bottom: 20px;
              }

              .stat-card {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
                text-align: center;
              }

              .stat-value {
                font-size: 18px;
                font-weight: bold;
                color: #1a3a5f;
                margin: 5px 0;
              }

              .stat-title {
                font-size: 14px;
                color: #666;
              }

              table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
                font-size: 12px;
              }

              th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: right;
              }

              th {
                background-color: #f2f2f2;
                font-weight: bold;
                color: #333;
              }

              tr:nth-child(even) {
                background-color: #f9f9f9;
              }

              .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 10px;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
              }

              .page-break {
                page-break-before: always;
              }

              .text-center {
                text-align: center;
              }

              .text-right {
                text-align: right;
              }

              .text-success {
                color: #27ae60;
              }

              .text-danger {
                color: #e74c3c;
              }
            </style>
          </head>
          <body>
            <div class="report-container">
              <div class="report-header">
                ${companyLogo ? `<img src="${companyLogo}" class="company-logo" alt="شعار الشركة" />` : ''}
                <h1 class="company-name">${companyName}</h1>
                <p class="company-address">${companyAddress}</p>
                ${companyPhone ? `<p class="company-address">${companyPhone}</p>` : ''}
                <h2 class="report-title">التقرير المفصل الشامل</h2>
                <p class="report-date">
                  الفترة: ${dateRange === 'quarter' ? 'الربع الحالي' :
                           dateRange === 'halfYear' ? 'النصف سنوي' :
                           dateRange === 'threeQuarters' ? 'ثلاثة أرباع السنة' :
                           dateRange === 'year' ? 'السنة الحالية' : 'جميع الفترات'}
                </p>
                <p class="report-date">تاريخ التقرير: ${formatDate(new Date().toISOString())}</p>
              </div>

              ${reportContent}

              <div class="footer">
                <p>${companyName} © ${new Date().getFullYear()} | جميع الحقوق محفوظة</p>
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخزون</p>
              </div>
            </div>

            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                }, 1000);
              };
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();

      showAlert('success', 'تم إرسال التقرير المفصل الشامل للطباعة');
    } catch (error) {
      console.error('خطأ في طباعة التقرير المفصل الشامل:', error);
      showAlert('danger', 'حدث خطأ أثناء إعداد التقرير المفصل الشامل');
    } finally {
      safeSetState(setIsLoadingSpecialReport, false);
    }
  };

  // طباعة التقرير (وظيفة احتياطية)
  const printReport = () => {
    try {
      // إضافة عنوان للتقرير حسب النوع
      let reportTitle = '';
      switch (activeTab) {
        case 'inventory':
          reportTitle = 'تقرير المخزون';
          break;
        case 'transactions':
          reportTitle = 'تقرير المعاملات';
          break;
        case 'profits':
          reportTitle = 'تقرير الأرباح';
          break;
        default:
          reportTitle = 'تقرير';
      }

      // إضافة معلومات التاريخ
      let dateInfo = '';
      // تم إزالة خيارات الشهر الحالي والفترة المخصصة
      if (dateRange === 'quarter') {
        dateInfo = 'الربع الحالي';
      } else if (dateRange === 'halfYear') {
        dateInfo = 'النصف سنوي';
      } else if (dateRange === 'threeQuarters') {
        dateInfo = 'ثلاثة أرباع السنة';
      } else if (dateRange === 'year') {
        dateInfo = 'السنة الحالية';
      } else {
        dateInfo = 'جميع الفترات';
      }

      // إنشاء عنصر مؤقت لتخزين معلومات الطباعة
      const printElement = document.createElement('div');
      printElement.className = 'print-header';
      printElement.innerHTML = `
        <div style="text-align: center; margin-bottom: 20px;">
          <h1 style="font-size: 24px; margin-bottom: 5px;">${reportTitle}</h1>
          <p style="font-size: 14px; color: #666;">${dateInfo}</p>
          <p style="font-size: 14px; color: #666;">تاريخ الطباعة: ${formatDate(new Date().toISOString())}</p>
        </div>
      `;

      // إضافة العنصر المؤقت قبل محتوى التقرير
      if (reportRef.current) {
        reportRef.current.prepend(printElement);
      }

      // طباعة التقرير
      window.print();

      // إزالة العنصر المؤقت بعد الطباعة
      if (reportRef.current && printElement.parentNode === reportRef.current) {
        reportRef.current.removeChild(printElement);
      }

      showAlert('success', 'تم إرسال التقرير للطباعة');
    } catch (err) {
      console.error('خطأ في طباعة التقرير:', err);
      showAlert('danger', 'فشل في طباعة التقرير');

      // محاولة استخدام طريقة بديلة للطباعة
      try {
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
          alert('يرجى السماح بالنوافذ المنبثقة لطباعة التقرير');
          return;
        }

        // الحصول على محتوى التقرير
        const reportContent = reportRef.current.innerHTML;

        // إعداد صفحة الطباعة
        printWindow.document.open();
        printWindow.document.write(`
          <!DOCTYPE html>
          <html dir="rtl" lang="ar">
            <head>
              <meta charset="UTF-8">
              <title>تقرير</title>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  direction: rtl;
                  padding: 20px;
                }

                @media print {
                  body {
                    padding: 0;
                  }

                  button {
                    display: none;
                  }
                }
              </style>
            </head>
            <body>
              ${reportContent}
              <script>
                window.onload = function() {
                  setTimeout(function() {
                    window.print();
                    window.close();
                  }, 500);
                };
              </script>
            </body>
          </html>
        `);
        printWindow.document.close();
      } catch (backupError) {
        console.error('خطأ في الطريقة البديلة للطباعة:', backupError);
        alert('فشلت جميع محاولات الطباعة. يرجى التحقق من إعدادات المتصفح.');
      }
    }
  };

  // عرض تنبيه
  const showAlert = (type, message) => {
    safeSetState(setAlert, { show: true, type, message });

    // إلغاء المؤقت السابق إذا كان موجودًا
    if (alertTimerRef.current) {
      clearTimeout(alertTimerRef.current);
    }

    // إخفاء التنبيه بعد 3 ثوان
    alertTimerRef.current = setTimeout(() => {
      safeSetState(setAlert, { show: false, type: '', message: '' });
      alertTimerRef.current = null;
    }, 3000);
  };

  // تحديث قيم الربح في قاعدة البيانات (وظيفة داخلية)
  // تم تحويل هذه الوظيفة إلى وظيفة داخلية فقط حيث تم إزالة زر المزامنة اليدوية
  // وتم تنفيذ المزامنة التلقائية في وظيفة fetchProfitsReport
  const _handleUpdateProfitValues = async () => {
    try {
      safeSetState(setIsUpdatingProfits, true);

      // استدعاء وظيفة تحديث قيم الربح
      await updateProfitValuesInDatabase(window.api, (value) => safeSetState(setIsUpdatingProfits, value), showAlert);

      // تصحيح قيم الربح السالبة
      try {
        // استدعاء وظيفة تصحيح قيم الربح السالبة
        const fixResult = await window.api.invoke('fix-negative-profits');
        if (fixResult && fixResult.success) {
          console.log(`تم تصحيح ${fixResult.updatedCount} قيمة ربح سالبة`);
        }
      } catch (fixError) {
        console.error('خطأ في تصحيح قيم الربح السالبة:', fixError);
        // نستمر في التنفيذ حتى لو فشل تصحيح القيم السالبة
      }

      // إعادة تحميل تقرير الأرباح بعد التحديث
      const filters = createDateFilters();
      const result = await window.api.invoke('get-profits-report', filters);

      if (result && result.stats) {
        // تحديث الأرباح من نتيجة الخادم
        const yearlyProfit = Math.max(0, result.stats.totalProfit || 0);

        // حساب الأرباح للفترات المختلفة
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth();

        // تصفية المعاملات حسب الفترات الزمنية
        let quarterlyProfit = 0;
        let halfYearlyProfit = 0;
        let threeQuartersProfit = 0;

        if (result.transactions && result.transactions.length > 0) {
          // دالة مساعدة لحساب الربح مع مصاريف النقل
          const calculateProfitWithTransport = (transaction) => {
            // إذا كان الربح محسوباً بالفعل ويتضمن مصاريف النقل، استخدمه
            if (transaction.profit && transaction.profit > 0) {
              return transaction.profit;
            }

            // إذا لم يكن محسوباً، احسبه بناءً على سعر البيع وسعر التكلفة
            if (transaction.selling_price > 0 && transaction.avg_price > 0) {
              // حساب الربح الأساسي
              const basicProfit = (transaction.selling_price - transaction.avg_price) * transaction.quantity;

              // خصم مصاريف النقل إذا كانت متوفرة
              const transportCost = transaction.transport_cost || 0;
              const finalProfit = basicProfit - transportCost;

              return Math.max(0, finalProfit);
            }

            // إذا لم تكن البيانات متوفرة، استخدم تقدير 20%
            return transaction.selling_price * transaction.quantity * 0.2;
          };

          // الربع سنوي
          const quarterlyTransactions = result.transactions.filter(t => {
            const transactionDate = new Date(t.transaction_date);
            return (
              transactionDate.getFullYear() === currentYear &&
              transactionDate.getMonth() >= currentMonth - 3 &&
              transactionDate.getMonth() <= currentMonth
            );
          });

          quarterlyProfit = quarterlyTransactions.reduce((sum, t) => {
            if (t.transaction_type === 'sale') {
              return sum + calculateProfitWithTransport(t);
            } else if (t.transaction_type === 'return') {
              return sum - Math.max(0, calculateProfitWithTransport(t));
            }
            return sum;
          }, 0);

          // النصف سنوي
          const halfYearlyTransactions = result.transactions.filter(t => {
            const transactionDate = new Date(t.transaction_date);
            return (
              transactionDate.getFullYear() === currentYear &&
              transactionDate.getMonth() >= currentMonth - 6 &&
              transactionDate.getMonth() <= currentMonth
            );
          });

          halfYearlyProfit = halfYearlyTransactions.reduce((sum, t) => {
            if (t.transaction_type === 'sale') {
              return sum + calculateProfitWithTransport(t);
            } else if (t.transaction_type === 'return') {
              return sum - Math.max(0, calculateProfitWithTransport(t));
            }
            return sum;
          }, 0);

          // ثلاثة أرباع السنة
          const threeQuartersTransactions = result.transactions.filter(t => {
            const transactionDate = new Date(t.transaction_date);
            return (
              transactionDate.getFullYear() === currentYear &&
              transactionDate.getMonth() >= currentMonth - 9 &&
              transactionDate.getMonth() <= currentMonth
            );
          });

          threeQuartersProfit = threeQuartersTransactions.reduce((sum, t) => {
            if (t.transaction_type === 'sale') {
              return sum + calculateProfitWithTransport(t);
            } else if (t.transaction_type === 'return') {
              return sum - Math.max(0, calculateProfitWithTransport(t));
            }
            return sum;
          }, 0);
        }

        safeSetState(setProfits, {
          quarterly: quarterlyProfit,
          halfYearly: halfYearlyProfit,
          threeQuarters: threeQuartersProfit,
          yearly: yearlyProfit
        });
      }

      console.log('تم تحديث قيم الربح بنجاح');
    } catch (error) {
      console.error('خطأ في تحديث قيم الربح:', error);
    } finally {
      safeSetState(setIsUpdatingProfits, false);
    }
  };

  // تنسيق التاريخ (بالتنسيق الميلادي)
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${year}/${month}/${day}`;
  };

  // تنسيق المبلغ كعملة (خاص بالتقارير)
  const formatReportAmount = (amount) => {
    if (amount === undefined || amount === null) return '0.00 ر.س';

    // تحويل المبلغ إلى رقم
    const numAmount = Number(amount);

    // تنسيق الرقم بفاصلتين عشريتين
    return numAmount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,') + ' ر.س';
  };

  // طباعة فاتورة العميل
  const printInvoice = (invoice) => {
    try {
      // الحصول على معلومات العميل
      const customer = customers.find(c => c.id === selectedCustomer);

      if (!customer) {
        showAlert('warning', 'لم يتم العثور على معلومات العميل');
        return;
      }

      // إنشاء نافذة الطباعة
      const printWindow = window.open('', '_blank');

      if (!printWindow) {
        showAlert('danger', 'يرجى السماح بالنوافذ المنبثقة لطباعة الفاتورة');
        return;
      }

      // محاولة الحصول على إعدادات النظام من النافذة العالمية
      const appSettings = window.appSettings || {};
      const companyName = appSettings.systemName || 'شركة اتش قروب';
      const companyAddress = appSettings.address || 'للتصميم و تصنيع الأثاث والديكورات';
      const companyPhone = appSettings.phone || '';
      const companyLogo = appSettings.logoUrl || '';

      // حساب إجمالي الفاتورة
      const totalAmount = invoice.total_amount ||
        (invoice.items ? invoice.items.reduce((sum, item) => sum + (item.total_price || 0), 0) : 0);

      // إنشاء رقم الفاتورة
      const invoiceNumber = invoice.invoice_number || `H-${invoice.id?.toString().padStart(5, '0') || '00000'}`;

      // تنسيق التاريخ
      const invoiceDate = formatDate(invoice.transaction_date || new Date().toISOString());

      // إعداد محتوى الفاتورة
      printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
          <head>
            <meta charset="UTF-8">
            <title>فاتورة رقم ${invoiceNumber}</title>
            <style>
              @page {
                size: A4 portrait;
                margin: 0.5cm;
              }
              body {
                font-family: 'Arial', sans-serif;
                direction: rtl;
                margin: 0;
                padding: 0;
                color: #333;
                background-color: white;
              }
              .invoice-container {
                width: 100%;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                box-sizing: border-box;
              }
              .header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid #ddd;
              }
              .company-info {
                text-align: right;
              }
              .company-name {
                font-size: 22px;
                font-weight: bold;
                color: #333;
                margin: 0 0 5px 0;
              }
              .company-address {
                font-size: 14px;
                color: #666;
                margin: 0;
              }
              .invoice-info {
                text-align: left;
              }
              .invoice-title {
                font-size: 18px;
                font-weight: bold;
                margin: 0 0 5px 0;
              }
              .invoice-number {
                font-size: 14px;
                margin: 0 0 5px 0;
              }
              .invoice-date {
                font-size: 14px;
                margin: 0;
              }
              .customer-info {
                margin-bottom: 20px;
              }
              .customer-title {
                font-size: 16px;
                font-weight: bold;
                margin: 0 0 10px 0;
              }
              .customer-details {
                display: flex;
                justify-content: space-between;
              }
              .customer-phone {
                font-size: 14px;
              }
              .invoice-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
              }
              .invoice-table th, .invoice-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: center;
              }
              .invoice-table th {
                background-color: #f2f2f2;
                font-weight: bold;
              }
              .invoice-table tr:nth-child(even) {
                background-color: #f9f9f9;
              }
              .totals {
                margin-top: 20px;
                display: flex;
                flex-direction: column;
                align-items: flex-end;
              }
              .total-row {
                display: flex;
                justify-content: space-between;
                width: 300px;
                margin-bottom: 5px;
              }
              .total-label {
                font-weight: bold;
              }
              .total-value {
                font-weight: bold;
              }
              .terms {
                margin-top: 30px;
                border-top: 1px solid #ddd;
                padding-top: 10px;
              }
              .terms-title {
                font-weight: bold;
                margin-bottom: 5px;
              }
              .terms-item {
                margin-bottom: 5px;
                font-size: 14px;
              }
              .signature {
                margin-top: 40px;
                text-align: left;
              }
              .signature-line {
                display: inline-block;
                width: 200px;
                border-bottom: 1px solid #333;
                margin-bottom: 5px;
              }
              .signature-label {
                font-size: 14px;
              }
              .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
              }
              .highlight {
                color: #ff6600;
                font-weight: bold;
              }
              .logo {
                max-width: 100px;
                max-height: 100px;
              }
              @media print {
                body {
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
                }
                .no-print {
                  display: none;
                }
              }
            </style>
          </head>
          <body>
            <div class="invoice-container">
              <!-- Header -->
              <div class="header">
                <div class="company-info">
                  ${companyLogo ? `<img src="${companyLogo}" class="logo" alt="شعار الشركة" />` : ''}
                  <h1 class="company-name">${companyName}</h1>
                  <p class="company-address">${companyAddress}</p>
                  ${companyPhone ? `<p class="company-address">${companyPhone}</p>` : ''}
                </div>
                <div class="invoice-info">
                  <p class="invoice-title">فاتورة رقم: ${invoiceNumber}</p>
                  <p class="invoice-date">التاريخ: ${invoiceDate}</p>
                </div>
              </div>

              <!-- Customer Info -->
              <div class="customer-info">
                <p class="customer-title">صاحب الطلب: ${customer.name}</p>
                <div class="customer-details">
                  <p class="customer-phone">رقم الهاتف: ${customer.phone || ''}</p>
                </div>
              </div>

              <!-- Invoice Items -->
              <table class="invoice-table">
                <thead>
                  <tr>
                    <th>وصف المنتج</th>
                    <th>السعر</th>
                    <th>الكمية</th>
                    <th>المجموع</th>
                  </tr>
                </thead>
                <tbody>
                  ${invoice.items && invoice.items.length > 0 ?
                    invoice.items.map((item, index) => `
                      <tr>
                        <td>${item.item_name || 'منتج'}</td>
                        <td>${item.price ? item.price.toFixed(3) : '0.000'}</td>
                        <td>${item.quantity || '1'}</td>
                        <td>${item.total_price ? item.total_price.toFixed(3) : '0.000'}</td>
                      </tr>
                    `).join('') :
                    '<tr><td colspan="4" style="text-align: center;">لا توجد أصناف في هذه الفاتورة</td></tr>'
                  }
                </tbody>
              </table>

              <!-- Totals -->
              <div class="totals">
                <div class="total-row">
                  <span class="total-label">المبلغ المستلم:</span>
                  <span class="total-value">0.000</span>
                </div>
                <div class="total-row">
                  <span class="total-label">المبلغ الباقي:</span>
                  <span class="total-value">0.000</span>
                </div>
                <div class="total-row">
                  <span class="total-label">المبلغ الإجمالي:</span>
                  <span class="total-value">${totalAmount.toFixed(3)}</span>
                </div>
              </div>

              <!-- Terms and Conditions -->
              <div class="terms">
                <p class="terms-title">بنود الاتفاق:</p>
                <p class="terms-item">مدة العرض 3 أيام من تاريخ اصدار الفاتورة.</p>
                <p class="terms-item">دفعة متقدمة بنسبة 50% عند الموافقة على العرض.</p>
                <p class="terms-item">دفعــة ثانية بنسبة 30% قبل 3 أيام من موعد التركيب.</p>
                <p class="terms-item">مدة التسليم 45 يوم من تاريخ استلام الدفعة الأولى.</p>
                <p class="terms-item highlight">هذا العرض شامل النقل والتنزيل الطابق الأرضي فقط.</p>
              </div>

              <!-- Signature -->
              <div class="signature">
                <div class="signature-line"></div>
                <p class="signature-label">التوقيع</p>
              </div>

              <!-- Footer -->
              <div class="footer">
                <p>نشكرك على ثقتك بنا ونتمنى لك تجربة ممتعة مع خدماتنا، شكراً لكم.</p>
              </div>
            </div>

            <script>
              window.onload = function() {
                // طباعة تلقائية بعد تحميل الصفحة
                setTimeout(function() {
                  window.print();
                }, 1000);
              };
            </script>
          </body>
        </html>
      `);

      printWindow.document.close();
      showAlert('success', 'تم إرسال الفاتورة للطباعة');
    } catch (error) {
      console.error('خطأ في طباعة الفاتورة:', error);
      showAlert('danger', 'حدث خطأ أثناء طباعة الفاتورة');
    }
  };

  // تحميل العملاء الدائمين
  useEffect(() => {
    if (customers && customers.length > 0) {
      // تصفية العملاء الدائمين فقط
      const regularCustomersList = customers.filter(c => c.customer_type === 'regular');
      setRegularCustomers(regularCustomersList);
    }
  }, [customers]);

  // تحميل مبيعات العملاء الفرعيين عند اختيار عميل دائم
  useEffect(() => {
    if (selectedRegularCustomer && customers && customers.length > 0) {
      // البحث عن العميل الدائم
      const regularCustomer = customers.find(c => c.id === selectedRegularCustomer);

      if (regularCustomer) {
        // البحث عن العملاء الفرعيين
        const subCustomers = customers.filter(c =>
          c.customer_type === 'sub' &&
          (c.parent_id === selectedRegularCustomer || c.parent_id === String(selectedRegularCustomer))
        );

        // جمع مبيعات العملاء الفرعيين
        const salesData = [];
        let totalSales = 0;
        let totalProfit = 0;

        // استخدام Promise.all لتحميل مبيعات جميع العملاء الفرعيين
        Promise.all(
          subCustomers.map(async (subCustomer) => {
            try {
              // استخدام API لجلب مبيعات العميل من قاعدة البيانات
              const salesResult = await window.api.invoke('get-customer-sales', subCustomer.id);

              if (salesResult && salesResult.sales && Array.isArray(salesResult.sales)) {
                // حساب إجمالي مبيعات وأرباح العميل الفرعي
                const subCustomerTotalSales = salesResult.sales.reduce((sum, sale) => sum + (Number(sale.total_price) || 0), 0);
                const subCustomerTotalProfit = salesResult.sales.reduce((sum, sale) => sum + (Number(sale.profit) || 0), 0);

                // إضافة إجمالي المبيعات والأرباح للعميل الفرعي
                const customerData = {
                  subCustomerId: subCustomer.id,
                  subCustomerName: subCustomer.name,
                  salesCount: salesResult.sales.length,
                  totalSales: subCustomerTotalSales,
                  totalProfit: subCustomerTotalProfit,
                  salesHistory: salesResult.sales,
                  // إضافة المبيعات المجمعة من الخادم
                  groupedSales: salesResult.groupedSales || []
                };

                // إضافة إلى مصفوفة بيانات المبيعات
                salesData.push(customerData);

                // إضافة إلى الإجمالي الكلي
                totalSales += subCustomerTotalSales;
                totalProfit += subCustomerTotalProfit;

                return customerData;
              } else {
                // إضافة العميل الفرعي بدون مبيعات
                const emptyCustomerData = {
                  subCustomerId: subCustomer.id,
                  subCustomerName: subCustomer.name,
                  salesCount: 0,
                  totalSales: 0,
                  totalProfit: 0,
                  salesHistory: [],
                  groupedSales: []
                };

                salesData.push(emptyCustomerData);
                return emptyCustomerData;
              }
            } catch (error) {
              console.error(`خطأ في تحميل مبيعات العميل الفرعي ${subCustomer.name}:`, error);

              // في حالة الخطأ، استخدم البيانات المحلية
              if (subCustomer.sales_history && subCustomer.sales_history.length > 0) {
                // حساب إجمالي مبيعات وأرباح العميل الفرعي
                const subCustomerTotalSales = subCustomer.sales_history.reduce((sum, sale) => sum + (Number(sale.total_price) || 0), 0);
                const subCustomerTotalProfit = subCustomer.sales_history.reduce((sum, sale) => sum + (Number(sale.profit) || 0), 0);

                // تجميع المبيعات حسب رقم الفاتورة
                const salesByInvoice = {};

                subCustomer.sales_history.forEach(sale => {
                  const invoiceNumber = sale.invoice_number || `INV-${sale.id.toString().padStart(5, '0')}`;

                  if (!salesByInvoice[invoiceNumber]) {
                    salesByInvoice[invoiceNumber] = {
                      invoice_number: invoiceNumber,
                      items: [],
                      transaction_date: sale.transaction_date,
                      notes: sale.notes || ''
                    };
                  }

                  salesByInvoice[invoiceNumber].items.push(sale);
                });

                // تحويل الكائن إلى مصفوفة
                const groupedSales = Object.values(salesByInvoice);

                // إضافة إجمالي المبيعات والأرباح للعميل الفرعي
                const customerData = {
                  subCustomerId: subCustomer.id,
                  subCustomerName: subCustomer.name,
                  salesCount: subCustomer.sales_history.length,
                  totalSales: subCustomerTotalSales,
                  totalProfit: subCustomerTotalProfit,
                  salesHistory: subCustomer.sales_history,
                  groupedSales: groupedSales
                };

                // إضافة إلى مصفوفة بيانات المبيعات
                salesData.push(customerData);

                // إضافة إلى الإجمالي الكلي
                totalSales += subCustomerTotalSales;
                totalProfit += subCustomerTotalProfit;

                return customerData;
              } else {
                // إضافة العميل الفرعي بدون مبيعات
                const emptyCustomerData = {
                  subCustomerId: subCustomer.id,
                  subCustomerName: subCustomer.name,
                  salesCount: 0,
                  totalSales: 0,
                  totalProfit: 0,
                  salesHistory: [],
                  groupedSales: []
                };

                salesData.push(emptyCustomerData);
                return emptyCustomerData;
              }
            }
          })
        ).then(() => {
          // تحديث بيانات مبيعات العملاء الفرعيين
          setSubCustomerSales({
            regularCustomer,
            subCustomers: salesData,
            totalSales,
            totalProfit
          });
        }).catch(error => {
          console.error('خطأ في تحميل مبيعات العملاء الفرعيين:', error);
          // إعادة تعيين بيانات مبيعات العملاء الفرعيين
          setSubCustomerSales([]);
        });
      }
    } else {
      // إعادة تعيين بيانات مبيعات العملاء الفرعيين
      setSubCustomerSales([]);
    }
  }, [selectedRegularCustomer, customers]);

  // تحميل تقرير المخزون
  useEffect(() => {
    const fetchInventoryReport = async () => {
      if (activeTab === 'inventory' || activeTab === 'returns') {
        try {
          console.log('جاري الحصول على تقرير المخزون من الخادم...');

          const result = await window.api.invoke('get-inventory-report');

          if (result && result.inventory) {
            // التحقق من وجود رسالة خطأ
            if (result.error) {
              console.error('خطأ في تقرير المخزون من الخادم:', result.error);
              showAlert('warning', `خطأ في تقرير المخزون: ${result.error}`);
              return;
            }

            // تحديث بيانات المخزون
            if (result.inventory.length > 0) {
              // تحديث بيانات المخزون في سياق التطبيق
              // هذا يعتمد على وجود دالة setInventory في سياق التطبيق
              if (typeof setInventory === 'function') {
                setInventory(result.inventory);
                console.log('تم تحديث بيانات المخزون من الخادم:', result.inventory.length);
              }
            }
          } else {
            console.warn('لم يتم العثور على بيانات المخزون في النتيجة:', result);
          }
        } catch (error) {
          console.error('خطأ في الحصول على تقرير المخزون:', error);
          showAlert('danger', 'حدث خطأ أثناء تحميل تقرير المخزون');
        }
      }
    };

    fetchInventoryReport();
  }, [activeTab]);

  // استيراد وظائف حساب الربح
  const { calculateProfit, calculateProfitMargin } = require('../utils/profitCalculator');

  // وظائف توليد HTML لأقسام التقرير المفصل الشامل

  /**
   * توليد HTML لقسم تقرير المخزون
   * @param {Array} inventory - بيانات المخزون
   * @param {Object} stats - إحصائيات المخزون
   * @returns {string} - HTML لقسم تقرير المخزون
   */
  const generateInventoryReportHTML = (inventory, stats) => {
    let html = `
      <div class="section">
        <h3 class="section-title">تقرير المخزون</h3>

        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-title">إجمالي الأصناف</div>
            <div class="stat-value">${stats.total}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">أصناف تحت الحد الأدنى</div>
            <div class="stat-value">${stats.low}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">أصناف نفذت الكمية</div>
            <div class="stat-value">${stats.out}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">قيمة المخزون</div>
            <div class="stat-value">${formatCurrency(stats.value)}</div>
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th>#</th>
              <th>الاسم</th>
              <th>وحدة القياس</th>
              <th>الكمية المتوفرة</th>
              <th>الحد الأدنى</th>
              <th>متوسط السعر</th>
              <th>سعر البيع</th>
              <th>القيمة</th>
            </tr>
          </thead>
          <tbody>
    `;

    // إضافة صفوف الجدول (بحد أقصى 20 صنف للحفاظ على حجم التقرير)
    const maxItems = Math.min(inventory.length, 20);
    for (let i = 0; i < maxItems; i++) {
      const item = inventory[i];
      const value = item.current_quantity * item.avg_price;

      html += `
        <tr>
          <td>${i + 1}</td>
          <td>${item.name}</td>
          <td>${item.unit || '-'}</td>
          <td>${item.current_quantity === 0 ?
            '<span class="text-danger">نفذت الكمية</span>' :
            (item.current_quantity <= item.minimum_quantity ?
              `<span class="text-danger">${item.current_quantity}</span>` :
              item.current_quantity)}</td>
          <td>${item.minimum_quantity}</td>
          <td>${formatCurrency(item.avg_price)}</td>
          <td>${formatCurrency(item.selling_price)}</td>
          <td>${formatCurrency(value)}</td>
        </tr>
      `;
    }

    // إضافة صف إجمالي إذا كان هناك المزيد من الأصناف
    if (inventory.length > maxItems) {
      html += `
        <tr>
          <td colspan="8" class="text-center">
            تم عرض ${maxItems} صنف من إجمالي ${inventory.length} صنف
          </td>
        </tr>
      `;
    }

    html += `
          </tbody>
        </table>
      </div>
      <div class="page-break"></div>
    `;

    return html;
  };

  /**
   * توليد HTML لقسم تقرير المعاملات
   * @param {Array} transactions - بيانات المعاملات
   * @param {Object} stats - إحصائيات المعاملات
   * @returns {string} - HTML لقسم تقرير المعاملات
   */
  const generateTransactionsReportHTML = (transactions, stats) => {
    let html = `
      <div class="section">
        <h3 class="section-title">تقرير المعاملات</h3>

        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-title">عمليات الشراء</div>
            <div class="stat-value">${stats.purchases.count}</div>
            <div>${formatCurrency(stats.purchases.value)}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">عمليات البيع</div>
            <div class="stat-value">${stats.sales.count}</div>
            <div>${formatCurrency(stats.sales.value)}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">إجمالي الربح</div>
            <div class="stat-value text-success">${formatCurrency(stats.sales.profit)}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">نسبة الربح</div>
            <div class="stat-value">${calculateProfitMargin(stats.sales.profit, stats.sales.value).toFixed(1)}%</div>
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th>التاريخ</th>
              <th>نوع المعاملة</th>
              <th>الصنف</th>
              <th>الكمية</th>
              <th>السعر</th>
              <th>الإجمالي</th>
              <th>التفاصيل</th>
            </tr>
          </thead>
          <tbody>
    `;

    // إضافة صفوف الجدول (بحد أقصى 20 معاملة للحفاظ على حجم التقرير)
    const maxTransactions = Math.min(transactions.length, 20);
    for (let i = 0; i < maxTransactions; i++) {
      const t = transactions[i];

      let type = '';
      let details = '';

      if (t.transaction_type === 'purchase') {
        type = 'شراء';
        details = '';
      } else if (t.transaction_type === 'sale') {
        type = 'بيع للعميل';
        details = `إلى ${t.customer_name || 'عميل'}`;
      } else if (t.transaction_type === 'return') {
        type = 'إرجاع من العميل';
        details = `من ${t.customer_name || 'عميل'}`;
      }

      html += `
        <tr>
          <td>${formatDate(t.transaction_date)}</td>
          <td>${type}</td>
          <td>${t.item_name}</td>
          <td>${t.quantity}</td>
          <td>${formatCurrency(t.price)}</td>
          <td>${formatCurrency(t.total_price)}</td>
          <td>${details}</td>
        </tr>
      `;
    }

    // إضافة صف إجمالي إذا كان هناك المزيد من المعاملات
    if (transactions.length > maxTransactions) {
      html += `
        <tr>
          <td colspan="7" class="text-center">
            تم عرض ${maxTransactions} معاملة من إجمالي ${transactions.length} معاملة
          </td>
        </tr>
      `;
    }

    html += `
          </tbody>
        </table>
      </div>
      <div class="page-break"></div>
    `;

    return html;
  };

  /**
   * توليد HTML لقسم تقرير الأرباح
   * @param {Object} profitValues - قيم الأرباح
   * @returns {string} - HTML لقسم تقرير الأرباح
   */
  const generateProfitsReportHTML = (profitValues) => {
    return `
      <div class="section">
        <h3 class="section-title">تقرير الأرباح</h3>

        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-title">الربح الربع سنوي</div>
            <div class="stat-value text-success">${formatCurrency(profitValues.quarterly)}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">الربح النصف سنوي</div>
            <div class="stat-value text-success">${formatCurrency(profitValues.halfYearly)}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">الربح ثلاثة أرباع سنوي</div>
            <div class="stat-value text-success">${formatCurrency(profitValues.threeQuarters)}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">الربح السنوي</div>
            <div class="stat-value text-success">${formatCurrency(profitValues.yearly)}</div>
          </div>
        </div>

        <div style="margin-top: 20px;">
          <h4 style="margin-bottom: 10px;">توزيع الأرباح السنوية المقترح:</h4>
          <table>
            <thead>
              <tr>
                <th>البند</th>
                <th>النسبة</th>
                <th>القيمة</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>الاستثمار وتطوير الأعمال</td>
                <td>30%</td>
                <td>${formatCurrency(profitValues.yearly * 0.3)}</td>
              </tr>
              <tr>
                <td>المكافآت والحوافز</td>
                <td>20%</td>
                <td>${formatCurrency(profitValues.yearly * 0.2)}</td>
              </tr>
              <tr>
                <td>الاحتياطي</td>
                <td>20%</td>
                <td>${formatCurrency(profitValues.yearly * 0.2)}</td>
              </tr>
              <tr>
                <td>توزيعات الأرباح</td>
                <td>30%</td>
                <td>${formatCurrency(profitValues.yearly * 0.3)}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="page-break"></div>
    `;
  };

  /**
   * توليد HTML لقسم تقرير الإرجاعات
   * @param {Array} returns - بيانات الإرجاعات
   * @returns {string} - HTML لقسم تقرير الإرجاعات
   */
  const generateReturnsReportHTML = (returns) => {
    if (!returns || returns.length === 0) {
      return `
        <div class="section">
          <h3 class="section-title">تقرير الإرجاعات</h3>
          <p class="text-center">لا توجد عمليات إرجاع للعرض</p>
        </div>
        <div class="page-break"></div>
      `;
    }

    // حساب إحصائيات الإرجاعات
    const returnsCount = returns.length;
    const returnsValue = returns.reduce((sum, r) => sum + Math.max(0, r.total_price || 0), 0);
    const returnsProfit = returns.reduce((sum, r) => sum + Math.max(0, r.profit || 0), 0);

    let html = `
      <div class="section">
        <h3 class="section-title">تقرير الإرجاعات</h3>

        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-title">عدد الإرجاعات</div>
            <div class="stat-value">${returnsCount}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">إجمالي قيمة الإرجاعات</div>
            <div class="stat-value">${formatCurrency(returnsValue)}</div>
          </div>

          <div class="stat-card">
            <div class="stat-title">إجمالي الربح المفقود</div>
            <div class="stat-value text-danger">${formatCurrency(returnsProfit)}</div>
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th>التاريخ</th>
              <th>العميل</th>
              <th>الصنف</th>
              <th>الكمية</th>
              <th>السعر</th>
              <th>الإجمالي</th>
              <th>ملاحظات</th>
            </tr>
          </thead>
          <tbody>
    `;

    // إضافة صفوف الجدول (بحد أقصى 15 إرجاع للحفاظ على حجم التقرير)
    const maxReturns = Math.min(returns.length, 15);
    for (let i = 0; i < maxReturns; i++) {
      const r = returns[i];

      html += `
        <tr>
          <td>${formatDate(r.return_date || r.transaction_date)}</td>
          <td>${r.customer_name || 'عميل'}</td>
          <td>${r.item_name}</td>
          <td>${Math.abs(r.quantity)}</td>
          <td>${formatCurrency(r.price)}</td>
          <td>${formatCurrency(Math.abs(r.total_price))}</td>
          <td>${r.notes || ''}</td>
        </tr>
      `;
    }

    // إضافة صف إجمالي إذا كان هناك المزيد من الإرجاعات
    if (returns.length > maxReturns) {
      html += `
        <tr>
          <td colspan="7" class="text-center">
            تم عرض ${maxReturns} إرجاع من إجمالي ${returns.length} إرجاع
          </td>
        </tr>
      `;
    }

    html += `
          </tbody>
        </table>
      </div>
      <div class="page-break"></div>
    `;

    return html;
  };

  /**
   * توليد HTML لقسم تقرير الأصناف الأكثر مبيعًا
   * @param {Array} items - بيانات الأصناف الأكثر مبيعًا
   * @returns {string} - HTML لقسم تقرير الأصناف الأكثر مبيعًا
   */
  const generateTopSellingItemsReportHTML = (items) => {
    if (!items || items.length === 0) {
      return `
        <div class="section">
          <h3 class="section-title">تقرير الأصناف الأكثر مبيعًا</h3>
          <p class="text-center">لا توجد بيانات للعرض</p>
        </div>
        <div class="page-break"></div>
      `;
    }

    let html = `
      <div class="section">
        <h3 class="section-title">تقرير الأصناف الأكثر مبيعًا</h3>

        <table>
          <thead>
            <tr>
              <th>#</th>
              <th>الصنف</th>
              <th>الكمية المباعة</th>
              <th>قيمة المبيعات</th>
              <th>الربح</th>
              <th>نسبة الربح</th>
            </tr>
          </thead>
          <tbody>
    `;

    // إضافة صفوف الجدول
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const profitMargin = calculateProfitMargin(
        item.total_profit || item.profit || 0,
        item.total_sales || item.value || 0
      );

      html += `
        <tr>
          <td>${i + 1}</td>
          <td>${item.item_name}</td>
          <td>${item.total_quantity || item.quantity || 0}</td>
          <td>${formatCurrency(item.total_sales || item.value || 0)}</td>
          <td class="text-success">${formatCurrency(item.total_profit || item.profit || 0)}</td>
          <td>${profitMargin.toFixed(1)}%</td>
        </tr>
      `;
    }

    html += `
          </tbody>
        </table>
      </div>
      <div class="page-break"></div>
    `;

    return html;
  };

  /**
   * توليد HTML لقسم تقرير العملاء الأكثر شراءً
   * @param {Array} customers - بيانات العملاء الأكثر شراءً
   * @returns {string} - HTML لقسم تقرير العملاء الأكثر شراءً
   */
  const generateTopCustomersReportHTML = (customers) => {
    if (!customers || customers.length === 0) {
      return `
        <div class="section">
          <h3 class="section-title">تقرير العملاء الأكثر شراءً</h3>
          <p class="text-center">لا توجد بيانات للعرض</p>
        </div>
        <div class="page-break"></div>
      `;
    }

    let html = `
      <div class="section">
        <h3 class="section-title">تقرير العملاء الأكثر شراءً</h3>

        <table>
          <thead>
            <tr>
              <th>#</th>
              <th>العميل</th>
              <th>نوع العميل</th>
              <th>عدد المعاملات</th>
              <th>إجمالي المشتريات</th>
              <th>الربح</th>
            </tr>
          </thead>
          <tbody>
    `;

    // إضافة صفوف الجدول
    for (let i = 0; i < customers.length; i++) {
      const customer = customers[i];

      let customerType = '';
      switch (customer.customer_type) {
        case 'regular':
          customerType = 'دائم';
          break;
        case 'sub':
          customerType = 'فرعي';
          break;
        default:
          customerType = 'عادي';
      }

      html += `
        <tr>
          <td>${i + 1}</td>
          <td>${customer.customer_name || customer.name}</td>
          <td>${customerType}</td>
          <td>${customer.transactions_count || customer.sales_count || 0}</td>
          <td>${formatCurrency(customer.total_purchases || customer.total_sales || 0)}</td>
          <td class="text-success">${formatCurrency(customer.total_profit || 0)}</td>
        </tr>
      `;
    }

    html += `
          </tbody>
        </table>
      </div>
      <div class="page-break"></div>
    `;

    return html;
  };

  /**
   * توليد HTML لقسم تقرير الأصناف التي تحتاج إعادة طلب
   * @param {Array} items - بيانات الأصناف التي تحتاج إعادة طلب
   * @returns {string} - HTML لقسم تقرير الأصناف التي تحتاج إعادة طلب
   */
  const generateLowStockReportHTML = (items) => {
    if (!items || items.length === 0) {
      return `
        <div class="section">
          <h3 class="section-title">تقرير الأصناف التي تحتاج إعادة طلب</h3>
          <p class="text-center">لا توجد أصناف تحتاج إلى إعادة طلب</p>
        </div>
      `;
    }

    // تصفية الأصناف التي تحتاج إلى إعادة طلب
    const lowStockItems = items.filter(item =>
      item.current_quantity <= item.minimum_quantity
    );

    if (lowStockItems.length === 0) {
      return `
        <div class="section">
          <h3 class="section-title">تقرير الأصناف التي تحتاج إعادة طلب</h3>
          <p class="text-center">لا توجد أصناف تحتاج إلى إعادة طلب</p>
        </div>
      `;
    }

    let html = `
      <div class="section">
        <h3 class="section-title">تقرير الأصناف التي تحتاج إعادة طلب</h3>

        <table>
          <thead>
            <tr>
              <th>#</th>
              <th>الصنف</th>
              <th>وحدة القياس</th>
              <th>الكمية المتوفرة</th>
              <th>الحد الأدنى</th>
              <th>الكمية المطلوبة</th>
              <th>متوسط السعر</th>
              <th>التكلفة المتوقعة</th>
            </tr>
          </thead>
          <tbody>
    `;

    // إضافة صفوف الجدول
    for (let i = 0; i < lowStockItems.length; i++) {
      const item = lowStockItems[i];
      const requiredQuantity = Math.max(0, item.minimum_quantity - item.current_quantity);
      const estimatedCost = requiredQuantity * item.avg_price;

      html += `
        <tr>
          <td>${i + 1}</td>
          <td>${item.name}</td>
          <td>${item.unit || '-'}</td>
          <td class="${item.current_quantity === 0 ? 'text-danger' : ''}">${item.current_quantity}</td>
          <td>${item.minimum_quantity}</td>
          <td>${requiredQuantity}</td>
          <td>${formatCurrency(item.avg_price)}</td>
          <td>${formatCurrency(estimatedCost)}</td>
        </tr>
      `;
    }

    // حساب إجمالي التكلفة المتوقعة
    const totalEstimatedCost = lowStockItems.reduce((sum, item) => {
      const requiredQuantity = Math.max(0, item.minimum_quantity - item.current_quantity);
      return sum + (requiredQuantity * item.avg_price);
    }, 0);

    html += `
          <tr>
            <td colspan="7" class="text-right"><strong>إجمالي التكلفة المتوقعة:</strong></td>
            <td><strong>${formatCurrency(totalEstimatedCost)}</strong></td>
          </tr>
          </tbody>
        </table>
      </div>
    `;

    return html;
  };

  /**
   * تنسيق القيمة المالية للعرض في التقارير
   * @param {number} amount - القيمة المالية
   * @returns {string} - القيمة المالية المنسقة
   */
  const formatReportCurrency = (amount) => {
    if (amount === undefined || amount === null) return '-';
    return `${Math.round(amount).toLocaleString()} د ل`;
  };

  // الحصول على الإحصائيات
  const inventoryStats = calculateInventoryStats();
  const transactionStats = calculateTransactionStats();
  const topSellingItemsLocal = calculateTopSellingItems();
  const mostProfitableItems = calculateMostProfitableItems();

  // استخدام قيم الأرباح المحسوبة مسبقًا من حالة المكون
  const profitValues = {
    quarterly: profits.quarterly || 0,
    halfYearly: profits.halfYearly || 0,
    threeQuarters: profits.threeQuarters || 0,
    yearly: profits.yearly || 0
  };

  // حساب نسبة الربح الإجمالية
  const overallProfitMargin = calculateProfitMargin(
    transactionStats.sales.profit,
    transactionStats.sales.value
  );

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="reports-page">
      <div className="reports-header">
        <h1>التقارير</h1>
        <p>عرض وتصدير تقارير المخزون والمعاملات والأرباح بطريقة سهلة وفعالة</p>
      </div>

      {alert.show && (
        <div className={`alert alert-${alert.type}`}>
          {alert.message}
        </div>
      )}

      {/* أقسام التقارير المحسنة - التنظيم الجديد */}
      <div className="reports-sections">
        {/* قسم تقارير المخزون والأصناف */}
        <div className="report-section">
          <h2>تقارير المخزون والأصناف</h2>
          <div className="reports-buttons">
            <ReportButton
              icon={<FaBoxes />}
              title="تقرير المخزون والأصناف الشامل"
              description="عرض تقرير شامل للمخزون والأصناف"
              onClick={() => setActiveTab('inventoryDetailed')}
              active={activeTab === 'inventoryDetailed'}
            />
          </div>
        </div>

        {/* قسم تقارير المبيعات والمالية */}
        <div className="report-section">
          <h2>تقارير المبيعات والمالية</h2>
          <div className="reports-buttons">
            <ReportButton
              icon={<FaChartPie />}
              title="تقرير المبيعات والمالية"
              description="عرض تقرير المبيعات والمالية الشامل"
              onClick={() => setActiveTab('financialSales')}
              active={activeTab === 'financialSales'}
            />
            <ReportButton
              icon={<FaUndo />}
              title="تقرير الإرجاعات"
              description="عرض معاملات الإرجاع فقط"
              onClick={() => setActiveTab('returns')}
              active={activeTab === 'returns'}
            />
          </div>
        </div>

        {/* قسم تقارير العملاء */}
        <div className="report-section">
          <h2>تقارير العملاء</h2>
          <div className="reports-buttons">
            <ReportButton
              icon={<FaUser />}
              title="تقرير العملاء الموحد"
              description="عرض تقرير العملاء الشامل"
              onClick={() => setActiveTab('unifiedCustomerReports')}
              active={activeTab === 'unifiedCustomerReports'}
            />
          </div>
        </div>

        {/* قسم التقرير الشامل */}
        <div className="report-section full-width">
          <h2>التقرير الشامل</h2>
          <div className="reports-buttons">
            <ReportButton
              icon={<FaFileInvoice />}
              title="التقرير المفصل الشامل"
              description="طباعة تقرير مفصل يشمل كافة البنود"
              onClick={() => setActiveTab('comprehensiveReport')}
              active={activeTab === 'comprehensiveReport'}
            />
          </div>
        </div>
      </div>

      {/* أدوات التصفية والتصدير */}
      <Card>
        <div className="filters-container">
          <div className="filter-group">
            <span className="filter-label">
              <FaCalendarAlt />
              الفترة الزمنية:
            </span>
            <select
              className="filter-select"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
            >
              <option value="all">جميع الفترات</option>
              <option value="quarter">الربع الحالي</option>
              <option value="halfYear">النصف سنوي</option>
              <option value="threeQuarters">ثلاثة أرباع السنة</option>
              <option value="year">السنة الحالية</option>
            </select>
          </div>


        </div>

        <div className="export-tools">
          <ReportExporter
            reportType={activeTab}
            dateRange={dateRange}
            data={{
              inventory: inventory,
              transactions: filteredTransactions,
              profitValues: profitValues,
              customers: activeTab === 'topCustomers' ? topCustomers : null,
              stats: activeTab === 'topCustomers' ? topCustomersStats : null,
              topSellingItems: topSellingItems,
              lowStockItems: lowStockItems,
              cashboxReport: cashboxReport
            }}
          />
          <button
            className="btn btn-secondary ml-2"
            onClick={printReport}
            title="طباعة احتياطية للتقرير"
          >
            <FaPrint className="ml-1" />
            طباعة احتياطية
          </button>
        </div>
      </Card>

      <Card ref={reportRef}>
        {activeTab === 'returns' && (
          <div>
            <h2>تقرير الإرجاعات</h2>
            {/* إحصائيات الإرجاعات */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaUndo />
                </div>
                <div className="stat-card-value">{filteredTransactions.filter(t => t.transaction_type === 'return').length}</div>
                <div className="stat-card-title">عدد الإرجاعات</div>
              </div>
              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaMoneyBillWave />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={filteredTransactions.filter(t => t.transaction_type === 'return').reduce((sum, t) => sum + Math.abs(t.total_price || 0), 0)} />
                </div>
                <div className="stat-card-title">إجمالي قيمة الإرجاعات</div>
              </div>
              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaChartBar />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={filteredTransactions.filter(t => t.transaction_type === 'return').reduce((sum, t) => sum + Math.max(0, t.profit || 0), 0)} isProfit={true} />
                </div>
                <div className="stat-card-title">إجمالي الربح المفقود</div>
              </div>
            </div>
            {/* جدول الإرجاعات */}
            <DataTable
              columns={[
                {
                  header: 'رقم المعاملة',
                  accessor: 'transaction_id',
                  cell: (row) => (
                    <span className="transaction-id">
                      {row.transaction_id || (row.id ? `TRX-${row.id.toString().padStart(6, '0')}` : 'TRX-000000')}
                    </span>
                  )
                },
                {
                  header: 'التاريخ',
                  accessor: 'transaction_date',
                  cell: (row) => formatDate(row.transaction_date)
                },
                {
                  header: 'العميل',
                  accessor: 'customer',
                  cell: (row) => getCustomerNameById(row.customer, customers)
                },
                {
                  header: 'الصنف',
                  accessor: 'item_name',
                  cell: (row) => row.item_name
                },
                {
                  header: 'الكمية',
                  accessor: 'quantity',
                  cell: (row) => -Math.abs(row.quantity)
                },
                {
                  header: 'السعر',
                  accessor: 'price',
                  cell: (row) => row.price ? <FormattedCurrency amount={row.price} /> : '-'
                },
                {
                  header: 'الإجمالي',
                  accessor: 'total_price',
                  cell: (row) => row.total_price ? <FormattedCurrency amount={-Math.abs(row.total_price)} /> : '-'
                },
                {
                  header: 'الربح',
                  accessor: 'profit',
                  cell: (row) => row.profit ? <FormattedCurrency amount={-Math.abs(row.profit)} isProfit={true} /> : '-'
                },
                {
                  header: 'ملاحظات',
                  accessor: 'notes',
                  cell: (row) => row.notes || ''
                }
              ]}
              data={filteredTransactions.filter(t => t.transaction_type === 'return')}
              pagination={true}
              pageSize={10}
              searchable={true}
              searchPlaceholder="بحث عن إرجاع..."
              emptyMessage="لا توجد بيانات إرجاع للعرض"
            />


          </div>
        )}
        {activeTab === 'inventory' && (
          <div>
            <h2>تقرير المخزون</h2>

            {/* إحصائيات المخزون */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaBoxes />
                </div>
                <div className="stat-card-value">{inventoryStats.total}</div>
                <div className="stat-card-title">إجمالي الأصناف</div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaExclamationTriangle />
                </div>
                <div className="stat-card-value">{inventoryStats.low}</div>
                <div className="stat-card-title">أصناف تحت الحد الأدنى</div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaExclamationTriangle />
                </div>
                <div className="stat-card-value">{inventoryStats.out}</div>
                <div className="stat-card-title">أصناف نفذت الكمية</div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaMoneyBillWave />
                </div>
                <div className="stat-card-value">
                  <FormattedCurrency amount={inventoryStats.value} />
                </div>
                <div className="stat-card-title">قيمة المخزون</div>
              </div>
            </div>

            {/* جدول المخزون */}
            <DataTable
              columns={[
                {
                  header: '#',
                  accessor: 'index',
                  cell: (_, index) => index + 1,
                  style: { width: '50px' }
                },
                {
                  header: 'الاسم',
                  accessor: 'name',
                  cell: (row) => row.name
                },
                {
                  header: 'وحدة القياس',
                  accessor: 'unit',
                  cell: (row) => row.unit || '-'
                },
                {
                  header: 'الكمية المتوفرة',
                  accessor: 'current_quantity',
                  cell: (row) => {
                    if (row.current_quantity === 0) {
                      return (
                        <span className="badge badge-danger">
                          نفذت الكمية
                        </span>
                      );
                    } else if (row.current_quantity <= row.minimum_quantity) {
                      return (
                        <span className="badge badge-warning">
                          <FaExclamationTriangle />
                          {row.current_quantity}
                        </span>
                      );
                    } else {
                      return row.current_quantity;
                    }
                  }
                },
                {
                  header: 'الحد الأدنى',
                  accessor: 'minimum_quantity',
                  cell: (row) => row.minimum_quantity
                },
                {
                  header: 'متوسط السعر',
                  accessor: 'avg_price',
                  cell: (row) => row.avg_price ? <FormattedCurrency amount={row.avg_price} /> : '-'
                },
                {
                  header: 'سعر البيع',
                  accessor: 'selling_price',
                  cell: (row) => row.selling_price ? <FormattedCurrency amount={row.selling_price} /> : '-'
                },
                {
                  header: 'القيمة',
                  accessor: 'value',
                  cell: (row) => <FormattedCurrency amount={row.current_quantity * row.avg_price} />
                }
              ]}
              data={inventory}
              pagination={true}
              pageSize={10}
              searchable={true}
              searchPlaceholder="بحث عن صنف..."
              emptyMessage="لا توجد بيانات للعرض"
            />
          </div>
        )}

        {activeTab === 'financialSales' && (
          <div>
            <h2>تقرير المبيعات والمالية</h2>

            <FinancialSalesReport
              transactions={filteredTransactions}
              profitValues={profitValues}
              cashboxReport={cashboxReport}
              dateRange={dateRange}
              onDateRangeChange={(value) => setDateRange(value)}
              onCashboxReportUpdate={handleCashboxReportUpdate}
            />
          </div>
        )}

        {activeTab === 'transactions' && (
          <div>
            <h2>تقرير المعاملات</h2>

            {/* إحصائيات المعاملات */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaArrowCircleDown />
                </div>
                <div className="stat-card-value">{transactionStats.purchases.count}</div>
                <div className="stat-card-title">عمليات الشراء</div>
                <div className="stat-card-subtitle">
                  <FormattedCurrency amount={transactionStats.purchases.value} />
                </div>
              </div>

              <div className="stat-card">
                <div className="stat-card-icon">
                  <FaArrowCircleUp />
                </div>
                <div className="stat-card-value">{transactionStats.sales.count}</div>
                <div className="stat-card-title">عمليات البيع</div>
                <div className="stat-card-subtitle">
                  <FormattedCurrency amount={transactionStats.sales.value} />
                </div>
              </div>
            </div>

            {/* الأصناف الأكثر مبيعًا */}
            {topSellingItemsLocal.length > 0 && (
              <div className="mb-4">
                <h3>الأصناف الأكثر مبيعًا</h3>
                <DataTable
                  columns={[
                    {
                      header: '#',
                      accessor: 'index',
                      cell: (_, index) => index + 1,
                      style: { width: '50px' }
                    },
                    {
                      header: 'الصنف',
                      accessor: 'item_name',
                      cell: (row) => row.item_name || 'غير معروف'
                    },
                    {
                      header: 'الكمية المباعة',
                      accessor: 'quantity',
                      cell: (row) => row.quantity || 0
                    },
                    {
                      header: 'قيمة المبيعات',
                      accessor: 'value',
                      cell: (row) => <FormattedCurrency amount={row.value || 0} />
                    },
                    {
                      header: 'الربح',
                      accessor: 'profit',
                      cell: (row) => (
                        <span className="text-success">
                          <FormattedCurrency amount={row.profit || 0} isProfit={true} />
                        </span>
                      )
                    }
                  ]}
                  data={topSellingItemsLocal}
                  pagination={false}
                  searchable={false}
                  emptyMessage="لا توجد بيانات للعرض"
                />
              </div>
            )}

            {/* جدول المعاملات */}
            <DataTable
              columns={[
                {
                  header: 'رقم المعاملة',
                  accessor: 'transaction_id',
                  cell: (row) => (
                    <span className="transaction-id">
                      {row.transaction_id || (row.id ? `TRX-${row.id.toString().padStart(6, '0')}` : 'TRX-000000')}
                    </span>
                  )
                },
                {
                  header: 'التاريخ',
                  accessor: 'transaction_date',
                  cell: (row) => formatDate(row.transaction_date)
                },
                {
                  header: 'نوع المعاملة',
                  accessor: 'transaction_type',
                  cell: (row) => {
                    switch (row.transaction_type) {
                      case 'purchase':
                        return 'شراء';
                      case 'sale':
                        return 'بيع للعميل';
                      default:
                        return row.transaction_type;
                    }
                  }
                },
                {
                  header: 'الصنف',
                  accessor: 'item_name',
                  cell: (row) => row.item_name
                },
                {
                  header: 'الكمية',
                  accessor: 'quantity',
                  cell: (row) => row.quantity
                },
                {
                  header: 'السعر',
                  accessor: 'price',
                  cell: (row) => row.price ? <FormattedCurrency amount={row.price} /> : '-'
                },
                {
                  header: 'الإجمالي',
                  accessor: 'total_price',
                  cell: (row) => row.total_price ? <FormattedCurrency amount={row.total_price} /> : '-'
                },
                {
                  header: 'التفاصيل',
                  accessor: 'details',
                  cell: (row) => {
                    switch (row.transaction_type) {
                      case 'purchase':
                        return '';
                      case 'sale':
                        // استخدام الوظيفة المساعدة للحصول على اسم العميل
                        const customerName = getCustomerNameById(row.customer, customers);
                        return `إلى ${customerName}`;
                      default:
                        return '';
                    }
                  }
                }
              ]}
              data={filteredTransactions}
              pagination={true}
              pageSize={10}
              searchable={true}
              searchPlaceholder="بحث عن معاملة..."
              emptyMessage="لا توجد بيانات للعرض"
            />
          </div>
        )}

        {activeTab === 'profits' && (
          <div>
            <div className="report-header-section">
              <h2>تقرير الأرباح</h2>
              <p className="report-subtitle">
                <FaCalendarAlt style={{ marginLeft: '5px' }} />
                {dateRange === 'quarter' ? 'الربع الحالي' :
                 dateRange === 'halfYear' ? 'النصف سنوي' :
                 dateRange === 'threeQuarters' ? 'ثلاثة أرباع السنة' :
                 dateRange === 'year' ? 'السنة الحالية' : 'جميع الفترات'}
                 - تاريخ التقرير: {formatDate(new Date().toISOString())}
              </p>
            </div>

            {/* إحصائيات الأرباح - تصميم جديد */}
            <div className="profits-container">
              <div className="profits-header">
                <div className="profits-title">ملخص الأرباح</div>
              </div>

              <div className="profits-grid">
                <div className="profit-item profit-quarterly">
                  <div className="profit-item-title">الربح الربع سنوي</div>
                  <div className="profit-item-value">
                    <FormattedCurrency
                      amount={Math.abs(profitValues.quarterly)}
                      isProfit={true}
                      useThousandSeparator={true}
                      decimalPlaces={0}
                    />
                  </div>
                </div>

                <div className="profit-item profit-half-yearly">
                  <div className="profit-item-title">الربح النصف سنوي</div>
                  <div className="profit-item-value">
                    <FormattedCurrency
                      amount={Math.abs(profitValues.halfYearly)}
                      isProfit={true}
                      useThousandSeparator={true}
                      decimalPlaces={0}
                    />
                  </div>
                </div>

                <div className="profit-item profit-three-quarters">
                  <div className="profit-item-title">الربح ثلاثة أرباع سنوي</div>
                  <div className="profit-item-value">
                    <FormattedCurrency
                      amount={Math.abs(profitValues.threeQuarters)}
                      isProfit={true}
                      useThousandSeparator={true}
                      decimalPlaces={0}
                    />
                  </div>
                </div>

                <div className="profit-item profit-yearly">
                  <div className="profit-item-title">الربح السنوي</div>
                  <div className="profit-item-value">
                    <FormattedCurrency
                      amount={Math.abs(profitValues.yearly)}
                      isProfit={true}
                      useThousandSeparator={true}
                      decimalPlaces={0}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* توزيع الأرباح */}
            <div className="profits-container">
              <div className="profits-header">
                <div className="profits-title">
                  <FaPercentage style={{ marginLeft: '8px' }} />
                  توزيع الأرباح السنوية
                </div>
              </div>

              <div className="profit-distribution">
                <div className="profit-distribution-item" style={{ width: '30%' }}>
                  <div className="profit-distribution-label">30% (الاستثمار)</div>
                  <div className="profit-distribution-value">
                    <FormattedCurrency
                      amount={Math.abs(profitValues.yearly) * 0.3}
                      isProfit={true}
                      useThousandSeparator={true}
                      decimalPlaces={0}
                    />
                  </div>
                </div>
                <div className="profit-distribution-item" style={{ width: '70%' }}>
                  <div className="profit-distribution-label">70% (الأرباح)</div>
                  <div className="profit-distribution-value">
                    <FormattedCurrency
                      amount={Math.abs(profitValues.yearly) * 0.7}
                      isProfit={true}
                      useThousandSeparator={true}
                      decimalPlaces={0}
                    />
                  </div>
                </div>
              </div>

              <div className="text-center" style={{ fontSize: '0.9rem', color: 'var(--text-light)', marginTop: '10px' }}>
                <FaInfoCircle style={{ marginLeft: '5px' }} />
                تقسيم الأرباح السنوية (30% للاستثمار و70% للأرباح)
              </div>
            </div>

            {/* ملخص المبيعات والمشتريات */}
            <div className="profits-container">
              <div className="profits-header">
                <div className="profits-title">
                  <FaChartBar style={{ marginLeft: '8px' }} />
                  ملخص المبيعات والمشتريات
                </div>
              </div>

              <DataTable
                columns={[
                  {
                    header: 'البيان',
                    accessor: 'statement',
                    cell: (row) => row.statement
                  },
                  {
                    header: 'العدد',
                    accessor: 'count',
                    cell: (row) => row.count
                  },
                  {
                    header: 'القيمة',
                    accessor: 'value',
                    cell: (row) => (
                      <span className={row.isProfit ? 'text-success font-weight-bold' : ''}>
                        <FormattedCurrency
                          amount={row.value}
                          isProfit={row.isProfit}
                          useThousandSeparator={true}
                        />
                      </span>
                    )
                  }
                ]}
                data={[
                  { statement: 'إجمالي المبيعات', count: transactionStats.sales.count, value: transactionStats.sales.value, isProfit: false },
                  { statement: 'إجمالي المشتريات', count: transactionStats.purchases.count, value: transactionStats.purchases.value, isProfit: false },
                  { statement: 'صافي الربح', count: '-', value: Math.abs(transactionStats.sales.profit), isProfit: true }
                ]}
                pagination={false}
                searchable={false}
                emptyMessage="لا توجد بيانات للعرض"
              />
            </div>

            {/* الأصناف الأكثر ربحًا */}
            {mostProfitableItems.length > 0 && (
              <div className="profits-container">
                <div className="profits-header">
                  <div className="profits-title">
                    <FaBoxes style={{ marginLeft: '8px' }} />
                    الأصناف الأكثر ربحًا
                  </div>
                </div>

                <DataTable
                  columns={[
                    {
                      header: '#',
                      accessor: 'index',
                      cell: (_, index) => index + 1,
                      style: { width: '50px' }
                    },
                    {
                      header: 'الصنف',
                      accessor: 'item_name',
                      cell: (row) => row.item_name || 'غير معروف'
                    },
                    {
                      header: 'الكمية المباعة',
                      accessor: 'quantity',
                      cell: (row) => row.quantity || 0
                    },
                    {
                      header: 'قيمة المبيعات',
                      accessor: 'value',
                      cell: (row) => <FormattedCurrency amount={row.value || 0} />
                    },
                    {
                      header: 'الربح',
                      accessor: 'profit',
                      cell: (row) => (
                        <span className="text-success font-weight-bold">
                          <FormattedCurrency
                            amount={row.profit || 0}
                            isProfit={true}
                            useThousandSeparator={true}
                          />
                        </span>
                      )
                    },
                    {
                      header: 'نسبة الربح',
                      accessor: 'profit_percentage',
                      cell: (row) => (
                        <span className="font-weight-bold">
                          {row.value && row.profit ? ((row.profit / row.value) * 100).toFixed(2) : '0.00'}%
                        </span>
                      )
                    }
                  ]}
                  data={mostProfitableItems}
                  pagination={false}
                  searchable={false}
                  emptyMessage="لا توجد بيانات للعرض"
                />
              </div>
            )}
          </div>
        )}
        {activeTab === 'unifiedCustomerReports' && (
          <div>
            <UnifiedCustomerReports />
          </div>
        )}

        {false && activeTab === 'subCustomers' && (
          <div>
            <h2>تقرير مبيعات العملاء الفرعيين</h2>

            {/* اختيار العميل الدائم */}
            <div className="mb-4">
              <label className="customer-select-label">
                <FaUser /> اختر العميل الدائم:
              </label>
              <select
                className="filter-select customer-select"
                value={selectedRegularCustomer}
                onChange={(e) => setSelectedRegularCustomer(e.target.value)}
              >
                <option value="">-- اختر العميل الدائم --</option>
                {regularCustomers.map(customer => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
            </div>

            {selectedRegularCustomer && subCustomerSales && subCustomerSales.regularCustomer ? (
              <div>
                {/* ملخص المبيعات للعملاء الفرعيين */}
                <div className="stats-grid">
                  <div className="stat-card">
                    <div className="stat-card-icon">
                      <FaUserFriends />
                    </div>
                    <div className="stat-card-value">{subCustomerSales.subCustomers.length}</div>
                    <div className="stat-card-title">عدد العملاء الفرعيين</div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-card-icon">
                      <FaShoppingCart />
                    </div>
                    <div className="stat-card-value">
                      {subCustomerSales.subCustomers.reduce((sum, customer) => sum + customer.salesCount, 0)}
                    </div>
                    <div className="stat-card-title">إجمالي عمليات البيع</div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-card-icon">
                      <FaMoneyBillWave />
                    </div>
                    <div className="stat-card-value">
                      <FormattedCurrency amount={subCustomerSales.totalSales} />
                    </div>
                    <div className="stat-card-title">إجمالي المبيعات</div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-card-icon">
                      <FaChartBar />
                    </div>
                    <div className="stat-card-value">
                      <FormattedCurrency amount={subCustomerSales.totalProfit} isProfit={true} />
                    </div>
                    <div className="stat-card-title">إجمالي الأرباح</div>
                  </div>
                </div>

                {/* جدول العملاء الفرعيين */}
                <div className="mb-4">
                  <h3>ملخص مبيعات العملاء الفرعيين</h3>
                  <DataTable
                    columns={[
                      {
                        header: '#',
                        accessor: 'index',
                        cell: (_, index) => index + 1,
                        style: { width: '50px' }
                      },
                      {
                        header: 'اسم العميل الفرعي',
                        accessor: 'subCustomerName',
                        cell: (row) => row.subCustomerName
                      },
                      {
                        header: 'عدد عمليات البيع',
                        accessor: 'salesCount',
                        cell: (row) => row.salesCount
                      },
                      {
                        header: 'إجمالي المبيعات',
                        accessor: 'totalSales',
                        cell: (row) => <FormattedCurrency amount={row.totalSales} />
                      },
                      {
                        header: 'إجمالي الأرباح',
                        accessor: 'totalProfit',
                        cell: (row) => (
                          <span className="text-success">
                            <FormattedCurrency amount={row.totalProfit} isProfit={true} />
                          </span>
                        )
                      },
                      {
                        header: 'نسبة الربح',
                        accessor: 'profitPercentage',
                        cell: (row) => (
                          <span>
                            {row.totalSales > 0
                              ? ((row.totalProfit / row.totalSales) * 100).toFixed(2)
                              : '0.00'}%
                          </span>
                        )
                      }
                    ]}
                    data={subCustomerSales.subCustomers}
                    pagination={false}
                    searchable={false}
                    emptyMessage="لا توجد عملاء فرعيين لهذا العميل الدائم"
                    footer={
                      <tr className="font-bold">
                        <td colSpan="3">الإجمالي</td>
                        <td><FormattedCurrency amount={subCustomerSales.totalSales} /></td>
                        <td className="text-success"><FormattedCurrency amount={subCustomerSales.totalProfit} isProfit={true} /></td>
                        <td>
                          {subCustomerSales.totalSales > 0
                            ? ((subCustomerSales.totalProfit / subCustomerSales.totalSales) * 100).toFixed(2)
                            : '0.00'}%
                        </td>
                      </tr>
                    }
                  />
                </div>

                {/* تفاصيل المبيعات لكل عميل فرعي */}
                {subCustomerSales.subCustomers.filter(c => c.salesCount > 0).map(customer => {
                  // استخدام المبيعات المجمعة من الخادم إذا كانت متوفرة، وإلا نقوم بتجميعها محليًا
                  let groupedInvoices = customer.groupedSales || [];

                  // إذا لم تكن المبيعات المجمعة متوفرة، نقوم بتجميعها محليًا
                  if (!groupedInvoices || groupedInvoices.length === 0) {
                    console.log(`تجميع المبيعات محليًا للعميل ${customer.subCustomerName}...`);

                    // تجميع المبيعات حسب رقم الفاتورة
                    const invoiceGroups = {};

                    // تجميع المبيعات حسب رقم الفاتورة
                    customer.salesHistory.forEach(sale => {
                      const invoiceNumber = sale.invoice_number || `INV-${sale.id.toString().padStart(5, '0')}`;

                      if (!invoiceGroups[invoiceNumber]) {
                        invoiceGroups[invoiceNumber] = {
                          invoiceNumber: invoiceNumber,
                          items: [],
                          totalAmount: 0,
                          totalProfit: 0,
                          transaction_date: sale.transaction_date,
                          notes: sale.notes || '-'
                        };
                      }

                      invoiceGroups[invoiceNumber].items.push(sale);
                      invoiceGroups[invoiceNumber].totalAmount += Number(sale.total_price) || 0;
                      invoiceGroups[invoiceNumber].totalProfit += Number(sale.profit) || 0;
                    });

                    // تحويل الكائن إلى مصفوفة
                    groupedInvoices = Object.values(invoiceGroups);
                    console.log(`تم تجميع ${customer.salesHistory.length} معاملة في ${groupedInvoices.length} فاتورة محليًا`);
                  } else {
                    console.log(`استخدام ${groupedInvoices.length} فاتورة مجمعة من الخادم للعميل ${customer.subCustomerName}`);

                    // التأكد من أن كل فاتورة تحتوي على الحقول المطلوبة
                    groupedInvoices = groupedInvoices.map(invoice => {
                      // حساب إجمالي المبلغ والربح إذا لم يكونا موجودين
                      let totalAmount = invoice.totalAmount;
                      let totalProfit = invoice.totalProfit;

                      if (totalAmount === undefined && invoice.items && invoice.items.length > 0) {
                        totalAmount = invoice.items.reduce((sum, item) => sum + (Number(item.total_price) || 0), 0);
                      }

                      if (totalProfit === undefined && invoice.items && invoice.items.length > 0) {
                        totalProfit = invoice.items.reduce((sum, item) => sum + (Number(item.profit) || 0), 0);
                      }

                      return {
                        ...invoice,
                        invoiceNumber: invoice.invoiceNumber || invoice.invoice_number,
                        totalAmount: totalAmount || 0,
                        totalProfit: totalProfit || 0
                      };
                    });
                  }

                  return (
                    <div key={customer.subCustomerId} className="mb-4">
                      <h3>
                        <FaUser /> تفاصيل مبيعات العميل: {customer.subCustomerName}
                      </h3>

                      {/* عرض الفواتير المجمعة */}
                      <div className="mb-4">
                        <h4>ملخص الفواتير</h4>
                        <DataTable
                          columns={[
                            {
                              header: '#',
                              accessor: 'index',
                              cell: (_, index) => index + 1,
                              style: { width: '50px' }
                            },
                            {
                              header: 'التاريخ',
                              accessor: 'transaction_date',
                              cell: (row) => formatDate(row.transaction_date)
                            },
                            {
                              header: 'رقم الفاتورة',
                              accessor: 'invoiceNumber',
                              cell: (row) => row.invoiceNumber
                            },
                            {
                              header: 'عدد الأصناف',
                              accessor: 'itemCount',
                              cell: (row) => row.items.length
                            },
                            {
                              header: 'إجمالي المبلغ',
                              accessor: 'totalAmount',
                              cell: (row) => <FormattedCurrency amount={row.totalAmount} />
                            },
                            {
                              header: 'إجمالي الربح',
                              accessor: 'totalProfit',
                              cell: (row) => (
                                <span className="text-success">
                                  <FormattedCurrency amount={row.totalProfit} />
                                </span>
                              )
                            }
                          ]}
                          data={groupedInvoices}
                          pagination={true}
                          pageSize={5}
                          searchable={false}
                          emptyMessage="لا توجد فواتير لهذا العميل"
                        />
                      </div>

                      {/* عرض تفاصيل الأصناف لكل فاتورة */}
                      {groupedInvoices.map((invoice, invoiceIndex) => {
                        // التأكد من وجود الأصناف
                        const items = invoice.items && Array.isArray(invoice.items) ? invoice.items : [];

                        return (
                          <div key={invoice.invoiceNumber || `invoice-${invoiceIndex}`} className="card mb-4">
                            <div className="card-header">
                              <h5>
                                <FaFileInvoice className="ml-2" />
                                فاتورة رقم: {invoice.invoiceNumber || invoice.invoice_number || `فاتورة-${invoiceIndex + 1}`} - التاريخ: {formatDate(invoice.transaction_date)}
                              </h5>
                            </div>
                            <div className="card-body">
                              <DataTable
                                columns={[
                                  {
                                    header: '#',
                                    accessor: 'index',
                                    cell: (_, index) => index + 1,
                                    style: { width: '50px' }
                                  },
                                  {
                                    header: 'الصنف',
                                    accessor: 'item_name',
                                    cell: (row) => row.item_name || 'صنف غير معروف'
                                  },
                                  {
                                    header: 'الكمية',
                                    accessor: 'quantity',
                                    cell: (row) => Number(row.quantity) || 0
                                  },
                                  {
                                    header: 'السعر',
                                    accessor: 'price',
                                    cell: (row) => <FormattedCurrency amount={Number(row.price) || 0} />
                                  },
                                  {
                                    header: 'الإجمالي',
                                    accessor: 'total_price',
                                    cell: (row) => <FormattedCurrency amount={Number(row.total_price) || 0} />
                                  },
                                  {
                                    header: 'الربح',
                                    accessor: 'profit',
                                    cell: (row) => (
                                      <span className="text-success">
                                        <FormattedCurrency amount={Number(row.profit) || 0} />
                                      </span>
                                    )
                                  }
                                ]}
                                data={items}
                                pagination={false}
                                searchable={false}
                                emptyMessage="لا توجد أصناف في هذه الفاتورة"
                                footer={
                                  <tr className="font-bold">
                                    <td colSpan="4">الإجمالي</td>
                                    <td><FormattedCurrency amount={Number(invoice.totalAmount) || 0} /></td>
                                    <td className="text-success"><FormattedCurrency amount={Number(invoice.totalProfit) || 0} /></td>
                                  </tr>
                                }
                              />
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="alert alert-info">
                <FaInfoCircle /> يرجى اختيار عميل دائم لعرض تقرير مبيعات العملاء الفرعيين
              </div>
            )}
          </div>
        )}

        {/* تقرير الأصناف الأكثر مبيعًا */}
        {activeTab === 'topSellingItems' && (
          <div>
            <h2>تقرير الأصناف الأكثر مبيعًا</h2>

            <div className="report-header-section">
              <p className="report-subtitle">
                <FaCalendarAlt style={{ marginLeft: '5px' }} />
                {dateRange === 'quarter' ? 'الربع الحالي' :
                 dateRange === 'halfYear' ? 'النصف سنوي' :
                 dateRange === 'threeQuarters' ? 'ثلاثة أرباع السنة' :
                 dateRange === 'year' ? 'السنة الحالية' : 'جميع الفترات'}
                 - تاريخ التقرير: {formatDate(new Date().toISOString())}
              </p>
            </div>

            {isLoadingSpecialReport ? (
              <div className="loading-indicator">جاري تحميل البيانات...</div>
            ) : (
              <>
                {topSellingItems.length > 0 ? (
                  <>
                    {/* إحصائيات الأصناف الأكثر مبيعًا */}
                    <div className="stats-grid">
                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaBoxes />
                        </div>
                        <div className="stat-card-value">{topSellingItems.length}</div>
                        <div className="stat-card-title">عدد الأصناف</div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaShoppingCart />
                        </div>
                        <div className="stat-card-value">
                          {topSellingItems.reduce((sum, item) => sum + (item.total_quantity || 0), 0)}
                        </div>
                        <div className="stat-card-title">إجمالي الكميات المباعة</div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaMoneyBillWave />
                        </div>
                        <div className="stat-card-value">
                          <FormattedCurrency amount={topSellingItems.reduce((sum, item) => sum + (item.total_sales || 0), 0)} />
                        </div>
                        <div className="stat-card-title">إجمالي المبيعات</div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaPercentage />
                        </div>
                        <div className="stat-card-value">
                          <FormattedCurrency amount={topSellingItems.reduce((sum, item) => sum + (item.total_profit || 0), 0)} isProfit={true} />
                        </div>
                        <div className="stat-card-title">إجمالي الأرباح</div>
                      </div>
                    </div>

                    {/* جدول الأصناف الأكثر مبيعًا */}
                    <DataTable
                      columns={[
                        {
                          header: '#',
                          accessor: 'index',
                          cell: (_, index) => index + 1,
                          style: { width: '50px' }
                        },
                        {
                          header: 'الصنف',
                          accessor: 'item_name',
                          cell: (row) => row.item_name
                        },
                        {
                          header: 'وحدة القياس',
                          accessor: 'item_unit',
                          cell: (row) => row.item_unit || '-'
                        },
                        {
                          header: 'الكمية المباعة',
                          accessor: 'total_quantity',
                          cell: (row) => row.total_quantity || 0
                        },
                        {
                          header: 'إجمالي المبيعات',
                          accessor: 'total_sales',
                          cell: (row) => <FormattedCurrency amount={row.total_sales || 0} />
                        },
                        {
                          header: 'إجمالي الأرباح',
                          accessor: 'total_profit',
                          cell: (row) => <FormattedCurrency amount={row.total_profit || 0} isProfit={true} />
                        },
                        {
                          header: 'نسبة الربح',
                          accessor: 'profit_margin',
                          cell: (row) => `${row.profit_margin ? row.profit_margin.toFixed(2) : 0}%`
                        },
                        {
                          header: 'الكمية الحالية',
                          accessor: 'current_quantity',
                          cell: (row) => {
                            if (row.current_quantity === 0) {
                              return (
                                <span className="badge badge-danger">
                                  نفذت الكمية
                                </span>
                              );
                            } else {
                              return row.current_quantity;
                            }
                          }
                        }
                      ]}
                      data={topSellingItems}
                      pagination={true}
                      pageSize={10}
                      searchable={true}
                      searchPlaceholder="بحث عن صنف..."
                      emptyMessage="لا توجد بيانات للعرض"
                    />
                  </>
                ) : (
                  <div className="empty-state">
                    <FaShoppingCart size={48} />
                    <p>لا توجد بيانات مبيعات للعرض</p>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* تقرير العملاء الأكثر شراءً */}
        {false && activeTab === 'topCustomers' && (
          <div>
            <h2>تقرير العملاء الأكثر شراءً</h2>

            <div className="report-header-section">
              <p className="report-subtitle">
                <FaCalendarAlt style={{ marginLeft: '5px' }} />
                {dateRange === 'quarter' ? 'الربع الحالي' :
                 dateRange === 'halfYear' ? 'النصف سنوي' :
                 dateRange === 'threeQuarters' ? 'ثلاثة أرباع السنة' :
                 dateRange === 'year' ? 'السنة الحالية' : 'جميع الفترات'}
                 - تاريخ التقرير: {formatDate(new Date().toISOString())}
              </p>
            </div>

            {isLoadingSpecialReport ? (
              <div className="loading-indicator">جاري تحميل البيانات...</div>
            ) : (
              <>
                {topCustomers.length > 0 ? (
                  <>
                    {/* إحصائيات العملاء الأكثر شراءً */}
                    <div className="stats-grid">
                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaUser />
                        </div>
                        <div className="stat-card-value">{topCustomers.length}</div>
                        <div className="stat-card-title">عدد العملاء</div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaFileInvoice />
                        </div>
                        <div className="stat-card-value">
                          {topCustomers.reduce((sum, customer) => sum + (customer.invoice_count || 0), 0)}
                        </div>
                        <div className="stat-card-title">عدد الفواتير</div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaMoneyBillWave />
                        </div>
                        <div className="stat-card-value">
                          <FormattedCurrency amount={topCustomers.reduce((sum, customer) => sum + (customer.total_sales || 0), 0)} />
                        </div>
                        <div className="stat-card-title">إجمالي المبيعات</div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaPercentage />
                        </div>
                        <div className="stat-card-value">
                          <FormattedCurrency amount={topCustomers.reduce((sum, customer) => sum + (customer.total_profit || 0), 0)} isProfit={true} />
                        </div>
                        <div className="stat-card-title">إجمالي الأرباح</div>
                      </div>
                    </div>

                    {/* جدول العملاء الأكثر شراءً */}
                    <DataTable
                      columns={[
                        {
                          header: '#',
                          accessor: 'index',
                          cell: (_, index) => index + 1,
                          style: { width: '50px' }
                        },
                        {
                          header: 'اسم العميل',
                          accessor: 'customer_name',
                          cell: (row) => row.customer_name
                        },
                        {
                          header: 'نوع العميل',
                          accessor: 'customer_type',
                          cell: (row) => row.customer_type || 'عادي'
                        },
                        {
                          header: 'عدد المعاملات',
                          accessor: 'transaction_count',
                          cell: (row) => row.transaction_count || 0
                        },
                        {
                          header: 'عدد الفواتير',
                          accessor: 'invoice_count',
                          cell: (row) => row.invoice_count || 0
                        },
                        {
                          header: 'إجمالي المبيعات',
                          accessor: 'total_sales',
                          cell: (row) => <FormattedCurrency amount={row.total_sales || 0} />
                        },
                        {
                          header: 'إجمالي الأرباح',
                          accessor: 'total_profit',
                          cell: (row) => <FormattedCurrency amount={row.total_profit || 0} isProfit={true} />
                        },
                        {
                          header: 'نسبة الربح',
                          accessor: 'profit_margin',
                          cell: (row) => `${row.profit_margin ? row.profit_margin.toFixed(2) : 0}%`
                        }
                      ]}
                      data={topCustomers}
                      pagination={true}
                      pageSize={10}
                      searchable={true}
                      searchPlaceholder="بحث عن عميل..."
                      emptyMessage="لا توجد بيانات للعرض"
                    />

                    {/* عرض الأصناف الأكثر شراءً لكل عميل */}
                    {topCustomers.map((customer) => {
                      if (!customer.top_items || customer.top_items.length === 0) return null;

                      return (
                        <div key={customer.customer_id} className="customer-top-items mt-4">
                          <h4>الأصناف الأكثر شراءً للعميل: {customer.customer_name}</h4>
                          <DataTable
                            columns={[
                              {
                                header: '#',
                                accessor: 'index',
                                cell: (_, index) => index + 1,
                                style: { width: '50px' }
                              },
                              {
                                header: 'الصنف',
                                accessor: 'item_name',
                                cell: (row) => row.item_name
                              },
                              {
                                header: 'الكمية المباعة',
                                accessor: 'total_quantity',
                                cell: (row) => row.total_quantity || 0
                              },
                              {
                                header: 'إجمالي المبيعات',
                                accessor: 'total_sales',
                                cell: (row) => <FormattedCurrency amount={row.total_sales || 0} />
                              }
                            ]}
                            data={customer.top_items}
                            pagination={false}
                            searchable={false}
                            emptyMessage="لا توجد أصناف لهذا العميل"
                          />
                        </div>
                      );
                    })}
                  </>
                ) : (
                  <div className="empty-state">
                    <FaUser size={48} />
                    <p>لا توجد بيانات عملاء للعرض</p>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* تقرير الأصناف التي تحتاج إلى إعادة طلب */}
        {activeTab === 'lowStock' && (
          <div>
            <h2>تقرير الأصناف التي تحتاج إلى إعادة طلب</h2>

            {isLoadingSpecialReport ? (
              <div className="loading-indicator">جاري تحميل البيانات...</div>
            ) : (
              <>
                {lowStockItems.length > 0 ? (
                  <>
                    {/* إحصائيات الأصناف التي تحتاج إلى إعادة طلب */}
                    <div className="stats-grid">
                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaExclamationTriangle />
                        </div>
                        <div className="stat-card-value">{lowStockItems.length}</div>
                        <div className="stat-card-title">إجمالي الأصناف</div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaExclamationTriangle />
                        </div>
                        <div className="stat-card-value">
                          {lowStockItems.filter(item => item.current_quantity === 0).length}
                        </div>
                        <div className="stat-card-title">أصناف نفذت الكمية</div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaExclamationTriangle />
                        </div>
                        <div className="stat-card-value">
                          {lowStockItems.filter(item => item.current_quantity > 0 && item.current_quantity <= item.minimum_quantity).length}
                        </div>
                        <div className="stat-card-title">أصناف تحت الحد الأدنى</div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaMoneyBillWave />
                        </div>
                        <div className="stat-card-value">
                          <FormattedCurrency amount={lowStockItems.reduce((sum, item) => sum + (item.total_value || 0), 0)} />
                        </div>
                        <div className="stat-card-title">قيمة المخزون</div>
                      </div>
                    </div>

                    {/* جدول الأصناف التي تحتاج إلى إعادة طلب */}
                    <DataTable
                      columns={[
                        {
                          header: '#',
                          accessor: 'index',
                          cell: (_, index) => index + 1,
                          style: { width: '50px' }
                        },
                        {
                          header: 'الصنف',
                          accessor: 'name',
                          cell: (row) => row.name
                        },
                        {
                          header: 'وحدة القياس',
                          accessor: 'unit',
                          cell: (row) => row.unit || '-'
                        },
                        {
                          header: 'الكمية الحالية',
                          accessor: 'current_quantity',
                          cell: (row) => {
                            if (row.current_quantity === 0) {
                              return (
                                <span className="badge badge-danger">
                                  نفذت الكمية
                                </span>
                              );
                            } else if (row.current_quantity <= row.minimum_quantity) {
                              return (
                                <span className="badge badge-warning">
                                  <FaExclamationTriangle />
                                  {row.current_quantity}
                                </span>
                              );
                            } else {
                              return row.current_quantity;
                            }
                          }
                        },
                        {
                          header: 'الحد الأدنى',
                          accessor: 'minimum_quantity',
                          cell: (row) => row.minimum_quantity || 0
                        },
                        {
                          header: 'الكمية المطلوبة',
                          accessor: 'quantity_to_order',
                          cell: (row) => row.quantity_to_order || 0
                        },
                        {
                          header: 'معدل الاستهلاك الشهري',
                          accessor: 'monthly_consumption',
                          cell: (row) => row.monthly_consumption || 0
                        },
                        {
                          header: 'الأيام المتبقية',
                          accessor: 'days_remaining',
                          cell: (row) => {
                            if (row.days_remaining === null) {
                              return '-';
                            } else if (row.days_remaining <= 7) {
                              return (
                                <span className="badge badge-danger">
                                  {row.days_remaining} يوم
                                </span>
                              );
                            } else if (row.days_remaining <= 14) {
                              return (
                                <span className="badge badge-warning">
                                  {row.days_remaining} يوم
                                </span>
                              );
                            } else {
                              return `${row.days_remaining} يوم`;
                            }
                          }
                        }
                      ]}
                      data={lowStockItems}
                      pagination={true}
                      pageSize={10}
                      searchable={true}
                      searchPlaceholder="بحث عن صنف..."
                      emptyMessage="لا توجد بيانات للعرض"
                    />
                  </>
                ) : (
                  <div className="empty-state">
                    <FaBoxes size={48} />
                    <p>لا توجد أصناف تحتاج إلى إعادة طلب</p>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* التقرير المفصل الشامل */}
        {activeTab === 'comprehensiveReport' && (
          <div>
            <h2>التقرير المفصل الشامل</h2>

            <div className="report-header-section">
              <p className="report-subtitle">
                <FaCalendarAlt style={{ marginLeft: '5px' }} />
                {dateRange === 'quarter' ? 'الربع الحالي' :
                 dateRange === 'halfYear' ? 'النصف سنوي' :
                 dateRange === 'threeQuarters' ? 'ثلاثة أرباع السنة' :
                 dateRange === 'year' ? 'السنة الحالية' : 'جميع الفترات'}
                 - تاريخ التقرير: {formatDate(new Date().toISOString())}
              </p>
            </div>

            <div className="comprehensive-report-container">
              <div className="comprehensive-report-description">
                <FaInfoCircle style={{ marginLeft: '8px', color: 'var(--primary-color)' }} />
                <p>
                  هذا التقرير المفصل يشمل جميع بنود التقارير المتاحة في النظام. يمكنك طباعة هذا التقرير للحصول على نظرة شاملة عن حالة المخزون والمبيعات والأرباح.
                </p>
              </div>

              <div className="comprehensive-report-actions">
                <button
                  className="btn btn-primary btn-lg"
                  onClick={printComprehensiveReport}
                  disabled={isLoadingSpecialReport}
                >
                  {isLoadingSpecialReport ? (
                    <>
                      <span className="spinner-border spinner-border-sm ml-2" role="status" aria-hidden="true"></span>
                      جاري التحميل...
                    </>
                  ) : (
                    <>
                      <FaPrint className="ml-2" />
                      طباعة التقرير المفصل الشامل
                    </>
                  )}
                </button>
              </div>

              <div className="comprehensive-report-sections">
                <h3>محتويات التقرير:</h3>
                <ul className="comprehensive-report-list">
                  <li>
                    <FaBoxes className="report-icon" />
                    <span>تقرير المخزون: </span>
                    <span className="report-description">إحصائيات المخزون وقائمة الأصناف</span>
                  </li>
                  <li>
                    <FaChartBar className="report-icon" />
                    <span>تقرير المعاملات: </span>
                    <span className="report-description">إحصائيات المبيعات والمشتريات</span>
                  </li>
                  <li>
                    <FaMoneyBillWave className="report-icon" />
                    <span>تقرير الأرباح: </span>
                    <span className="report-description">إحصائيات الأرباح للفترات المختلفة</span>
                  </li>
                  <li>
                    <FaUndo className="report-icon" />
                    <span>تقرير الإرجاعات: </span>
                    <span className="report-description">إحصائيات وتفاصيل عمليات الإرجاع</span>
                  </li>
                  <li>
                    <FaShoppingCart className="report-icon" />
                    <span>تقرير الأصناف الأكثر مبيعًا: </span>
                    <span className="report-description">قائمة الأصناف الأكثر مبيعًا وربحًا</span>
                  </li>
                  <li>
                    <FaUser className="report-icon" />
                    <span>تقرير العملاء الأكثر شراءً: </span>
                    <span className="report-description">قائمة العملاء الأكثر شراءً</span>
                  </li>
                  <li>
                    <FaExclamationTriangle className="report-icon" />
                    <span>تقرير الأصناف التي تحتاج إعادة طلب: </span>
                    <span className="report-description">قائمة الأصناف التي تحتاج إلى إعادة طلب</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* تقرير الخزينة */}
        {activeTab === 'cashbox' && (
          <div>
            <h2>تقرير الخزينة</h2>

            <div className="report-header-section">
              <p className="report-subtitle">
                <FaCalendarAlt style={{ marginLeft: '5px' }} />
                {dateRange === 'quarter' ? 'الربع الحالي' :
                 dateRange === 'halfYear' ? 'النصف سنوي' :
                 dateRange === 'threeQuarters' ? 'ثلاثة أرباع السنة' :
                 dateRange === 'year' ? 'السنة الحالية' : 'جميع الفترات'}
                 - تاريخ التقرير: {formatDate(new Date().toISOString())}
              </p>
            </div>

            {isLoadingSpecialReport ? (
              <div className="loading-indicator">جاري تحميل البيانات...</div>
            ) : (
              <>
                {cashboxReport ? (
                  <>
                    {/* معلومات الخزينة */}
                    <div className="cashbox-info mb-4">
                      <div className="card">
                        <div className="card-header">
                          <h4>معلومات الخزينة</h4>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            <div className="col-md-6">
                              <p><strong>الرصيد الابتدائي:</strong> <FormattedCurrency amount={cashboxReport.cashbox.initial_balance} /></p>
                              <p><strong>الرصيد الحالي:</strong> <FormattedCurrency amount={cashboxReport.cashbox.current_balance} /></p>
                            </div>
                            <div className="col-md-6">
                              <p><strong>تاريخ الإنشاء:</strong> {formatDate(cashboxReport.cashbox.created_at)}</p>
                              <p><strong>آخر تحديث:</strong> {formatDate(cashboxReport.cashbox.updated_at)}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* إحصائيات الخزينة */}
                    <div className="stats-grid">
                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaArrowCircleDown />
                        </div>
                        <div className="stat-card-value">
                          <FormattedCurrency amount={cashboxReport.stats.totalIncome} />
                        </div>
                        <div className="stat-card-title">إجمالي الدخل</div>
                        <div className="stat-card-subtitle">
                          {cashboxReport.stats.incomeCount} معاملة
                        </div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaArrowCircleUp />
                        </div>
                        <div className="stat-card-value">
                          <FormattedCurrency amount={cashboxReport.stats.totalExpenses} />
                        </div>
                        <div className="stat-card-title">إجمالي المصروفات</div>
                        <div className="stat-card-subtitle">
                          {cashboxReport.stats.expenseCount} معاملة
                        </div>
                      </div>

                      <div className="stat-card">
                        <div className="stat-card-icon">
                          <FaMoneyBillWave />
                        </div>
                        <div className="stat-card-value">
                          <FormattedCurrency amount={cashboxReport.stats.balance} />
                        </div>
                        <div className="stat-card-title">الرصيد</div>
                        <div className="stat-card-subtitle">
                          {cashboxReport.stats.totalCount} معاملة
                        </div>
                      </div>
                    </div>

                    {/* تحليل المعاملات حسب المصدر */}
                    {cashboxReport.sourceStats && cashboxReport.sourceStats.length > 0 && (
                      <div className="mb-4">
                        <h4>تحليل المعاملات حسب المصدر</h4>
                        <DataTable
                          columns={[
                            {
                              header: 'المصدر',
                              accessor: 'source',
                              cell: (row) => row.source
                            },
                            {
                              header: 'عدد المعاملات',
                              accessor: 'count',
                              cell: (row) => row.count
                            },
                            {
                              header: 'الدخل',
                              accessor: 'income',
                              cell: (row) => <FormattedCurrency amount={row.income} />
                            },
                            {
                              header: 'المصروفات',
                              accessor: 'expense',
                              cell: (row) => <FormattedCurrency amount={row.expense} />
                            },
                            {
                              header: 'الصافي',
                              accessor: 'net',
                              cell: (row) => (
                                <span className={row.net >= 0 ? 'text-success' : 'text-danger'}>
                                  <FormattedCurrency amount={row.net} />
                                </span>
                              )
                            }
                          ]}
                          data={cashboxReport.sourceStats}
                          pagination={true}
                          pageSize={10}
                          searchable={true}
                          searchPlaceholder="بحث عن مصدر..."
                          emptyMessage="لا توجد بيانات للعرض"
                        />
                      </div>
                    )}

                    {/* تحليل المعاملات حسب الشهر */}
                    {cashboxReport.dateStats && cashboxReport.dateStats.length > 0 && (
                      <div className="mb-4">
                        <h4>تحليل المعاملات حسب الشهر</h4>
                        <DataTable
                          columns={[
                            {
                              header: 'الشهر',
                              accessor: 'label',
                              cell: (row) => row.label
                            },
                            {
                              header: 'عدد المعاملات',
                              accessor: 'count',
                              cell: (row) => row.count
                            },
                            {
                              header: 'الدخل',
                              accessor: 'income',
                              cell: (row) => <FormattedCurrency amount={row.income} />
                            },
                            {
                              header: 'المصروفات',
                              accessor: 'expense',
                              cell: (row) => <FormattedCurrency amount={row.expense} />
                            },
                            {
                              header: 'الصافي',
                              accessor: 'net',
                              cell: (row) => (
                                <span className={row.net >= 0 ? 'text-success' : 'text-danger'}>
                                  <FormattedCurrency amount={row.net} />
                                </span>
                              )
                            }
                          ]}
                          data={cashboxReport.dateStats}
                          pagination={true}
                          pageSize={10}
                          searchable={false}
                          emptyMessage="لا توجد بيانات للعرض"
                        />
                      </div>
                    )}

                    {/* جدول معاملات الخزينة */}
                    <h4>معاملات الخزينة</h4>
                    <DataTable
                      columns={[
                        {
                          header: '#',
                          accessor: 'index',
                          cell: (_, index) => index + 1,
                          style: { width: '50px' }
                        },
                        {
                          header: 'التاريخ',
                          accessor: 'created_at',
                          cell: (row) => formatDate(row.created_at)
                        },
                        {
                          header: 'النوع',
                          accessor: 'type',
                          cell: (row) => (
                            <span className={row.type === 'income' ? 'badge badge-success' : 'badge badge-danger'}>
                              {row.type === 'income' ? 'دخل' : 'مصروف'}
                            </span>
                          )
                        },
                        {
                          header: 'المصدر',
                          accessor: 'source',
                          cell: (row) => row.source || '-'
                        },
                        {
                          header: 'المبلغ',
                          accessor: 'amount',
                          cell: (row) => <FormattedCurrency amount={row.amount} />
                        },
                        {
                          header: 'الملاحظات',
                          accessor: 'notes',
                          cell: (row) => row.notes || '-'
                        },
                        {
                          header: 'المستخدم',
                          accessor: 'user_name',
                          cell: (row) => row.user_name || '-'
                        }
                      ]}
                      data={cashboxReport.transactions}
                      pagination={true}
                      pageSize={10}
                      searchable={true}
                      searchPlaceholder="بحث عن معاملة..."
                      emptyMessage="لا توجد معاملات للعرض"
                    />
                  </>
                ) : (
                  <div className="empty-state">
                    <FaMoneyBillWave size={48} />
                    <p>لا توجد بيانات خزينة للعرض</p>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* قسم فواتير العملاء */}
        {/* تقرير المخزون والأصناف الشامل */}
        {activeTab === 'inventoryDetailed' && (
          <div>
            <h2>تقرير المخزون والأصناف الشامل</h2>

            <div className="report-header-section">
              <p className="report-subtitle">
                <FaCalendarAlt style={{ marginLeft: '5px' }} />
                {dateRange === 'quarter' ? 'الربع الحالي' :
                 dateRange === 'halfYear' ? 'النصف سنوي' :
                 dateRange === 'threeQuarters' ? 'ثلاثة أرباع السنة' :
                 dateRange === 'year' ? 'السنة الحالية' : 'جميع الفترات'}
                 - تاريخ التقرير: {formatDate(new Date().toISOString())}
              </p>
            </div>

            {isLoadingSpecialReport ? (
              <div className="loading-indicator">جاري تحميل البيانات...</div>
            ) : (
              <>
                {/* شريط التنقل بين أقسام التقرير */}
                <div className="inventory-report-tabs">
                  <button
                    className="inventory-tab-btn"
                    onClick={() => document.getElementById('inventory-summary').scrollIntoView({ behavior: 'smooth' })}
                  >
                    <FaBoxes /> ملخص المخزون
                  </button>
                  <button
                    className="inventory-tab-btn"
                    onClick={() => document.getElementById('low-stock-items').scrollIntoView({ behavior: 'smooth' })}
                  >
                    <FaExclamationTriangle /> الأصناف التي تحتاج طلب
                  </button>
                  <button
                    className="inventory-tab-btn"
                    onClick={() => document.getElementById('top-selling-items').scrollIntoView({ behavior: 'smooth' })}
                  >
                    <FaShoppingCart /> الأصناف الأكثر مبيعًا
                  </button>
                  <button
                    className="inventory-tab-btn"
                    onClick={() => document.getElementById('inventory-list').scrollIntoView({ behavior: 'smooth' })}
                  >
                    <FaList /> قائمة المخزون
                  </button>
                  <button
                    className="inventory-tab-btn"
                    onClick={() => document.getElementById('inventory-analysis').scrollIntoView({ behavior: 'smooth' })}
                  >
                    <FaChartBar /> تحليل المخزون
                  </button>
                </div>

                {/* قسم ملخص المخزون */}
                <div id="inventory-summary" className="detailed-report-section">
                  <h3>
                    <FaBoxes style={{ marginLeft: '8px' }} />
                    ملخص المخزون
                  </h3>
                  <div className="stats-grid">
                    <div className="stat-card">
                      <div className="stat-card-icon">
                        <FaBoxes />
                      </div>
                      <div className="stat-card-value">{inventoryStats.total}</div>
                      <div className="stat-card-title">إجمالي الأصناف</div>
                    </div>

                    <div className="stat-card">
                      <div className="stat-card-icon">
                        <FaExclamationTriangle />
                      </div>
                      <div className="stat-card-value">{inventoryStats.low}</div>
                      <div className="stat-card-title">أصناف تحت الحد الأدنى</div>
                    </div>

                    <div className="stat-card">
                      <div className="stat-card-icon">
                        <FaExclamationTriangle />
                      </div>
                      <div className="stat-card-value">{inventoryStats.out}</div>
                      <div className="stat-card-title">أصناف نفذت الكمية</div>
                    </div>

                    <div className="stat-card">
                      <div className="stat-card-icon">
                        <FaMoneyBillWave />
                      </div>
                      <div className="stat-card-value">
                        <FormattedCurrency amount={inventoryStats.value} />
                      </div>
                      <div className="stat-card-title">قيمة المخزون</div>
                    </div>
                  </div>

                  {/* إضافة مؤشرات أداء المخزون */}
                  <div className="inventory-kpi-section">
                    <h4>مؤشرات أداء المخزون</h4>
                    <div className="kpi-grid">
                      <div className="kpi-card">
                        <div className="kpi-title">نسبة الأصناف المتوفرة</div>
                        <div className="kpi-value">
                          {inventoryStats.total > 0 ?
                            `${Math.round(((inventoryStats.total - inventoryStats.out) / inventoryStats.total) * 100)}%` :
                            '0%'}
                        </div>
                        <div className="kpi-progress">
                          <div
                            className="kpi-progress-bar"
                            style={{
                              width: inventoryStats.total > 0 ?
                                `${Math.round(((inventoryStats.total - inventoryStats.out) / inventoryStats.total) * 100)}%` :
                                '0%',
                              backgroundColor: inventoryStats.total > 0 && ((inventoryStats.total - inventoryStats.out) / inventoryStats.total) < 0.7 ?
                                '#e74c3c' : '#27ae60'
                            }}
                          ></div>
                        </div>
                      </div>

                      <div className="kpi-card">
                        <div className="kpi-title">نسبة الأصناف تحت الحد الأدنى</div>
                        <div className="kpi-value">
                          {inventoryStats.total > 0 ?
                            `${Math.round((inventoryStats.low / inventoryStats.total) * 100)}%` :
                            '0%'}
                        </div>
                        <div className="kpi-progress">
                          <div
                            className="kpi-progress-bar"
                            style={{
                              width: inventoryStats.total > 0 ?
                                `${Math.round((inventoryStats.low / inventoryStats.total) * 100)}%` :
                                '0%',
                              backgroundColor: inventoryStats.total > 0 && (inventoryStats.low / inventoryStats.total) > 0.3 ?
                                '#e74c3c' : '#27ae60'
                            }}
                          ></div>
                        </div>
                      </div>

                      <div className="kpi-card">
                        <div className="kpi-title">متوسط قيمة الصنف</div>
                        <div className="kpi-value">
                          <FormattedCurrency amount={inventoryStats.total > 0 ? inventoryStats.value / inventoryStats.total : 0} />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* قسم الأصناف التي تحتاج إعادة طلب */}
                <div id="low-stock-items" className="detailed-report-section">
                  <h3>
                    <FaExclamationTriangle style={{ marginLeft: '8px' }} />
                    الأصناف التي تحتاج إعادة طلب
                  </h3>

                  {/* ملخص الأصناف التي تحتاج إعادة طلب */}
                  <div className="low-stock-summary">
                    <div className="low-stock-summary-item">
                      <div className="summary-icon danger">
                        <FaExclamationTriangle />
                      </div>
                      <div className="summary-details">
                        <div className="summary-value">{lowStockItems.filter(item => item.current_quantity === 0).length}</div>
                        <div className="summary-label">أصناف نفذت الكمية</div>
                      </div>
                    </div>
                    <div className="low-stock-summary-item">
                      <div className="summary-icon warning">
                        <FaExclamationTriangle />
                      </div>
                      <div className="summary-details">
                        <div className="summary-value">{lowStockItems.filter(item => item.current_quantity > 0 && item.current_quantity <= item.minimum_quantity).length}</div>
                        <div className="summary-label">أصناف تحت الحد الأدنى</div>
                      </div>
                    </div>
                    <div className="low-stock-summary-item">
                      <div className="summary-icon">
                        <FaMoneyBillWave />
                      </div>
                      <div className="summary-details">
                        <div className="summary-value">
                          <FormattedCurrency
                            amount={lowStockItems.reduce((sum, item) => {
                              const requiredQuantity = Math.max(0, item.minimum_quantity - item.current_quantity);
                              return sum + (requiredQuantity * (item.avg_price || 0));
                            }, 0)}
                          />
                        </div>
                        <div className="summary-label">التكلفة المتوقعة للطلب</div>
                      </div>
                    </div>
                  </div>

                  {lowStockItems.length > 0 ? (
                    <>
                      <div className="action-buttons">
                        <Button
                          variant="primary"
                          size="sm"
                          icon={<FaPrint />}
                          iconPosition="left"
                          onClick={() => {
                            // طباعة قائمة الأصناف التي تحتاج إعادة طلب
                            const printWindow = window.open('', '_blank');
                            if (printWindow) {
                              printWindow.document.write(`
                                <html dir="rtl">
                                <head>
                                  <title>قائمة الأصناف التي تحتاج إعادة طلب</title>
                                  <style>
                                    body { font-family: Arial, sans-serif; direction: rtl; }
                                    table { width: 100%; border-collapse: collapse; }
                                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                                    th { background-color: #f2f2f2; }
                                    .text-danger { color: red; }
                                    h1 { text-align: center; }
                                  </style>
                                </head>
                                <body>
                                  <h1>قائمة الأصناف التي تحتاج إعادة طلب</h1>
                                  <table>
                                    <thead>
                                      <tr>
                                        <th>#</th>
                                        <th>الصنف</th>
                                        <th>وحدة القياس</th>
                                        <th>الكمية الحالية</th>
                                        <th>الحد الأدنى</th>
                                        <th>الكمية المطلوبة</th>
                                        <th>متوسط السعر</th>
                                        <th>التكلفة المتوقعة</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      ${lowStockItems.map((item, index) => {
                                        const requiredQuantity = Math.max(0, item.minimum_quantity - item.current_quantity);
                                        const estimatedCost = requiredQuantity * (item.avg_price || 0);
                                        return `
                                          <tr>
                                            <td>${index + 1}</td>
                                            <td>${item.name}</td>
                                            <td>${item.unit || '-'}</td>
                                            <td ${item.current_quantity === 0 ? 'class="text-danger"' : ''}>${item.current_quantity}</td>
                                            <td>${item.minimum_quantity || 0}</td>
                                            <td>${requiredQuantity}</td>
                                            <td>${formatCurrency(item.avg_price || 0)}</td>
                                            <td>${formatCurrency(estimatedCost)}</td>
                                          </tr>
                                        `;
                                      }).join('')}
                                    </tbody>
                                  </table>
                                </body>
                                </html>
                              `);
                              printWindow.document.close();
                              printWindow.print();
                            }
                          }}
                        >
                          طباعة قائمة الطلب
                        </Button>
                        <Button
                          variant="secondary"
                          size="sm"
                          icon={<FaFileExport />}
                          iconPosition="left"
                          onClick={() => {
                            // تصدير قائمة الأصناف التي تحتاج إعادة طلب
                            let csvContent = 'الرقم,الصنف,وحدة القياس,الكمية الحالية,الحد الأدنى,الكمية المطلوبة,متوسط السعر,التكلفة المتوقعة\n';

                            lowStockItems.forEach((item, index) => {
                              const requiredQuantity = Math.max(0, item.minimum_quantity - item.current_quantity);
                              const estimatedCost = requiredQuantity * (item.avg_price || 0);
                              csvContent += `${index + 1},${item.name},${item.unit || ''},${item.current_quantity},${item.minimum_quantity || 0},${requiredQuantity},${item.avg_price || 0},${estimatedCost}\n`;
                            });

                            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                            const url = URL.createObjectURL(blob);
                            const link = document.createElement('a');
                            link.setAttribute('href', url);
                            link.setAttribute('download', 'قائمة_الأصناف_التي_تحتاج_إعادة_طلب.csv');
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                        >
                          تصدير قائمة الطلب
                        </Button>
                      </div>

                      <DataTable
                        columns={[
                          {
                            header: '#',
                            accessor: 'index',
                            cell: (_, index) => index + 1,
                            style: { width: '50px' }
                          },
                          {
                            header: 'الصنف',
                            accessor: 'name',
                            cell: (row) => row.name
                          },
                          {
                            header: 'وحدة القياس',
                            accessor: 'unit',
                            cell: (row) => row.unit || '-'
                          },
                          {
                            header: 'الكمية الحالية',
                            accessor: 'current_quantity',
                            cell: (row) => {
                              if (row.current_quantity === 0) {
                                return (
                                  <span className="badge badge-danger">
                                    نفذت الكمية
                                  </span>
                                );
                              } else if (row.current_quantity <= row.minimum_quantity) {
                                return (
                                  <span className="badge badge-warning">
                                    <FaExclamationTriangle />
                                    {row.current_quantity}
                                  </span>
                                );
                              } else {
                                return row.current_quantity;
                              }
                            }
                          },
                          {
                            header: 'الحد الأدنى',
                            accessor: 'minimum_quantity',
                            cell: (row) => row.minimum_quantity || 0
                          },
                          {
                            header: 'الكمية المطلوبة',
                            accessor: 'quantity_to_order',
                            cell: (row) => row.quantity_to_order || 0
                          },
                          {
                            header: 'متوسط السعر',
                            accessor: 'avg_price',
                            cell: (row) => <FormattedCurrency amount={row.avg_price || 0} />
                          },
                          {
                            header: 'التكلفة المتوقعة',
                            accessor: 'estimated_cost',
                            cell: (row) => <FormattedCurrency amount={(row.quantity_to_order || 0) * (row.avg_price || 0)} />
                          },
                          {
                            header: 'الأولوية',
                            accessor: 'priority',
                            cell: (row) => {
                              if (row.current_quantity === 0) {
                                return <span className="badge badge-danger">عالية</span>;
                              } else if (row.current_quantity <= row.minimum_quantity / 2) {
                                return <span className="badge badge-warning">متوسطة</span>;
                              } else {
                                return <span className="badge badge-info">منخفضة</span>;
                              }
                            }
                          }
                        ]}
                        data={lowStockItems}
                        pagination={true}
                        pageSize={5}
                        searchable={true}
                        searchPlaceholder="بحث عن صنف..."
                        emptyMessage="لا توجد أصناف تحتاج إلى إعادة طلب"
                      />
                    </>
                  ) : (
                    <div className="empty-state">
                      <FaBoxes size={48} />
                      <p>لا توجد أصناف تحتاج إلى إعادة طلب</p>
                    </div>
                  )}
                </div>

                {/* قسم الأصناف الأكثر مبيعًا */}
                <div id="top-selling-items" className="detailed-report-section">
                  <h3>
                    <FaShoppingCart style={{ marginLeft: '8px' }} />
                    الأصناف الأكثر مبيعًا
                  </h3>
                  {topSellingItems.length > 0 ? (
                    <>
                      <div className="top-selling-summary">
                        <div className="top-selling-stats">
                          <div className="stats-grid">
                            <div className="stat-card">
                              <div className="stat-card-icon">
                                <FaBoxes />
                              </div>
                              <div className="stat-card-value">{topSellingItems.length}</div>
                              <div className="stat-card-title">عدد الأصناف</div>
                            </div>

                            <div className="stat-card">
                              <div className="stat-card-icon">
                                <FaShoppingCart />
                              </div>
                              <div className="stat-card-value">
                                {topSellingItems.reduce((sum, item) => sum + (item.total_quantity || 0), 0)}
                              </div>
                              <div className="stat-card-title">إجمالي الكميات المباعة</div>
                            </div>

                            <div className="stat-card">
                              <div className="stat-card-icon">
                                <FaMoneyBillWave />
                              </div>
                              <div className="stat-card-value">
                                <FormattedCurrency amount={topSellingItems.reduce((sum, item) => sum + (item.total_sales || 0), 0)} />
                              </div>
                              <div className="stat-card-title">إجمالي المبيعات</div>
                            </div>

                            <div className="stat-card">
                              <div className="stat-card-icon">
                                <FaPercentage />
                              </div>
                              <div className="stat-card-value">
                                <FormattedCurrency amount={topSellingItems.reduce((sum, item) => sum + (item.total_profit || 0), 0)} isProfit={true} />
                              </div>
                              <div className="stat-card-title">إجمالي الأرباح</div>
                            </div>
                          </div>
                        </div>

                        {/* عرض الأصناف الخمسة الأكثر مبيعًا في رسم بياني */}
                        <div className="top-selling-chart">
                          <h4>الأصناف الخمسة الأكثر مبيعًا</h4>
                          <div className="chart-container">
                            {topSellingItems.slice(0, 5).map((item, index) => {
                              // حساب النسبة المئوية من إجمالي المبيعات
                              const totalSales = topSellingItems.reduce((sum, i) => sum + (i.total_sales || 0), 0);
                              const percentage = totalSales > 0 ? ((item.total_sales || 0) / totalSales) * 100 : 0;

                              return (
                                <div key={index} className="chart-item">
                                  <div className="chart-label">{item.item_name}</div>
                                  <div className="chart-bar-container">
                                    <div
                                      className="chart-bar"
                                      style={{
                                        width: `${percentage}%`,
                                        backgroundColor: `hsl(${210 - (index * 30)}, 70%, 50%)`
                                      }}
                                    ></div>
                                    <div className="chart-value">
                                      <FormattedCurrency amount={item.total_sales || 0} />
                                      <span className="percentage">({percentage.toFixed(1)}%)</span>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </div>

                      <div className="action-buttons">
                        <Button
                          variant="primary"
                          size="sm"
                          icon={<FaPrint />}
                          iconPosition="left"
                          onClick={() => {
                            // طباعة تقرير الأصناف الأكثر مبيعًا
                            const printWindow = window.open('', '_blank');
                            if (printWindow) {
                              printWindow.document.write(`
                                <html dir="rtl">
                                <head>
                                  <title>تقرير الأصناف الأكثر مبيعًا</title>
                                  <style>
                                    body { font-family: Arial, sans-serif; direction: rtl; }
                                    table { width: 100%; border-collapse: collapse; }
                                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                                    th { background-color: #f2f2f2; }
                                    h1, h2 { text-align: center; }
                                    .summary { display: flex; justify-content: space-around; margin: 20px 0; }
                                    .summary-item { text-align: center; }
                                    .summary-value { font-size: 24px; font-weight: bold; }
                                    .summary-label { color: #666; }
                                  </style>
                                </head>
                                <body>
                                  <h1>تقرير الأصناف الأكثر مبيعًا</h1>
                                  <p style="text-align: center;">
                                    الفترة: ${dateRange === 'quarter' ? 'الربع الحالي' :
                                      dateRange === 'halfYear' ? 'النصف سنوي' :
                                      dateRange === 'threeQuarters' ? 'ثلاثة أرباع السنة' :
                                      dateRange === 'year' ? 'السنة الحالية' : 'جميع الفترات'}
                                    - تاريخ التقرير: ${formatDate(new Date().toISOString())}
                                  </p>

                                  <div class="summary">
                                    <div class="summary-item">
                                      <div class="summary-value">${topSellingItems.length}</div>
                                      <div class="summary-label">عدد الأصناف</div>
                                    </div>
                                    <div class="summary-item">
                                      <div class="summary-value">${topSellingItems.reduce((sum, item) => sum + (item.total_quantity || 0), 0)}</div>
                                      <div class="summary-label">إجمالي الكميات المباعة</div>
                                    </div>
                                    <div class="summary-item">
                                      <div class="summary-value">${formatCurrency(topSellingItems.reduce((sum, item) => sum + (item.total_sales || 0), 0))}</div>
                                      <div class="summary-label">إجمالي المبيعات</div>
                                    </div>
                                    <div class="summary-item">
                                      <div class="summary-value">${formatCurrency(topSellingItems.reduce((sum, item) => sum + (item.total_profit || 0), 0))}</div>
                                      <div class="summary-label">إجمالي الأرباح</div>
                                    </div>
                                  </div>

                                  <h2>قائمة الأصناف الأكثر مبيعًا</h2>
                                  <table>
                                    <thead>
                                      <tr>
                                        <th>#</th>
                                        <th>الصنف</th>
                                        <th>وحدة القياس</th>
                                        <th>الكمية المباعة</th>
                                        <th>إجمالي المبيعات</th>
                                        <th>إجمالي الأرباح</th>
                                        <th>نسبة الربح</th>
                                        <th>الكمية الحالية</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      ${topSellingItems.map((item, index) => {
                                        return `
                                          <tr>
                                            <td>${index + 1}</td>
                                            <td>${item.item_name}</td>
                                            <td>${item.item_unit || '-'}</td>
                                            <td>${item.total_quantity || 0}</td>
                                            <td>${formatCurrency(item.total_sales || 0)}</td>
                                            <td>${formatCurrency(item.total_profit || 0)}</td>
                                            <td>${item.profit_margin ? item.profit_margin.toFixed(2) : 0}%</td>
                                            <td>${item.current_quantity === 0 ? 'نفذت الكمية' : item.current_quantity}</td>
                                          </tr>
                                        `;
                                      }).join('')}
                                    </tbody>
                                  </table>
                                </body>
                                </html>
                              `);
                              printWindow.document.close();
                              printWindow.print();
                            }
                          }}
                        >
                          طباعة التقرير
                        </Button>
                        <Button
                          variant="secondary"
                          size="sm"
                          icon={<FaFileExport />}
                          iconPosition="left"
                          onClick={() => {
                            // تصدير تقرير الأصناف الأكثر مبيعًا
                            let csvContent = 'الرقم,الصنف,وحدة القياس,الكمية المباعة,إجمالي المبيعات,إجمالي الأرباح,نسبة الربح,الكمية الحالية\n';

                            topSellingItems.forEach((item, index) => {
                              csvContent += `${index + 1},${item.item_name},${item.item_unit || ''},${item.total_quantity || 0},${item.total_sales || 0},${item.total_profit || 0},${item.profit_margin ? item.profit_margin.toFixed(2) : 0}%,${item.current_quantity}\n`;
                            });

                            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                            const url = URL.createObjectURL(blob);
                            const link = document.createElement('a');
                            link.setAttribute('href', url);
                            link.setAttribute('download', 'تقرير_الأصناف_الأكثر_مبيعًا.csv');
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                        >
                          تصدير التقرير
                        </Button>
                      </div>

                      <DataTable
                        columns={[
                          {
                            header: '#',
                            accessor: 'index',
                            cell: (_, index) => index + 1,
                            style: { width: '50px' }
                          },
                          {
                            header: 'الصنف',
                            accessor: 'item_name',
                            cell: (row) => row.item_name
                          },
                          {
                            header: 'وحدة القياس',
                            accessor: 'item_unit',
                            cell: (row) => row.item_unit || '-'
                          },
                          {
                            header: 'الكمية المباعة',
                            accessor: 'total_quantity',
                            cell: (row) => row.total_quantity || 0
                          },
                          {
                            header: 'إجمالي المبيعات',
                            accessor: 'total_sales',
                            cell: (row) => <FormattedCurrency amount={row.total_sales || 0} />
                          },
                          {
                            header: 'إجمالي الأرباح',
                            accessor: 'total_profit',
                            cell: (row) => <FormattedCurrency amount={row.total_profit || 0} isProfit={true} />
                          },
                          {
                            header: 'نسبة الربح',
                            accessor: 'profit_margin',
                            cell: (row) => `${row.profit_margin ? row.profit_margin.toFixed(2) : 0}%`
                          },
                          {
                            header: 'الكمية الحالية',
                            accessor: 'current_quantity',
                            cell: (row) => {
                              if (row.current_quantity === 0) {
                                return (
                                  <span className="badge badge-danger">
                                    نفذت الكمية
                                  </span>
                                );
                              } else {
                                return row.current_quantity;
                              }
                            }
                          }
                        ]}
                        data={topSellingItems}
                        pagination={true}
                        pageSize={5}
                        searchable={true}
                        searchPlaceholder="بحث عن صنف..."
                        emptyMessage="لا توجد بيانات للعرض"
                      />
                    </>
                  ) : (
                    <div className="empty-state">
                      <FaShoppingCart size={48} />
                      <p>لا توجد بيانات مبيعات للعرض</p>
                    </div>
                  )}
                </div>

                {/* قسم المخزون الكامل */}
                <div id="inventory-list" className="detailed-report-section">
                  <h3>
                    <FaBoxes style={{ marginLeft: '8px' }} />
                    قائمة المخزون الكاملة
                  </h3>

                  <div className="inventory-filters">
                    <div className="filter-group">
                      <label>تصفية حسب الحالة:</label>
                      <select
                        className="filter-select"
                        onChange={(e) => {
                          // تنفيذ تصفية المخزون حسب الحالة
                          const filterValue = e.target.value;
                          let filteredData = [...inventory];

                          if (filterValue === 'out-of-stock') {
                            filteredData = inventory.filter(item => item.current_quantity === 0);
                          } else if (filterValue === 'low-stock') {
                            filteredData = inventory.filter(item => item.current_quantity > 0 && item.current_quantity <= item.minimum_quantity);
                          } else if (filterValue === 'in-stock') {
                            filteredData = inventory.filter(item => item.current_quantity > item.minimum_quantity);
                          }

                          // تحديث البيانات المعروضة (يمكن إضافة حالة للبيانات المفلترة)
                          // هذا مجرد مثال، يمكن تنفيذه بشكل أفضل باستخدام حالة React
                          const dataTable = document.querySelector('#inventory-table');
                          if (dataTable) {
                            // يمكن تنفيذ التصفية هنا
                          }
                        }}
                      >
                        <option value="all">جميع الأصناف</option>
                        <option value="in-stock">متوفر في المخزون</option>
                        <option value="low-stock">تحت الحد الأدنى</option>
                        <option value="out-of-stock">نفذت الكمية</option>
                      </select>
                    </div>

                    <div className="filter-group">
                      <label>ترتيب حسب:</label>
                      <select
                        className="filter-select"
                        onChange={(e) => {
                          // تنفيذ ترتيب المخزون
                          const sortValue = e.target.value;
                          let sortedData = [...inventory];

                          if (sortValue === 'name-asc') {
                            sortedData.sort((a, b) => a.name.localeCompare(b.name));
                          } else if (sortValue === 'name-desc') {
                            sortedData.sort((a, b) => b.name.localeCompare(a.name));
                          } else if (sortValue === 'quantity-asc') {
                            sortedData.sort((a, b) => a.current_quantity - b.current_quantity);
                          } else if (sortValue === 'quantity-desc') {
                            sortedData.sort((a, b) => b.current_quantity - a.current_quantity);
                          } else if (sortValue === 'value-asc') {
                            sortedData.sort((a, b) => (a.current_quantity * a.avg_price) - (b.current_quantity * b.avg_price));
                          } else if (sortValue === 'value-desc') {
                            sortedData.sort((a, b) => (b.current_quantity * b.avg_price) - (a.current_quantity * a.avg_price));
                          }

                          // تحديث البيانات المعروضة
                          // هذا مجرد مثال، يمكن تنفيذه بشكل أفضل باستخدام حالة React
                        }}
                      >
                        <option value="name-asc">الاسم (تصاعدي)</option>
                        <option value="name-desc">الاسم (تنازلي)</option>
                        <option value="quantity-asc">الكمية (تصاعدي)</option>
                        <option value="quantity-desc">الكمية (تنازلي)</option>
                        <option value="value-asc">القيمة (تصاعدي)</option>
                        <option value="value-desc">القيمة (تنازلي)</option>
                      </select>
                    </div>

                    <div className="action-buttons">
                      <Button
                        variant="primary"
                        size="sm"
                        icon={<FaPrint />}
                        iconPosition="left"
                        onClick={() => {
                          // طباعة قائمة المخزون
                          const printWindow = window.open('', '_blank');
                          if (printWindow) {
                            printWindow.document.write(`
                              <html dir="rtl">
                              <head>
                                <title>قائمة المخزون الكاملة</title>
                                <style>
                                  body { font-family: Arial, sans-serif; direction: rtl; }
                                  table { width: 100%; border-collapse: collapse; }
                                  th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                                  th { background-color: #f2f2f2; }
                                  .text-danger { color: red; }
                                  .text-warning { color: orange; }
                                  h1 { text-align: center; }
                                  .summary { display: flex; justify-content: space-around; margin: 20px 0; }
                                  .summary-item { text-align: center; }
                                  .summary-value { font-size: 24px; font-weight: bold; }
                                  .summary-label { color: #666; }
                                </style>
                              </head>
                              <body>
                                <h1>قائمة المخزون الكاملة</h1>
                                <p style="text-align: center;">تاريخ التقرير: ${formatDate(new Date().toISOString())}</p>

                                <div class="summary">
                                  <div class="summary-item">
                                    <div class="summary-value">${inventory.length}</div>
                                    <div class="summary-label">إجمالي الأصناف</div>
                                  </div>
                                  <div class="summary-item">
                                    <div class="summary-value">${inventory.filter(item => item.current_quantity === 0).length}</div>
                                    <div class="summary-label">أصناف نفذت الكمية</div>
                                  </div>
                                  <div class="summary-item">
                                    <div class="summary-value">${inventory.filter(item => item.current_quantity > 0 && item.current_quantity <= item.minimum_quantity).length}</div>
                                    <div class="summary-label">أصناف تحت الحد الأدنى</div>
                                  </div>
                                  <div class="summary-item">
                                    <div class="summary-value">${formatCurrency(inventory.reduce((sum, item) => sum + (item.current_quantity * (item.avg_price || 0)), 0))}</div>
                                    <div class="summary-label">إجمالي قيمة المخزون</div>
                                  </div>
                                </div>

                                <table>
                                  <thead>
                                    <tr>
                                      <th>#</th>
                                      <th>الصنف</th>
                                      <th>وحدة القياس</th>
                                      <th>الكمية المتوفرة</th>
                                      <th>الحد الأدنى</th>
                                      <th>متوسط السعر</th>
                                      <th>سعر البيع</th>
                                      <th>القيمة</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    ${inventory.map((item, index) => {
                                      const value = item.current_quantity * (item.avg_price || 0);
                                      let quantityClass = '';
                                      if (item.current_quantity === 0) {
                                        quantityClass = 'text-danger';
                                      } else if (item.current_quantity <= item.minimum_quantity) {
                                        quantityClass = 'text-warning';
                                      }

                                      return `
                                        <tr>
                                          <td>${index + 1}</td>
                                          <td>${item.name}</td>
                                          <td>${item.unit || '-'}</td>
                                          <td class="${quantityClass}">${item.current_quantity}</td>
                                          <td>${item.minimum_quantity || 0}</td>
                                          <td>${formatCurrency(item.avg_price || 0)}</td>
                                          <td>${formatCurrency(item.selling_price || 0)}</td>
                                          <td>${formatCurrency(value)}</td>
                                        </tr>
                                      `;
                                    }).join('')}
                                  </tbody>
                                </table>
                              </body>
                              </html>
                            `);
                            printWindow.document.close();
                            printWindow.print();
                          }
                        }}
                      >
                        طباعة قائمة المخزون
                      </Button>
                      <Button
                        variant="secondary"
                        size="sm"
                        icon={<FaFileExport />}
                        iconPosition="left"
                        onClick={() => {
                          // تصدير قائمة المخزون
                          let csvContent = 'الرقم,الصنف,وحدة القياس,الكمية المتوفرة,الحد الأدنى,متوسط السعر,سعر البيع,القيمة\n';

                          inventory.forEach((item, index) => {
                            const value = item.current_quantity * (item.avg_price || 0);
                            csvContent += `${index + 1},${item.name},${item.unit || ''},${item.current_quantity},${item.minimum_quantity || 0},${item.avg_price || 0},${item.selling_price || 0},${value}\n`;
                          });

                          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                          const url = URL.createObjectURL(blob);
                          const link = document.createElement('a');
                          link.setAttribute('href', url);
                          link.setAttribute('download', 'قائمة_المخزون_الكاملة.csv');
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                      >
                        تصدير قائمة المخزون
                      </Button>
                    </div>
                  </div>

                  <DataTable
                    id="inventory-table"
                    columns={[
                      {
                        header: '#',
                        accessor: 'index',
                        cell: (_, index) => index + 1,
                        style: { width: '50px' }
                      },
                      {
                        header: 'الاسم',
                        accessor: 'name',
                        cell: (row) => row.name
                      },
                      {
                        header: 'وحدة القياس',
                        accessor: 'unit',
                        cell: (row) => row.unit || '-'
                      },
                      {
                        header: 'الكمية المتوفرة',
                        accessor: 'current_quantity',
                        cell: (row) => {
                          if (row.current_quantity === 0) {
                            return (
                              <span className="badge badge-danger">
                                نفذت الكمية
                              </span>
                            );
                          } else if (row.current_quantity <= row.minimum_quantity) {
                            return (
                              <span className="badge badge-warning">
                                <FaExclamationTriangle />
                                {row.current_quantity}
                              </span>
                            );
                          } else {
                            return row.current_quantity;
                          }
                        }
                      },
                      {
                        header: 'الحد الأدنى',
                        accessor: 'minimum_quantity',
                        cell: (row) => row.minimum_quantity || 0
                      },
                      {
                        header: 'متوسط السعر',
                        accessor: 'avg_price',
                        cell: (row) => row.avg_price ? <FormattedCurrency amount={row.avg_price} /> : '-'
                      },
                      {
                        header: 'سعر البيع',
                        accessor: 'selling_price',
                        cell: (row) => row.selling_price ? <FormattedCurrency amount={row.selling_price} /> : '-'
                      },
                      {
                        header: 'هامش الربح',
                        accessor: 'profit_margin',
                        cell: (row) => {
                          if (row.avg_price && row.selling_price) {
                            const margin = ((row.selling_price - row.avg_price) / row.avg_price) * 100;
                            return `${margin.toFixed(2)}%`;
                          }
                          return '-';
                        }
                      },
                      {
                        header: 'القيمة',
                        accessor: 'value',
                        cell: (row) => <FormattedCurrency amount={row.current_quantity * (row.avg_price || 0)} />
                      }
                    ]}
                    data={inventory}
                    pagination={true}
                    pageSize={10}
                    searchable={true}
                    searchPlaceholder="بحث عن صنف..."
                    emptyMessage="لا توجد بيانات للعرض"
                  />
                </div>

                {/* قسم تحليل المخزون */}
                <div id="inventory-analysis" className="detailed-report-section">
                  <h3>
                    <FaChartBar style={{ marginLeft: '8px' }} />
                    تحليل المخزون
                  </h3>

                  <div className="inventory-analysis-grid">
                    {/* تحليل توزيع قيمة المخزون */}
                    <div className="analysis-card">
                      <h4>توزيع قيمة المخزون</h4>
                      <div className="pie-chart-container">
                        <div className="pie-chart-placeholder">
                          {/* هنا يمكن إضافة رسم بياني دائري لتوزيع قيمة المخزون */}
                          <div className="pie-chart-segments">
                            {(() => {
                              // تقسيم الأصناف إلى فئات حسب القيمة
                              const highValueItems = inventory.filter(item => (item.current_quantity * (item.avg_price || 0)) > 1000);
                              const mediumValueItems = inventory.filter(item => {
                                const value = item.current_quantity * (item.avg_price || 0);
                                return value <= 1000 && value > 300;
                              });
                              const lowValueItems = inventory.filter(item => (item.current_quantity * (item.avg_price || 0)) <= 300);

                              // حساب إجمالي قيمة كل فئة
                              const highValue = highValueItems.reduce((sum, item) => sum + (item.current_quantity * (item.avg_price || 0)), 0);
                              const mediumValue = mediumValueItems.reduce((sum, item) => sum + (item.current_quantity * (item.avg_price || 0)), 0);
                              const lowValue = lowValueItems.reduce((sum, item) => sum + (item.current_quantity * (item.avg_price || 0)), 0);

                              // إجمالي قيمة المخزون
                              const totalValue = highValue + mediumValue + lowValue;

                              // حساب النسب المئوية
                              const highPercentage = totalValue > 0 ? (highValue / totalValue) * 100 : 0;
                              const mediumPercentage = totalValue > 0 ? (mediumValue / totalValue) * 100 : 0;
                              const lowPercentage = totalValue > 0 ? (lowValue / totalValue) * 100 : 0;

                              return (
                                <>
                                  <div
                                    className="pie-segment high-value"
                                    style={{
                                      transform: `rotate(0deg) skew(${90 - (highPercentage * 3.6)}deg)`,
                                      display: highPercentage > 0 ? 'block' : 'none'
                                    }}
                                  ></div>
                                  <div
                                    className="pie-segment medium-value"
                                    style={{
                                      transform: `rotate(${highPercentage * 3.6}deg) skew(${90 - (mediumPercentage * 3.6)}deg)`,
                                      display: mediumPercentage > 0 ? 'block' : 'none'
                                    }}
                                  ></div>
                                  <div
                                    className="pie-segment low-value"
                                    style={{
                                      transform: `rotate(${(highPercentage + mediumPercentage) * 3.6}deg) skew(${90 - (lowPercentage * 3.6)}deg)`,
                                      display: lowPercentage > 0 ? 'block' : 'none'
                                    }}
                                  ></div>
                                </>
                              );
                            })()}
                          </div>
                        </div>
                        <div className="pie-chart-legend">
                          <div className="legend-item">
                            <div className="legend-color high-value"></div>
                            <div className="legend-text">
                              <div className="legend-label">قيمة عالية (أكثر من 1000)</div>
                              <div className="legend-value">
                                <FormattedCurrency
                                  amount={inventory
                                    .filter(item => (item.current_quantity * (item.avg_price || 0)) > 1000)
                                    .reduce((sum, item) => sum + (item.current_quantity * (item.avg_price || 0)), 0)}
                                />
                              </div>
                            </div>
                          </div>
                          <div className="legend-item">
                            <div className="legend-color medium-value"></div>
                            <div className="legend-text">
                              <div className="legend-label">قيمة متوسطة (300 - 1000)</div>
                              <div className="legend-value">
                                <FormattedCurrency
                                  amount={inventory
                                    .filter(item => {
                                      const value = item.current_quantity * (item.avg_price || 0);
                                      return value <= 1000 && value > 300;
                                    })
                                    .reduce((sum, item) => sum + (item.current_quantity * (item.avg_price || 0)), 0)}
                                />
                              </div>
                            </div>
                          </div>
                          <div className="legend-item">
                            <div className="legend-color low-value"></div>
                            <div className="legend-text">
                              <div className="legend-label">قيمة منخفضة (أقل من 300)</div>
                              <div className="legend-value">
                                <FormattedCurrency
                                  amount={inventory
                                    .filter(item => (item.current_quantity * (item.avg_price || 0)) <= 300)
                                    .reduce((sum, item) => sum + (item.current_quantity * (item.avg_price || 0)), 0)}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* تحليل حالة المخزون */}
                    <div className="analysis-card">
                      <h4>حالة المخزون</h4>
                      <div className="inventory-status-chart">
                        <div className="status-bars">
                          {(() => {
                            // حساب عدد الأصناف في كل حالة
                            const outOfStock = inventory.filter(item => item.current_quantity === 0).length;
                            const lowStock = inventory.filter(item => item.current_quantity > 0 && item.current_quantity <= item.minimum_quantity).length;
                            const inStock = inventory.filter(item => item.current_quantity > item.minimum_quantity).length;

                            // إجمالي عدد الأصناف
                            const totalItems = inventory.length;

                            // حساب النسب المئوية
                            const outOfStockPercentage = totalItems > 0 ? (outOfStock / totalItems) * 100 : 0;
                            const lowStockPercentage = totalItems > 0 ? (lowStock / totalItems) * 100 : 0;
                            const inStockPercentage = totalItems > 0 ? (inStock / totalItems) * 100 : 0;

                            return (
                              <>
                                <div className="status-bar-group">
                                  <div className="status-label">نفذت الكمية</div>
                                  <div className="status-bar-container">
                                    <div
                                      className="status-bar out-of-stock"
                                      style={{ width: `${outOfStockPercentage}%` }}
                                    ></div>
                                    <div className="status-value">{outOfStock} ({outOfStockPercentage.toFixed(1)}%)</div>
                                  </div>
                                </div>
                                <div className="status-bar-group">
                                  <div className="status-label">تحت الحد الأدنى</div>
                                  <div className="status-bar-container">
                                    <div
                                      className="status-bar low-stock"
                                      style={{ width: `${lowStockPercentage}%` }}
                                    ></div>
                                    <div className="status-value">{lowStock} ({lowStockPercentage.toFixed(1)}%)</div>
                                  </div>
                                </div>
                                <div className="status-bar-group">
                                  <div className="status-label">متوفر في المخزون</div>
                                  <div className="status-bar-container">
                                    <div
                                      className="status-bar in-stock"
                                      style={{ width: `${inStockPercentage}%` }}
                                    ></div>
                                    <div className="status-value">{inStock} ({inStockPercentage.toFixed(1)}%)</div>
                                  </div>
                                </div>
                              </>
                            );
                          })()}
                        </div>
                      </div>
                    </div>

                    {/* تحليل هوامش الربح */}
                    <div className="analysis-card">
                      <h4>تحليل هوامش الربح</h4>
                      <div className="profit-margin-analysis">
                        {(() => {
                          // حساب هوامش الربح للأصناف
                          const itemsWithMargins = inventory
                            .filter(item => item.avg_price && item.selling_price)
                            .map(item => {
                              const margin = ((item.selling_price - item.avg_price) / item.avg_price) * 100;
                              return { ...item, margin };
                            });

                          // تقسيم الأصناف حسب هامش الربح
                          const highMarginItems = itemsWithMargins.filter(item => item.margin > 30);
                          const mediumMarginItems = itemsWithMargins.filter(item => item.margin <= 30 && item.margin > 15);
                          const lowMarginItems = itemsWithMargins.filter(item => item.margin <= 15);

                          // حساب متوسط هامش الربح
                          const avgMargin = itemsWithMargins.length > 0
                            ? itemsWithMargins.reduce((sum, item) => sum + item.margin, 0) / itemsWithMargins.length
                            : 0;

                          return (
                            <>
                              <div className="margin-summary">
                                <div className="margin-stat">
                                  <div className="margin-value">{avgMargin.toFixed(2)}%</div>
                                  <div className="margin-label">متوسط هامش الربح</div>
                                </div>
                                <div className="margin-stat">
                                  <div className="margin-value">{highMarginItems.length}</div>
                                  <div className="margin-label">أصناف بهامش ربح عالي</div>
                                </div>
                                <div className="margin-stat">
                                  <div className="margin-value">{mediumMarginItems.length}</div>
                                  <div className="margin-label">أصناف بهامش ربح متوسط</div>
                                </div>
                                <div className="margin-stat">
                                  <div className="margin-value">{lowMarginItems.length}</div>
                                  <div className="margin-label">أصناف بهامش ربح منخفض</div>
                                </div>
                              </div>

                              <div className="top-margin-items">
                                <h5>الأصناف ذات هامش الربح الأعلى</h5>
                                <div className="top-margin-list">
                                  {itemsWithMargins
                                    .sort((a, b) => b.margin - a.margin)
                                    .slice(0, 5)
                                    .map((item, index) => (
                                      <div key={index} className="top-margin-item">
                                        <div className="item-name">{item.name}</div>
                                        <div className="item-margin">{item.margin.toFixed(2)}%</div>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            </>
                          );
                        })()}
                      </div>
                    </div>

                    {/* توصيات المخزون */}
                    <div className="analysis-card">
                      <h4>توصيات المخزون</h4>
                      <div className="inventory-recommendations">
                        <div className="recommendation-list">
                          {(() => {
                            const recommendations = [];

                            // التحقق من الأصناف التي نفذت الكمية
                            const outOfStockItems = inventory.filter(item => item.current_quantity === 0);
                            if (outOfStockItems.length > 0) {
                              recommendations.push(
                                <div key="out-of-stock" className="recommendation-item urgent">
                                  <div className="recommendation-icon">
                                    <FaExclamationTriangle />
                                  </div>
                                  <div className="recommendation-content">
                                    <div className="recommendation-title">إعادة طلب الأصناف التي نفذت الكمية</div>
                                    <div className="recommendation-description">
                                      يوجد {outOfStockItems.length} صنف نفذت كميته من المخزون. يجب إعادة طلب هذه الأصناف في أقرب وقت.
                                    </div>
                                  </div>
                                </div>
                              );
                            }

                            // التحقق من الأصناف تحت الحد الأدنى
                            const lowStockItems = inventory.filter(item => item.current_quantity > 0 && item.current_quantity <= item.minimum_quantity);
                            if (lowStockItems.length > 0) {
                              recommendations.push(
                                <div key="low-stock" className="recommendation-item warning">
                                  <div className="recommendation-icon">
                                    <FaExclamationCircle />
                                  </div>
                                  <div className="recommendation-content">
                                    <div className="recommendation-title">مراقبة الأصناف تحت الحد الأدنى</div>
                                    <div className="recommendation-description">
                                      يوجد {lowStockItems.length} صنف تحت الحد الأدنى. يجب مراقبة هذه الأصناف وإعادة طلبها قبل نفاذ الكمية.
                                    </div>
                                  </div>
                                </div>
                              );
                            }

                            // التحقق من الأصناف ذات هامش الربح المنخفض
                            const lowMarginItems = inventory
                              .filter(item => item.avg_price && item.selling_price)
                              .filter(item => ((item.selling_price - item.avg_price) / item.avg_price) * 100 < 15);
                            if (lowMarginItems.length > 0) {
                              recommendations.push(
                                <div key="low-margin" className="recommendation-item info">
                                  <div className="recommendation-icon">
                                    <FaInfoCircle />
                                  </div>
                                  <div className="recommendation-content">
                                    <div className="recommendation-title">مراجعة أسعار البيع للأصناف ذات هامش الربح المنخفض</div>
                                    <div className="recommendation-description">
                                      يوجد {lowMarginItems.length} صنف بهامش ربح منخفض (أقل من 15%). يمكن مراجعة أسعار البيع لهذه الأصناف لتحسين الربحية.
                                    </div>
                                  </div>
                                </div>
                              );
                            }

                            // التحقق من الأصناف ذات القيمة العالية في المخزون
                            const highValueItems = inventory
                              .filter(item => (item.current_quantity * (item.avg_price || 0)) > 1000)
                              .filter(item => item.current_quantity > item.minimum_quantity * 2);
                            if (highValueItems.length > 0) {
                              recommendations.push(
                                <div key="high-value" className="recommendation-item info">
                                  <div className="recommendation-icon">
                                    <FaMoneyBillWave />
                                  </div>
                                  <div className="recommendation-content">
                                    <div className="recommendation-title">تقليل المخزون للأصناف ذات القيمة العالية</div>
                                    <div className="recommendation-description">
                                      يوجد {highValueItems.length} صنف ذو قيمة عالية وكمية كبيرة في المخزون. يمكن تقليل كمية الطلب المستقبلية لهذه الأصناف لتحسين السيولة.
                                    </div>
                                  </div>
                                </div>
                              );
                            }

                            // إذا لم توجد توصيات
                            if (recommendations.length === 0) {
                              recommendations.push(
                                <div key="no-recommendations" className="recommendation-item success">
                                  <div className="recommendation-icon">
                                    <FaCheckCircle />
                                  </div>
                                  <div className="recommendation-content">
                                    <div className="recommendation-title">المخزون في حالة جيدة</div>
                                    <div className="recommendation-description">
                                      لا توجد توصيات حالية للمخزون. استمر في المراقبة الدورية للمخزون.
                                    </div>
                                  </div>
                                </div>
                              );
                            }

                            return recommendations;
                          })()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* أزرار الطباعة والتصدير */}
                <div className="detailed-report-actions">
                  <Button
                    variant="primary"
                    icon={<FaPrint />}
                    iconPosition="left"
                    onClick={printReport}
                  >
                    طباعة التقرير الشامل
                  </Button>
                  <Button
                    variant="secondary"
                    icon={<FaFileExport />}
                    iconPosition="left"
                    onClick={() => exportToCSV()}
                  >
                    تصدير التقرير
                  </Button>
                </div>
              </>
            )}
          </div>
        )}

        {activeTab === 'customerInvoices' && (
          <div>
            <h2>
              <FaFileInvoice style={{ marginLeft: '10px', color: '#3498db' }} />
              تقرير فواتير العملاء المحسن
            </h2>

            <div className="report-header-section">
              <p className="report-subtitle">
                <FaCalendarAlt style={{ marginLeft: '5px' }} />
                {dateRange === 'quarter' ? 'الربع الحالي' :
                 dateRange === 'halfYear' ? 'النصف سنوي' :
                 dateRange === 'threeQuarters' ? 'ثلاثة أرباع السنة' :
                 dateRange === 'year' ? 'السنة الحالية' : 'جميع الفترات'}
                 - تاريخ التقرير: {formatDate(new Date().toISOString())}
              </p>
            </div>

            {/* اختيار العميل المحسن */}
            <div className="customer-selection-enhanced">
              <div className="card">
                <div className="card-header">
                  <h4>
                    <FaUser style={{ marginLeft: '8px' }} />
                    اختيار العميل
                  </h4>
                </div>
                <div className="card-body">
                  <div className="customer-selector-grid">
                    <div className="search-section">
                      <label className="customer-select-label">
                        <FaUser /> اختر العميل:
                      </label>
                      <select
                        className="filter-select customer-select enhanced-select"
                        value={selectedCustomer}
                        onChange={(e) => setSelectedCustomer(e.target.value)}
                      >
                        <option value="">-- اختر العميل --</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name}
                            {customer.customer_type === 'regular' && ' (دائم)'}
                            {customer.customer_type === 'sub' && ' (فرعي)'}
                          </option>
                        ))}
                      </select>
                    </div>

                    {selectedCustomer && (
                      <div className="selected-customer-info">
                        {(() => {
                          const customer = customers.find(c => c.id == selectedCustomer);
                          return customer ? (
                            <div className="customer-info-card">
                              <div className="customer-avatar">
                                <FaUser />
                              </div>
                              <div className="customer-details">
                                <h5>{customer.name}</h5>
                                <p>
                                  <span className={`customer-type-badge ${customer.customer_type}`}>
                                    {customer.customer_type === 'regular' ? 'دائم' :
                                     customer.customer_type === 'sub' ? 'فرعي' : 'عادي'}
                                  </span>
                                </p>
                                {customer.phone && <p><FaPhone /> {customer.phone}</p>}
                              </div>
                            </div>
                          ) : null;
                        })()}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {isLoadingSpecialReport ? (
              <div className="loading-indicator enhanced-loading">
                <div className="spinner-enhanced"></div>
                <p>جاري تحميل فواتير العميل...</p>
              </div>
            ) : (
              <>
                {selectedCustomer ? (
                  <>
                    {customerInvoices.length > 0 ? (
                      <>
                        {/* إحصائيات الفواتير المحسنة */}
                        <div className="enhanced-stats-section">
                          <h3>
                            <FaChartBar style={{ marginLeft: '8px' }} />
                            إحصائيات الفواتير
                          </h3>

                          {(() => {
                            // تصنيف الفواتير
                            const mainInvoices = customerInvoices.filter(invoice => {
                              const invoiceNumber = invoice.invoice_number || '';
                              const notes = invoice.notes || '';
                              return invoiceNumber.startsWith('H') ||
                                     notes.includes('رئيسية') ||
                                     notes.includes('مجمعة') ||
                                     (!invoiceNumber.startsWith('F') && !notes.includes('فرعية'));
                            });

                            const subInvoices = customerInvoices.filter(invoice => {
                              const invoiceNumber = invoice.invoice_number || '';
                              const notes = invoice.notes || '';
                              return invoiceNumber.startsWith('F') || notes.includes('فرعية');
                            });

                            const totalAmount = customerInvoices.reduce((sum, invoice) =>
                              sum + (invoice.total_amount || 0), 0);

                            return (
                              <div className="stats-grid enhanced">
                                <div className="stat-card total">
                                  <div className="stat-card-icon">
                                    <FaFileInvoice />
                                  </div>
                                  <div className="stat-card-content">
                                    <div className="stat-card-value">{customerInvoices.length}</div>
                                    <div className="stat-card-title">إجمالي الفواتير</div>
                                  </div>
                                </div>

                                <div className="stat-card main">
                                  <div className="stat-card-icon">
                                    <FaReceipt />
                                  </div>
                                  <div className="stat-card-content">
                                    <div className="stat-card-value">{mainInvoices.length}</div>
                                    <div className="stat-card-title">فواتير رئيسية</div>
                                  </div>
                                </div>

                                <div className="stat-card sub">
                                  <div className="stat-card-icon">
                                    <FaFileInvoice />
                                  </div>
                                  <div className="stat-card-content">
                                    <div className="stat-card-value">{subInvoices.length}</div>
                                    <div className="stat-card-title">فواتير فرعية</div>
                                  </div>
                                </div>

                                <div className="stat-card amount">
                                  <div className="stat-card-icon">
                                    <FaMoneyBillWave />
                                  </div>
                                  <div className="stat-card-content">
                                    <div className="stat-card-value">
                                      <FormattedCurrency amount={totalAmount} />
                                    </div>
                                    <div className="stat-card-title">إجمالي القيمة</div>
                                  </div>
                                </div>

                                <div className="stat-card date">
                                  <div className="stat-card-icon">
                                    <FaCalendarAlt />
                                  </div>
                                  <div className="stat-card-content">
                                    <div className="stat-card-value">
                                      {customerInvoices.length > 0 ?
                                        formatDate(customerInvoices[0].invoice_date) : '-'}
                                    </div>
                                    <div className="stat-card-title">تاريخ آخر فاتورة</div>
                                  </div>
                                </div>
                              </div>
                            );
                          })()}
                        </div>

                        {/* عرض الفواتير المصنفة */}
                        {(() => {
                          // تصنيف الفواتير
                          const mainInvoices = customerInvoices.filter(invoice => {
                            const invoiceNumber = invoice.invoice_number || '';
                            const notes = invoice.notes || '';
                            return invoiceNumber.startsWith('H') ||
                                   notes.includes('رئيسية') ||
                                   notes.includes('مجمعة') ||
                                   (!invoiceNumber.startsWith('F') && !notes.includes('فرعية'));
                          });

                          const subInvoices = customerInvoices.filter(invoice => {
                            const invoiceNumber = invoice.invoice_number || '';
                            const notes = invoice.notes || '';
                            return invoiceNumber.startsWith('F') || notes.includes('فرعية');
                          });

                          return (
                            <>
                              {/* الفواتير الرئيسية */}
                              {mainInvoices.length > 0 && (
                                <div className="main-invoices-section mb-4">
                                  <div className="section-header main">
                                    <h4 className="text-success">
                                      <FaReceipt style={{ marginLeft: '8px' }} />
                                      الفواتير الرئيسية ({mainInvoices.length})
                                    </h4>
                                  </div>

                                  <DataTable
                                    columns={[
                                      {
                                        header: '#',
                                        accessor: 'index',
                                        cell: (_, index) => index + 1,
                                        style: { width: '50px' }
                                      },
                                      {
                                        header: 'رقم الفاتورة',
                                        accessor: 'invoice_number',
                                        cell: (row) => (
                                          <span className="invoice-number main text-success">
                                            <FaReceipt style={{ marginLeft: '5px' }} />
                                            {row.invoice_number || `INV-${row.id.toString().padStart(6, '0')}`}
                                          </span>
                                        )
                                      },
                                      {
                                        header: 'التاريخ',
                                        accessor: 'invoice_date',
                                        cell: (row) => formatDate(row.invoice_date)
                                      },
                                      {
                                        header: 'عدد الأصناف',
                                        accessor: 'items_count',
                                        cell: (row) => row.items_count || (row.items ? row.items.length : 0)
                                      },
                                      {
                                        header: 'إجمالي القيمة',
                                        accessor: 'total_amount',
                                        cell: (row) => <FormattedCurrency amount={row.total_amount} />
                                      },
                                      {
                                        header: 'حالة الدفع',
                                        accessor: 'payment_status',
                                        cell: (row) => (
                                          <span className={row.payment_status === 'paid' ? 'badge badge-success' : 'badge badge-warning'}>
                                            {row.payment_status === 'paid' ? 'مدفوعة' : 'غير مدفوعة'}
                                          </span>
                                        )
                                      },
                                      {
                                        header: 'الإجراءات',
                                        accessor: 'actions',
                                        cell: (row) => (
                                          <button
                                            className="btn btn-sm btn-outline-success"
                                            onClick={() => printInvoice(row)}
                                            title="طباعة الفاتورة"
                                          >
                                            <FaPrint />
                                          </button>
                                        )
                                      }
                                    ]}
                                    data={mainInvoices}
                                    pagination={true}
                                    pageSize={5}
                                    searchable={true}
                                    searchPlaceholder="بحث في الفواتير الرئيسية..."
                                    emptyMessage="لا توجد فواتير رئيسية للعرض"
                                  />
                                </div>
                              )}

                              {/* الفواتير الفرعية */}
                              {subInvoices.length > 0 && (
                                <div className="sub-invoices-section mb-4">
                                  <div className="section-header sub">
                                    <h4 className="text-info">
                                      <FaFileInvoice style={{ marginLeft: '8px' }} />
                                      الفواتير الفرعية ({subInvoices.length})
                                    </h4>
                                  </div>

                                  <DataTable
                                    columns={[
                                      {
                                        header: '#',
                                        accessor: 'index',
                                        cell: (_, index) => index + 1,
                                        style: { width: '50px' }
                                      },
                                      {
                                        header: 'رقم الفاتورة',
                                        accessor: 'invoice_number',
                                        cell: (row) => (
                                          <span className="invoice-number sub text-info">
                                            <FaFileInvoice style={{ marginLeft: '5px' }} />
                                            {row.invoice_number || `SUB-${row.id.toString().padStart(6, '0')}`}
                                          </span>
                                        )
                                      },
                                      {
                                        header: 'التاريخ',
                                        accessor: 'invoice_date',
                                        cell: (row) => formatDate(row.invoice_date)
                                      },
                                      {
                                        header: 'عدد الأصناف',
                                        accessor: 'items_count',
                                        cell: (row) => row.items_count || (row.items ? row.items.length : 0)
                                      },
                                      {
                                        header: 'إجمالي القيمة',
                                        accessor: 'total_amount',
                                        cell: (row) => <FormattedCurrency amount={row.total_amount} />
                                      },
                                      {
                                        header: 'حالة الدفع',
                                        accessor: 'payment_status',
                                        cell: (row) => (
                                          <span className={row.payment_status === 'paid' ? 'badge badge-success' : 'badge badge-warning'}>
                                            {row.payment_status === 'paid' ? 'مدفوعة' : 'غير مدفوعة'}
                                          </span>
                                        )
                                      },
                                      {
                                        header: 'الإجراءات',
                                        accessor: 'actions',
                                        cell: (row) => (
                                          <button
                                            className="btn btn-sm btn-outline-info"
                                            onClick={() => printInvoice(row)}
                                            title="طباعة الفاتورة"
                                          >
                                            <FaPrint />
                                          </button>
                                        )
                                      }
                                    ]}
                                    data={subInvoices}
                                    pagination={true}
                                    pageSize={5}
                                    searchable={true}
                                    searchPlaceholder="بحث في الفواتير الفرعية..."
                                    emptyMessage="لا توجد فواتير فرعية للعرض"
                                  />
                                </div>
                              )}
                            </>
                          );
                        })()}

                        {/* عرض تفاصيل الفواتير */}
                        {customerInvoices.map((invoice) => (
                          <div key={invoice.id} className="invoice-details mt-4">
                            <div className="card">
                              <div className="card-header">
                                <h5>
                                  <FaFileInvoice className="ml-2" />
                                  فاتورة رقم: {invoice.invoice_number || `INV-${invoice.id.toString().padStart(6, '0')}`} - التاريخ: {formatDate(invoice.invoice_date)}
                                </h5>
                              </div>
                              <div className="card-body">
                                {invoice.items && invoice.items.length > 0 ? (
                                  <DataTable
                                    columns={[
                                      {
                                        header: '#',
                                        accessor: 'index',
                                        cell: (_, index) => index + 1,
                                        style: { width: '50px' }
                                      },
                                      {
                                        header: 'الصنف',
                                        accessor: 'item_name',
                                        cell: (row) => row.item_name
                                      },
                                      {
                                        header: 'الكمية',
                                        accessor: 'quantity',
                                        cell: (row) => row.quantity
                                      },
                                      {
                                        header: 'السعر',
                                        accessor: 'price',
                                        cell: (row) => <FormattedCurrency amount={row.price} />
                                      },
                                      {
                                        header: 'الإجمالي',
                                        accessor: 'total_price',
                                        cell: (row) => <FormattedCurrency amount={row.total_price} />
                                      }
                                    ]}
                                    data={invoice.items}
                                    pagination={false}
                                    searchable={false}
                                    emptyMessage="لا توجد أصناف في هذه الفاتورة"
                                  />
                                ) : (
                                  <p>لا توجد تفاصيل متاحة لهذه الفاتورة</p>
                                )}

                                <div className="invoice-summary mt-3">
                                  <div className="row">
                                    <div className="col-md-6">
                                      <p><strong>ملاحظات:</strong> {invoice.notes || 'لا توجد ملاحظات'}</p>
                                    </div>
                                    <div className="col-md-6 text-left">
                                      <p><strong>إجمالي الفاتورة:</strong> <FormattedCurrency amount={invoice.total_amount} /></p>
                                    </div>
                                  </div>
                                </div>

                                <div className="text-center mt-3">
                                  <button
                                    className="btn btn-primary"
                                    onClick={() => printInvoice(invoice)}
                                  >
                                    <FaPrint className="ml-1" />
                                    طباعة الفاتورة
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </>
                    ) : (
                      <div className="empty-state">
                        <FaFileInvoice size={48} />
                        <p>لا توجد فواتير لهذا العميل</p>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="empty-state">
                    <FaUser size={48} />
                    <p>يرجى اختيار عميل لعرض فواتيره</p>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </Card>
    </div>
  );
};

export default Reports;
