/**
 * إضافة حقول إلغاء المعاملات إلى قاعدة البيانات
 * يضيف هذا الملف الحقول اللازمة لتتبع إلغاء المعاملات وسجل التدقيق
 */

const { logError, logSystem } = require('./error-handler');
const DatabaseManager = require('./database-singleton');

/**
 * إضافة حقول إلغاء المعاملات
 */
function addTransactionCancellationFields() {
  try {
    console.log('بدء إضافة حقول إلغاء المعاملات...');
    logSystem('بدء إضافة حقول إلغاء المعاملات', 'info');

    // الحصول على اتصال قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    const db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    // التحقق من وجود الحقول الجديدة
    const tableInfo = db.prepare("PRAGMA table_info(transactions)").all();
    const existingColumns = tableInfo.map(col => col.name);

    console.log('الأعمدة الموجودة في جدول المعاملات:', existingColumns);

    // إضافة حقل status إذا لم يكن موجوداً
    if (!existingColumns.includes('status')) {
      console.log('إضافة حقل status...');
      db.prepare("ALTER TABLE transactions ADD COLUMN status TEXT DEFAULT 'active'").run();
      
      // تحديث جميع المعاملات الموجودة لتكون نشطة
      db.prepare("UPDATE transactions SET status = 'active' WHERE status IS NULL").run();
      
      console.log('تم إضافة حقل status بنجاح');
      logSystem('تم إضافة حقل status إلى جدول المعاملات', 'info');
    } else {
      console.log('حقل status موجود بالفعل');
    }

    // إضافة حقل cancelled_at إذا لم يكن موجوداً
    if (!existingColumns.includes('cancelled_at')) {
      console.log('إضافة حقل cancelled_at...');
      db.prepare("ALTER TABLE transactions ADD COLUMN cancelled_at TEXT").run();
      console.log('تم إضافة حقل cancelled_at بنجاح');
      logSystem('تم إضافة حقل cancelled_at إلى جدول المعاملات', 'info');
    } else {
      console.log('حقل cancelled_at موجود بالفعل');
    }

    // إضافة حقل cancelled_by إذا لم يكن موجوداً
    if (!existingColumns.includes('cancelled_by')) {
      console.log('إضافة حقل cancelled_by...');
      db.prepare("ALTER TABLE transactions ADD COLUMN cancelled_by INTEGER").run();
      console.log('تم إضافة حقل cancelled_by بنجاح');
      logSystem('تم إضافة حقل cancelled_by إلى جدول المعاملات', 'info');
    } else {
      console.log('حقل cancelled_by موجود بالفعل');
    }

    // إضافة حقل cancellation_reason إذا لم يكن موجوداً
    if (!existingColumns.includes('cancellation_reason')) {
      console.log('إضافة حقل cancellation_reason...');
      db.prepare("ALTER TABLE transactions ADD COLUMN cancellation_reason TEXT").run();
      console.log('تم إضافة حقل cancellation_reason بنجاح');
      logSystem('تم إضافة حقل cancellation_reason إلى جدول المعاملات', 'info');
    } else {
      console.log('حقل cancellation_reason موجود بالفعل');
    }

    // إنشاء جدول سجل التدقيق إذا لم يكن موجوداً
    const auditTableExists = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='transaction_audit_log'
    `).get();

    if (!auditTableExists) {
      console.log('إنشاء جدول سجل التدقيق...');
      
      db.prepare(`
        CREATE TABLE transaction_audit_log (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transaction_id INTEGER NOT NULL,
          action_type TEXT NOT NULL,
          old_status TEXT,
          new_status TEXT,
          reason TEXT,
          performed_by INTEGER NOT NULL,
          performed_at TEXT NOT NULL,
          additional_data TEXT,
          FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
          FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE SET NULL
        )
      `).run();

      console.log('تم إنشاء جدول سجل التدقيق بنجاح');
      logSystem('تم إنشاء جدول transaction_audit_log', 'info');
    } else {
      console.log('جدول سجل التدقيق موجود بالفعل');
    }

    // إنشاء الفهارس
    try {
      db.prepare("CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)").run();
      db.prepare("CREATE INDEX IF NOT EXISTS idx_transactions_cancelled_by ON transactions(cancelled_by)").run();
      db.prepare("CREATE INDEX IF NOT EXISTS idx_transaction_audit_log_transaction_id ON transaction_audit_log(transaction_id)").run();
      db.prepare("CREATE INDEX IF NOT EXISTS idx_transaction_audit_log_performed_by ON transaction_audit_log(performed_by)").run();
      db.prepare("CREATE INDEX IF NOT EXISTS idx_transaction_audit_log_performed_at ON transaction_audit_log(performed_at)").run();
      
      console.log('تم إنشاء الفهارس بنجاح');
      logSystem('تم إنشاء فهارس حقول إلغاء المعاملات', 'info');
    } catch (indexError) {
      console.warn('تحذير: فشل في إنشاء بعض الفهارس:', indexError);
      logSystem(`تحذير: فشل في إنشاء بعض الفهارس: ${indexError.message}`, 'warning');
    }

    return {
      success: true,
      message: 'تم إضافة حقول إلغاء المعاملات بنجاح'
    };

  } catch (error) {
    console.error('خطأ في إضافة حقول إلغاء المعاملات:', error);
    logError(error, 'addTransactionCancellationFields');

    return {
      success: false,
      message: `خطأ في إضافة حقول إلغاء المعاملات: ${error.message}`
    };
  }
}

// تصدير الوظيفة
module.exports = {
  addTransactionCancellationFields
};

// تشغيل الإضافة إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  console.log('🔧 تشغيل إضافة حقول إلغاء المعاملات...');
  
  const result = addTransactionCancellationFields();
  
  if (result.success) {
    console.log('✅ تم إضافة حقول إلغاء المعاملات بنجاح');
  } else {
    console.error('❌ فشل في إضافة حقول إلغاء المعاملات:', result.message);
  }
}
