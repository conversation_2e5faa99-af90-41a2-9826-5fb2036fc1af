@echo off
echo ========================================
echo بناء التطبيق باستخدام Webpack
echo ========================================
echo.

echo 🔧 بدء عملية البناء...
echo.

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo ❌ مجلد node_modules غير موجود
    echo يرجى تشغيل: npm install
    pause
    exit /b 1
)

REM التحقق من وجود webpack
if not exist "node_modules\.bin\webpack.cmd" (
    echo ❌ Webpack غير مثبت
    echo يرجى تشغيل: npm install webpack webpack-cli
    pause
    exit /b 1
)

echo ✅ تم العثور على Webpack
echo.

REM تشغيل webpack
echo 📦 تشغيل Webpack...
node_modules\.bin\webpack.cmd --mode production --progress

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo.
    echo الملفات المبنية:
    if exist "bundle.js" echo   ✅ bundle.js
    if exist "bundle.js.map" echo   ✅ bundle.js.map
    echo.
    echo يمكنك الآن:
    echo 1. تشغيل التطبيق: electron .
    echo 2. أو بناء المثبت: electron-builder
) else (
    echo.
    echo ❌ فشل في بناء التطبيق
    echo تحقق من الأخطاء أعلاه
)

echo.
pause
