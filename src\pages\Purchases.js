import React, { useState, useEffect, useRef } from 'react';
import {
  FaPlus,
  FaSearch,
  FaShoppingCart,
  FaBoxes,
  FaClipboardList,
  FaTimes,
  FaExclamationTriangle,
  FaHistory
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import { useAuth } from '../context/AuthContext';
import Card from '../components/Card';
import Button from '../components/Button';
import Modal from '../components/Modal';
import DataTable from '../components/DataTable';
import FormattedCurrency from '../components/FormattedCurrency';
import CancellationConfirmDialog from '../components/CancellationConfirmDialog';
import useInputHandler from '../hooks/useInputHandler';
import useFormReset from '../hooks/useFormReset';
import apiFallback from '../utils/api-fallback-enhanced';
import './Purchases.css';

const Purchases = () => {
  // التحقق من وجود window.api وإنشاؤه إذا لزم الأمر
  useEffect(() => {
    apiFallback.checkAndCreateAPI();

    // إنشاء مستخدم افتراضي إذا لم يكن موجود
    if (!localStorage.getItem('currentUserId')) {
      localStorage.setItem('currentUserId', 'default-user');
      localStorage.setItem('currentUserName', 'مستخدم النظام');
      localStorage.setItem('currentUserRole', 'employee');
      console.log('تم إنشاء مستخدم افتراضي للنظام');
    }
  }, []);

  // استخدام سياق التطبيق
  const { transactions, loading, addTransaction, updateItem } = useApp();
  const { currentUser } = useAuth();

  // حالة البحث والفلترة
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredItems, setFilteredItems] = useState([]);
  const [filteredPurchases, setFilteredPurchases] = useState([]);

  // إضافة حالة محلية لتخزين جميع الأصناف
  const [items, setItems] = useState([]);

  // حالة النافذة المنبثقة
  const [showModal, setShowModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // حالة النموذج باستخدام هوك معالجة الإدخال المحسن
  const {
    formData,
    handleInputChange,
    updateFormData,
    resetForm: resetFormData
  } = useInputHandler({
    quantity: 1,
    price: 0,
    selling_price: 0,
    minimum_quantity: 0,
    transport_cost: 0,
    invoice_number: '',
    transaction_date: new Date().toISOString().split('T')[0]
  });

  // حالة التنبيهات
  const [alert, setAlert] = useState({ show: false, type: '', message: '' });

  // حالة التحميل للزر
  const [isSubmitting, setIsSubmitting] = useState(false);

  // حالة إلغاء الفواتير
  const [showCancellationDialog, setShowCancellationDialog] = useState(false);
  const [transactionToCancel, setTransactionToCancel] = useState(null);
  const [isCancelling, setIsCancelling] = useState(false);
  const [showStatusFilter, setShowStatusFilter] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'active', 'cancelled'

  // مرجع للنموذج
  const formRef = useRef(null);

  // مرجع لمؤقت التنبيهات
  const alertTimeoutRef = useRef(null);

  // تنظيف المؤقتات عند إلغاء تحميل المكون
  useEffect(() => {
    return () => {
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
        alertTimeoutRef.current = null;
      }
    };
  }, []);

  // تحديث قائمة المشتريات عند تحميل الصفحة أو تغيير المعاملات
  useEffect(() => {
    console.log('تحديث قائمة المشتريات بعد تغيير المعاملات...');

    // الحصول على معاملات الشراء (استبعاد معاملات الكمية الابتدائية)
    const allPurchases = transactions.filter(t =>
      t.transaction_type === 'purchase' &&
      t.supplier !== 'كمية ابتدائية' &&
      t.notes !== 'إضافة كمية ابتدائية'
    );

    // تصفية المشتريات بناءً على رقم الفاتورة إذا كان هناك مصطلح بحث
    if (searchTerm && searchTerm.trim() !== '') {
      const filteredByInvoice = allPurchases.filter(purchase => {
        // التحقق من وجود رقم الفاتورة قبل البحث
        if (!purchase.invoice_number) return false;

        // البحث في رقم الفاتورة فقط
        return String(purchase.invoice_number).includes(searchTerm.trim());
      });

      console.log(`تم تصفية المشتريات بناءً على رقم الفاتورة "${searchTerm}"، النتائج: ${filteredByInvoice.length}`);
      setFilteredPurchases(filteredByInvoice);
    } else {
      // إذا لم يكن هناك مصطلح بحث، عرض جميع المشتريات
      setFilteredPurchases(allPurchases);
    }

    // تنظيف الدالة العالمية عند إلغاء تحميل المكون
    return () => {
      // لا شيء للتنظيف
    };
  }, [transactions, searchTerm]);

  // إضافة مستمع لحدث إضافة صنف جديد
  useEffect(() => {
    // دالة لتحديث قائمة الأصناف عند إضافة صنف جديد
    const handleItemAdded = async (data) => {
      try {
        console.log('تم استلام حدث إضافة صنف جديد في صفحة المشتريات:', data);

        // تحميل الأصناف المحدثة مباشرة (بدون مسح التخزين المؤقت)
        const updatedItems = await window.api.items.getAll(false);

        if (Array.isArray(updatedItems)) {
          console.log(`تم تحديث قائمة الأصناف في صفحة المشتريات بعد إضافة صنف جديد (${updatedItems.length} صنف)`);

          // تحديث قائمة الأصناف المحلية
          setItems(updatedItems);

          // تصفية الأصناف بناءً على مصطلح البحث
          filterItemsBySearchTerm(updatedItems, searchTerm);

          // عرض تنبيه للمستخدم
          showAlert('success', `تم إضافة الصنف "${data.name}" وهو متاح الآن للشراء`);
        }
      } catch (error) {
        console.error('خطأ في تحديث قائمة الأصناف بعد إضافة صنف جديد:', error);
      }
    };

    // إضافة مستمع لحدث إضافة صنف جديد
    window.addEventListener('item-added', (event) => handleItemAdded(event.detail));

    // تنظيف المستمعين عند إلغاء تحميل المكون
    return () => {
      window.removeEventListener('item-added', (event) => handleItemAdded(event.detail));
    };
  }, [searchTerm]);

  // تحميل الأصناف مباشرة من API مرة واحدة عند تحميل الصفحة
  // تم تحسين هذا الجزء لتجنب التحديثات المتكررة التي تسبب تجمد النظام:
  // 1. تحميل الأصناف مرة واحدة فقط عند تحميل الصفحة
  // 2. استخدام التخزين المؤقت بدلاً من مسحه في كل مرة
  // 3. تخزين الأصناف محلياً وتصفيتها بدلاً من إعادة تحميلها
  useEffect(() => {
    console.log('إعداد آلية تحميل الأصناف المباشر في صفحة المشتريات');

    // تعريف دالة تحميل الأصناف مباشرة من API
    const loadItemsDirectly = async (forceRefresh = false) => {
      try {
        console.log('تحميل الأصناف مباشرة من API في صفحة المشتريات...');

        // تحميل الأصناف المحدثة (استخدام التخزين المؤقت إذا كان متاحًا)
        const directItems = await window.api.items.getAll(forceRefresh);

        if (Array.isArray(directItems)) {
          console.log(`تم تحميل ${directItems.length} صنف مباشرة من API في صفحة المشتريات`);

          // تخزين جميع الأصناف في حالة محلية
          setItems(directItems);

          // تصفية الأصناف بناءً على مصطلح البحث
          filterItemsBySearchTerm(directItems, searchTerm);

          return directItems;
        } else {
          console.warn('لم يتم استلام مصفوفة صالحة من الأصناف من API');
          return null;
        }
      } catch (error) {
        console.error('خطأ في تحميل الأصناف مباشرة من API:', error);
        return null;
      }
    };

    // تنفيذ تحميل الأصناف مباشرة عند تحميل الصفحة (مرة واحدة فقط)
    loadItemsDirectly(false);

    // تنظيف عند إلغاء تحميل المكون
    return () => {
      // لا شيء للتنظيف
    };
  }, []); // تحميل مرة واحدة فقط عند تحميل المكون

  // دالة لتصفية الأصناف بناءً على مصطلح البحث
  // تم تحسين هذه الدالة لتصفية الأصناف محلياً بدلاً من إعادة تحميلها من قاعدة البيانات
  // هذا يقلل من استدعاءات API المتكررة التي تسبب تجمد النظام
  const filterItemsBySearchTerm = (itemsArray, term) => {
    if (!Array.isArray(itemsArray)) return;

    const filtered = itemsArray.filter(item => {
      // التحقق من وجود اسم الصنف قبل استدعاء toLowerCase
      if (!item || !item.name) return false;
      return item.name.toLowerCase().includes((term || '').toLowerCase());
    });

    console.log(`تم تصفية الأصناف، عدد الأصناف بعد التصفية: ${filtered.length}`);

    // تحديث قائمة الأصناف المفلترة
    setFilteredItems(filtered);
  };

  // تصفية الأصناف عند تغيير مصطلح البحث
  // هذا يضمن تحديث القائمة المفلترة عند تغيير مصطلح البحث أو تحديث قائمة الأصناف
  useEffect(() => {
    filterItemsBySearchTerm(items, searchTerm);
  }, [searchTerm, items]);

  // استخدام هوك useFormReset
  const { isMounted, enableFormFields } = useFormReset(formRef);



  // معالجة اختيار صنف
  const handleItemSelect = (item) => {
    console.log('Selected item:', item);

    // تسجيل معلومات تشخيصية للصنف
    console.log(`معلومات تفصيلية للصنف ${item.name}:`, {
      id: item.id || item._id,
      minimum_quantity: item.minimum_quantity,
      avg_price: item.avg_price,
      last_purchase_price: item.last_purchase_price,
      selling_price: item.selling_price,
      minimum_quantity_type: typeof item.minimum_quantity,
      avg_price_type: typeof item.avg_price,
      last_purchase_price_type: typeof item.last_purchase_price,
      selling_price_type: typeof item.selling_price
    });

    // التحقق من وجود معرف الصنف
    if (!item || (!item.id && !item._id)) {
      console.error('الصنف غير صالح أو لا يحتوي على معرف:', item);
      showAlert('danger', 'الصنف غير صالح، يرجى اختيار صنف آخر');
      return;
    }

    // تخزين الصنف المحدد مع التأكد من وجود معرف
    const selectedItemWithId = {
      ...item,
      id: item.id || item._id,
      _id: item._id || item.id
    };

    setSelectedItem(selectedItemWithId);

    // تحديث بيانات النموذج
    const minimumQuantity = typeof item.minimum_quantity === 'number' ? item.minimum_quantity : 0;
    console.log(`الحد الأدنى للصنف ${item.name}: ${minimumQuantity} (${typeof item.minimum_quantity})`);

    // تحديد السعر المستخدم وتسجيل معلومات توضيحية
    let priceToUse = 0;
    let priceSource = '';

    if (item.last_purchase_price && item.last_purchase_price > 0) {
      priceToUse = item.last_purchase_price;
      priceSource = 'آخر سعر شراء';
    } else if (item.avg_price && item.avg_price > 0) {
      priceToUse = item.avg_price;
      priceSource = 'متوسط السعر';
    } else {
      priceToUse = 0;
      priceSource = 'لا يوجد سعر محفوظ';
    }

    console.log(`سعر الشراء المستخدم للصنف ${item.name}: ${priceToUse} (${priceSource})`);

    const formDataToUpdate = {
      price: priceToUse,
      selling_price: item.selling_price || 0,
      minimum_quantity: minimumQuantity,
      transaction_date: new Date().toISOString().split('T')[0]
    };

    console.log('تحديث بيانات النموذج:', formDataToUpdate);
    updateFormData(formDataToUpdate);

    setShowModal(true);

    // إعادة تمكين حقول النموذج
    setTimeout(() => {
      enableFormFields();
    }, 100);
  };

  // معالجة إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Form submitted', formData);

    // التحقق من أن النموذج ليس قيد الإرسال بالفعل
    if (isSubmitting) {
      console.log('النموذج قيد الإرسال بالفعل، تجاهل النقرة المتكررة');
      return;
    }

    if (!selectedItem) {
      showAlert('danger', 'الرجاء اختيار صنف');
      return;
    }

    if (formData.quantity <= 0) {
      showAlert('danger', 'الرجاء إدخال كمية صحيحة');
      return;
    }

    if (formData.price <= 0) {
      showAlert('danger', 'الرجاء إدخال سعر شراء صحيح');
      return;
    }

    if (formData.selling_price <= 0) {
      showAlert('danger', 'الرجاء إدخال سعر بيع صحيح');
      return;
    }

    if (!formData.transaction_date) {
      showAlert('danger', 'الرجاء إدخال تاريخ العملية');
      return;
    }

    // إغلاق النافذة المنبثقة فوراً قبل بدء العملية
    // هذا سيمنع المستخدم من التفاعل مع النافذة أثناء المعالجة
    setShowModal(false);

    // تعيين حالة التحميل
    setIsSubmitting(true);

    try {
      // إظهار رسالة تحميل
      showAlert('info', 'جاري تسجيل عملية الشراء...');

      // حساب السعر الإجمالي
      const totalPrice = formData.quantity * formData.price;

      // إنشاء معاملة شراء جديدة
      const itemId = selectedItem._id || selectedItem.id;
      if (!itemId) {
        showAlert('danger', 'معرف الصنف غير موجود، يرجى اختيار صنف آخر');
        return;
      }

      // التأكد من أن الحد الأدنى قيمة صحيحة
      let minimumQuantityInput = formData.minimum_quantity;
      let minimumQuantity = 0;

      try {
        // تنظيف القيمة إذا كانت نصية
        if (typeof minimumQuantityInput === 'string') {
          minimumQuantityInput = minimumQuantityInput.trim();
        }

        // تحويل القيمة إلى رقم باستخدام Number بدلاً من parseInt
        minimumQuantity = Number(minimumQuantityInput);

        console.log(`الحد الأدنى المحدد للصنف ${selectedItem.name}:`);
        console.log(`- القيمة المدخلة: ${minimumQuantityInput} (${typeof minimumQuantityInput})`);
        console.log(`- القيمة بعد التحويل: ${minimumQuantity} (${typeof minimumQuantity})`);
        console.log(`- هل القيمة غير رقمية؟ ${isNaN(minimumQuantity)}`);
      } catch (error) {
        console.error('خطأ في معالجة قيمة الحد الأدنى:', error);
        minimumQuantity = 0;
      }

      // تأكيد أن الحد الأدنى ليس null أو undefined
      if (minimumQuantity === null || minimumQuantity === undefined) {
        console.error('الحد الأدنى لا يزال null أو undefined، سيتم استخدام 0 بدلاً منه');
        minimumQuantity = 0;
      }

      // استخدام القيمة الصحيحة للحد الأدنى
      const validMinimumQuantity = !isNaN(minimumQuantity) ? minimumQuantity : 0;
      console.log(`- القيمة النهائية للحد الأدنى: ${validMinimumQuantity} (${typeof validMinimumQuantity})`);

      // معالجة رقم الفاتورة بشكل آمن
      const invoiceNumber = formData.invoice_number || '';

      const newPurchase = {
        transaction_type: 'purchase',
        item_id: itemId, // استخدام _id (NeDB) أو id (للتوافق مع الإصدارات القديمة)
        item_name: selectedItem.name,
        quantity: Number(formData.quantity),
        price: Number(formData.price),
        selling_price: Number(formData.selling_price),
        minimum_quantity: validMinimumQuantity, // استخدام القيمة الصحيحة للحد الأدنى
        total_price: totalPrice,
        transport_cost: Number(formData.transport_cost) || 0,
        invoice_number: invoiceNumber, // إضافة رقم الفاتورة بعد معالجته
        transaction_date: formData.transaction_date
      };

      console.log('التحقق من معاملة الشراء قبل الإرسال:', {
        ...newPurchase,
        itemId_exists: !!itemId,
        selectedItem_id: selectedItem.id,
        selectedItem_id_type: typeof selectedItem.id,
        selectedItem_underscore_id: selectedItem._id,
        selectedItem_underscore_id_type: typeof selectedItem._id
      });

      console.log('New purchase transaction:', newPurchase);

      // تحديث الصنف لتحديث الحد الأدنى
      const itemToUpdate = {
        id: selectedItem._id || selectedItem.id,
        minimum_quantity: validMinimumQuantity, // استخدام القيمة الصحيحة للحد الأدنى
        name: selectedItem.name,
        unit: selectedItem.unit || 'قطعة',
        selling_price: Number(formData.selling_price)
      };

      console.log('معلومات تحديث الصنف:', {
        id: itemToUpdate.id,
        name: itemToUpdate.name,
        minimum_quantity: itemToUpdate.minimum_quantity,
        selling_price: itemToUpdate.selling_price
      });

      // استخدام الطريقة التسلسلية مع تحسينات
      try {
        // إضافة المعاملة أولاً
        console.log('جاري إضافة المعاملة...');
        const result = await addTransaction(newPurchase);

        // التحقق من نجاح العملية
        if (result && result.success === false) {
          throw new Error(result.error || 'فشل في إضافة المعاملة');
        }

        console.log('تم تسجيل عملية الشراء بنجاح:', result);

        // إعادة تعيين النموذج
        console.log('إعادة تعيين النموذج بعد إضافة المعاملة...');
        const resetData = {
          quantity: 1,
          price: 0,
          selling_price: 0,
          minimum_quantity: 0,
          transport_cost: 0,
          invoice_number: '',
          transaction_date: new Date().toISOString().split('T')[0]
        };
        console.log('بيانات إعادة التعيين:', resetData);
        resetFormData(resetData);

        // تم إغلاق النافذة المنبثقة بالفعل في بداية العملية
        console.log('تم إغلاق النافذة المنبثقة بنجاح في بداية العملية');

        // تأكيد إعادة تعيين العنصر المحدد
        setSelectedItem(null);

        // عرض رسالة نجاح
        showAlert('success', 'تم تسجيل عملية الشراء بنجاح');

        // التحقق من تحديث المخزون بعد عملية الشراء (بدون استدعاء API إضافي)
        console.log('تم تحديث المخزون بنجاح بعد عملية الشراء');


      } catch (error) {
        console.error('فشل في تسجيل عملية الشراء أو تحديث الصنف:', error);
        showAlert('danger', error.message || 'فشل في تسجيل عملية الشراء');
      } finally {
        // إعادة تعيين حالة التحميل
        if (isMounted.current) {
          setIsSubmitting(false);

          // النافذة المنبثقة تم إغلاقها بالفعل في بداية العملية
          console.log('النافذة المنبثقة تم إغلاقها بالفعل في بداية العملية');

          // تأكيد إعادة تعيين العنصر المحدد
          setSelectedItem(null);
        }
      }
    } catch (err) {
      console.error('Error adding purchase:', err);
      showAlert('danger', 'فشل في تسجيل عملية الشراء');

      // إعادة تعيين حالة التحميل
      if (isMounted.current) {
        setIsSubmitting(false);
      }
    }
  };

  // عرض تنبيه
  const showAlert = (type, message) => {
    if (isMounted.current) {
      setAlert({ show: true, type, message });

      // تنظيف أي مؤقت سابق
      if (alertTimeoutRef.current) {
        clearTimeout(alertTimeoutRef.current);
      }

      // إخفاء التنبيه بعد 3 ثوان
      alertTimeoutRef.current = setTimeout(() => {
        if (isMounted.current) {
          setAlert({ show: false, type: '', message: '' });
          alertTimeoutRef.current = null;
        }
      }, 3000);
    }
  };

  // تنسيق التاريخ (بالتنسيق الميلادي)
  const formatDate = (dateString) => {
    if (!dateString) return '-';

    try {
      const date = new Date(dateString);

      // التحقق من صحة التاريخ
      if (isNaN(date.getTime())) {
        console.error('تاريخ غير صالح:', dateString);
        return '-';
      }

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return `${year}/${month}/${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error('خطأ في تنسيق التاريخ:', error, dateString);
      return '-';
    }
  };

  // معالج إلغاء فاتورة الشراء
  const handleCancelPurchase = async (transaction) => {
    try {
      console.log('🔥 [DEBUG] بدء عملية إلغاء فاتورة الشراء:', transaction);
      console.log('🔥 [DEBUG] معرف الفاتورة:', transaction?.id);
      console.log('🔥 [DEBUG] حالة الفاتورة:', transaction?.status);

      // التحقق من وجود window.api
      if (!window.api) {
        console.error('❌ [DEBUG] window.api غير متوفر');
        showAlert('danger', 'خطأ في النظام: window.api غير متوفر');
        return;
      }

      // الحصول على معرف المستخدم الحالي أو استخدام قيمة افتراضية
      const userId = currentUser?.id || localStorage.getItem('currentUserId') || 'system';
      const userName = currentUser?.username || localStorage.getItem('currentUserName') || 'مستخدم النظام';

      console.log('🔥 [DEBUG] معرف المستخدم للإلغاء:', userId, 'اسم المستخدم:', userName);

      // التحقق من إمكانية الإلغاء أولاً
      console.log('🔥 [DEBUG] بدء التحقق من إمكانية الإلغاء...');
      const validation = await window.api.invoke('validate-purchase-cancellation', {
        transactionId: transaction.id,
        userId: userId
      });

      console.log('🔥 [DEBUG] نتيجة التحقق:', validation);

      if (!validation.success) {
        console.error('❌ [DEBUG] فشل التحقق:', validation.error);
        showAlert('danger', validation.error);
        return;
      }

      console.log('✅ [DEBUG] التحقق نجح، إظهار نافذة التأكيد...');
      // عرض حوار التأكيد
      setTransactionToCancel(transaction);
      setShowCancellationDialog(true);
    } catch (error) {
      console.error('❌ [DEBUG] خطأ في التحقق من إمكانية الإلغاء:', error);
      console.error('❌ [DEBUG] تفاصيل الخطأ:', error.stack);
      showAlert('danger', 'حدث خطأ أثناء التحقق من إمكانية الإلغاء');
    }
  };

  // تأكيد إلغاء فاتورة الشراء
  const confirmCancellation = async (reason) => {
    console.log('🔥 [DEBUG] بدء تأكيد الإلغاء...');
    console.log('🔥 [DEBUG] الفاتورة المراد إلغاؤها:', transactionToCancel);
    console.log('🔥 [DEBUG] سبب الإلغاء:', reason);

    if (!transactionToCancel) {
      console.error('❌ [DEBUG] لا توجد فاتورة محددة للإلغاء');
      return;
    }

    // الحصول على معرف المستخدم الحالي أو استخدام قيمة افتراضية
    const userId = currentUser?.id || localStorage.getItem('currentUserId') || 'system';
    const userName = currentUser?.username || localStorage.getItem('currentUserName') || 'مستخدم النظام';

    console.log('🔥 [DEBUG] معرف المستخدم:', userId, 'اسم المستخدم:', userName);

    setIsCancelling(true);

    try {
      console.log('🔥 [DEBUG] إرسال طلب الإلغاء إلى الخادم...');

      const result = await window.api.invoke('cancel-purchase-invoice', {
        transactionId: transactionToCancel.id,
        userId: userId,
        reason: reason
      });

      console.log('🔥 [DEBUG] نتيجة طلب الإلغاء:', result);

      if (result.success) {
        console.log('✅ [DEBUG] تم الإلغاء بنجاح');
        showAlert('success', 'تم إلغاء فاتورة الشراء بنجاح');

        // إغلاق حوار التأكيد
        setShowCancellationDialog(false);
        setTransactionToCancel(null);

        // تحديث قائمة المعاملات (سيتم تحديثها تلقائياً عبر السياق)
        // يمكن إضافة تحديث يدوي هنا إذا لزم الأمر
      } else {
        console.error('❌ [DEBUG] فشل الإلغاء:', result.error);
        showAlert('danger', result.error || 'فشل في إلغاء فاتورة الشراء');
      }
    } catch (error) {
      console.error('❌ [DEBUG] خطأ في إلغاء فاتورة الشراء:', error);
      console.error('❌ [DEBUG] تفاصيل الخطأ:', error.stack);
      showAlert('danger', 'حدث خطأ أثناء إلغاء فاتورة الشراء');
    } finally {
      setIsCancelling(false);
    }
  };

  // إغلاق حوار الإلغاء
  const closeCancellationDialog = () => {
    if (!isCancelling) {
      setShowCancellationDialog(false);
      setTransactionToCancel(null);
    }
  };

  // تصفية المعاملات حسب الحالة
  const getFilteredPurchasesByStatus = () => {
    if (statusFilter === 'all') {
      return filteredPurchases;
    }

    return filteredPurchases.filter(purchase => {
      if (statusFilter === 'active') {
        return !purchase.status || purchase.status === 'active';
      } else if (statusFilter === 'cancelled') {
        return purchase.status === 'cancelled';
      }
      return true;
    });
  };

  // الحصول على لون حالة المعاملة
  const getStatusColor = (status) => {
    switch (status) {
      case 'cancelled':
        return 'var(--danger-color, #dc3545)';
      case 'active':
      default:
        return 'var(--success-color, #28a745)';
    }
  };

  // الحصول على نص حالة المعاملة
  const getStatusText = (status) => {
    switch (status) {
      case 'cancelled':
        return 'ملغاة';
      case 'active':
      default:
        return 'نشطة';
    }
  };



  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
      </div>
    );
  }

  // التحقق من صلاحيات المستخدم
  const currentUserRole = localStorage.getItem('currentUserRole');
  const isViewer = currentUserRole === 'viewer';
  const isAdmin = currentUserRole === 'admin';

  // تحديد ما إذا كان يمكن للمستخدم إجراء عمليات الشراء
  const canMakePurchases = !isViewer && !isAdmin;

  return (
    <div className="purchases-page">
      <div className="purchases-header">
        <h1>المشتريات</h1>
        <p>إدارة عمليات شراء الأصناف وتسجيلها في المخزون</p>
      </div>

      {alert.show && (
        <div className={`alert alert-${alert.type}`}>
          {alert.message}
        </div>
      )}

      <div className="search-filters-container">
        <div className="search-container">
          <FaSearch className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="بحث برقم الفاتورة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* قائمة الأصناف */}
      <Card
        title="الأصناف المتاحة"
        icon={<FaBoxes />}
        className="items-table-container"
      >
        <DataTable
          columns={[
            {
              header: '#',
              accessor: 'index',
              cell: (_, index) => index + 1,
              style: { width: '50px' }
            },
            {
              header: 'الاسم',
              accessor: 'name',
              cell: (row) => (
                <div className="item-name">{row.name}</div>
              )
            },
            {
              header: 'وحدة القياس',
              accessor: 'unit',
              cell: (row) => (
                <div className="item-unit">{row.unit || '-'}</div>
              )
            },
            {
              header: 'متوسط السعر',
              accessor: 'avg_price',
              cell: (row) => (
                <div className="item-price">
                  {row.avg_price ? <FormattedCurrency amount={row.avg_price} /> : '-'}
                </div>
              ),
              hidden: true // إخفاء عمود متوسط السعر
            },
            {
              header: 'الحد الأدنى',
              accessor: 'minimum_quantity',
              cell: (row) => (
                <div className="item-min-quantity" title="الحد الأدنى للكمية المتوفرة في المخزون">
                  {row.minimum_quantity || 0}
                </div>
              ),
              hidden: true // إخفاء عمود الحد الأدنى
            },
            {
              header: 'الإجراءات',
              accessor: 'actions',
              cell: (row) => (
                <div className="item-actions">
                  {canMakePurchases ? (
                    <Button
                      variant="primary"
                      size="sm"
                      icon={<FaPlus />}
                      onClick={() => handleItemSelect(row)}
                    >
                      شراء
                    </Button>
                  ) : (
                    <span className="no-permission-text">
                      {isViewer ? 'مشاهدة فقط' : 'غير مسموح'}
                    </span>
                  )}
                </div>
              )
            }
          ]}
          data={filteredItems}
          pagination={true}
          pageSize={10}
          searchable={false}
          emptyMessage={
            searchTerm
              ? 'لا توجد نتائج للبحث'
              : 'لا توجد أصناف مسجلة'
          }
        />
      </Card>

      {/* آخر عمليات الشراء */}
      <Card
        title="آخر عمليات الشراء"
        icon={<FaShoppingCart />}
        className="purchases-table-container"
        actions={
          <div className="status-filter-controls">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="status-filter-select"
            >
              <option value="all">جميع الفواتير</option>
              <option value="active">الفواتير النشطة</option>
              <option value="cancelled">الفواتير الملغاة</option>
            </select>
          </div>
        }
      >
        <DataTable
          columns={[
            {
              header: 'رقم الفاتورة',
              accessor: 'invoice_number',
              cell: (row) => (
                <div className="purchase-invoice-number">
                  {row.invoice_number || '-'}
                </div>
              )
            },
            {
              header: 'التاريخ',
              accessor: 'transaction_date',
              cell: (row) => (
                <div className="purchase-date">
                  {formatDate(row.transaction_date)}
                </div>
              )
            },
            {
              header: 'الصنف',
              accessor: 'item_name',
              cell: (row) => (
                <div className="purchase-item">{row.item_name}</div>
              )
            },
            {
              header: 'الكمية',
              accessor: 'quantity',
              cell: (row) => (
                <div className="purchase-quantity">{row.quantity}</div>
              )
            },
            {
              header: 'سعر الوحدة',
              accessor: 'price',
              cell: (row) => (
                <div className="purchase-price">
                  <FormattedCurrency amount={row.price} />
                </div>
              )
            },
            {
              header: 'الإجمالي',
              accessor: 'total_price',
              cell: (row) => (
                <div className="purchase-total">
                  <FormattedCurrency amount={row.total_price} />
                </div>
              )
            },
            {
              header: 'الحد الأدنى',
              accessor: 'minimum_quantity',
              cell: (row) => (
                <div className="purchase-min-quantity" title="الحد الأدنى للكمية المتوفرة في المخزون">
                  {row.minimum_quantity || 0}
                </div>
              ),
              hidden: true // إخفاء عمود الحد الأدنى
            },
            {
              header: 'الحالة',
              accessor: 'status',
              cell: (row) => (
                <div className="purchase-status">
                  <span
                    className={`status-badge ${row.status === 'cancelled' ? 'cancelled' : 'active'}`}
                    style={{ color: getStatusColor(row.status) }}
                  >
                    {getStatusText(row.status)}
                  </span>
                </div>
              )
            },
            {
              header: 'الإجراءات',
              accessor: 'actions',
              cell: (row) => (
                <div className="purchase-actions">
                  {(!row.status || row.status === 'active') && canMakePurchases && (
                    <Button
                      variant="danger"
                      size="sm"
                      icon={<FaTimes />}
                      onClick={() => handleCancelPurchase(row)}
                      title="إلغاء فاتورة الشراء"
                      disabled={isCancelling}
                    >
                      إلغاء
                    </Button>
                  )}
                  {row.status === 'cancelled' && (
                    <Button
                      variant="light"
                      size="sm"
                      icon={<FaHistory />}
                      onClick={() => {
                        // يمكن إضافة عرض تفاصيل الإلغاء هنا
                        showAlert('info', `تم إلغاء هذه الفاتورة في ${formatDate(row.cancelled_at)}`);
                      }}
                      title="عرض تفاصيل الإلغاء"
                    >
                      تفاصيل
                    </Button>
                  )}
                </div>
              )
            }

          ]}
          data={getFilteredPurchasesByStatus()}
          pagination={true}
          pageSize={10}
          searchable={true}
          searchPlaceholder="بحث برقم الفاتورة..."
          searchFields={['invoice_number']}
          emptyMessage={'لا توجد عمليات شراء مسجلة'}
        />
      </Card>

      {/* نافذة إضافة عملية شراء */}
      {showModal && selectedItem && (
        <Modal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={`تسجيل عملية شراء - ${selectedItem.name}`}
          size="lg"
          footer={
            <>
              <Button
                variant="light"
                onClick={() => {
                  console.log('إلغاء عملية الشراء...');
                  // إغلاق النافذة المنبثقة فوراً
                  setShowModal(false);
                  setSelectedItem(null);
                }}
              >
                إلغاء
              </Button>
              <Button
                variant="primary"
                onClick={handleSubmit}
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'جاري التسجيل...' : 'تسجيل عملية الشراء'}
              </Button>
            </>
          }
        >
          <form ref={formRef} className="purchase-form" onSubmit={(e) => e.preventDefault()}>
            <div className="form-group">
              <label className="form-label">الكمية <span className="required">*</span></label>
              <input
                type="number"
                name="quantity"
                className="form-control"
                value={formData.quantity}
                onChange={handleInputChange}
                min="1"
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label">سعر الشراء <span className="required">*</span></label>
              <input
                type="number"
                name="price"
                className="form-control"
                value={formData.price}
                onChange={handleInputChange}
                min="0.01"
                step="0.01"
                required
              />
              {selectedItem && (
                <small className="form-text text-muted">
                  {selectedItem.last_purchase_price && selectedItem.last_purchase_price > 0 ? (
                    <>
                      آخر سعر شراء: <FormattedCurrency amount={selectedItem.last_purchase_price} />
                      {selectedItem.avg_price && selectedItem.avg_price > 0 && (
                        <> | متوسط السعر: <FormattedCurrency amount={selectedItem.avg_price} /></>
                      )}
                    </>
                  ) : selectedItem.avg_price && selectedItem.avg_price > 0 ? (
                    <>متوسط السعر: <FormattedCurrency amount={selectedItem.avg_price} /></>
                  ) : (
                    'لا يوجد سعر محفوظ مسبقاً'
                  )}
                </small>
              )}
            </div>


            <div className="form-group">
              <label className="form-label">سعر البيع <span className="required">*</span></label>
              <input
                type="number"
                name="selling_price"
                className="form-control"
                value={formData.selling_price}
                onChange={handleInputChange}
                min="0.01"
                step="0.01"
                required
              />
            </div>

            <div className="form-group">
              <label className="form-label">مصاريف النقل</label>
              <input
                type="number"
                name="transport_cost"
                className="form-control"
                value={formData.transport_cost}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                placeholder="0.00"
              />
              <small className="form-text">مصاريف النقل والشحن (اختياري)</small>
            </div>

            <div className="form-group">
              <label className="form-label">رقم الفاتورة</label>
              <input
                type="text"
                name="invoice_number"
                className="form-control"
                value={formData.invoice_number}
                onChange={handleInputChange}
                placeholder="أدخل رقم الفاتورة (اختياري)"
              />
              <small className="form-text">رقم الفاتورة الخاص بالمورد (اختياري)</small>
            </div>


            <div className="form-group">
              <label className="form-label">الحد الأدنى</label>
              <input
                type="number"
                name="minimum_quantity"
                className="form-control"
                value={formData.minimum_quantity}
                onChange={handleInputChange}
                min="0"
                step="1"
                onBlur={(e) => {
                  try {
                    // تنظيف القيمة عند فقدان التركيز
                    const inputValue = e.target.value;
                    // تحويل القيمة إلى رقم
                    const numValue = Number(String(inputValue).trim());

                    if (isNaN(numValue)) {
                      // إذا كانت القيمة غير صالحة، استخدم 0
                      updateFormData({ minimum_quantity: 0 });
                    } else {
                      // تأكد من أن القيمة رقم صحيح غير سالب
                      updateFormData({ minimum_quantity: Math.max(0, Math.floor(numValue)) });
                    }
                  } catch (error) {
                    console.error('خطأ في معالجة قيمة الحد الأدنى:', error);
                    updateFormData({ minimum_quantity: 0 });
                  }
                }}
              />
              <small className="form-text">الحد الأدنى للكمية المتوفرة في المخزون</small>
            </div>

            <div className="form-group full-width">
              <label className="form-label">الإجمالي</label>
              <div className="total-price-display">
                <div className="total-breakdown">
                  <div className="total-item">
                    <span>قيمة المشتريات:</span>
                    <FormattedCurrency amount={formData.quantity * formData.price} />
                  </div>
                  {formData.transport_cost > 0 && (
                    <div className="total-item">
                      <span>مصاريف النقل:</span>
                      <FormattedCurrency amount={formData.transport_cost} />
                    </div>
                  )}
                  <div className="total-item total-final">
                    <span>الإجمالي النهائي:</span>
                    <FormattedCurrency amount={(formData.quantity * formData.price) + (Number(formData.transport_cost) || 0)} />
                  </div>
                </div>
              </div>
            </div>

            <div className="form-group">
              <label className="form-label">التاريخ <span className="required">*</span></label>
              <input
                type="date"
                name="transaction_date"
                className="form-control"
                value={formData.transaction_date}
                onChange={handleInputChange}
                required
              />
            </div>
          </form>
        </Modal>
      )}

      {/* حوار تأكيد إلغاء فاتورة الشراء */}
      <CancellationConfirmDialog
        isOpen={showCancellationDialog}
        onClose={closeCancellationDialog}
        onConfirm={confirmCancellation}
        transaction={transactionToCancel}
        loading={isCancelling}
        title="تأكيد إلغاء فاتورة الشراء"
        message="هل أنت متأكد من رغبتك في إلغاء هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء."
      />
    </div>
  );
};

export default Purchases;
