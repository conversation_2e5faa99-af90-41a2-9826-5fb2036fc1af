@echo off
echo.
echo ========================================
echo Closing Electron and Node processes...
echo ========================================
echo.

echo Killing Electron processes...
taskkill /f /im electron.exe 2>nul
taskkill /f /im "نظام إدارة المخازن.exe" 2>nul
taskkill /f /im "warehouse-management-system.exe" 2>nul

echo.
echo Killing Node.js processes...
taskkill /f /im node.exe 2>nul

echo.
echo Killing app-builder processes...
taskkill /f /im app-builder.exe 2>nul

echo.
echo Waiting for processes to close...
timeout /t 3 /nobreak >nul

echo.
echo Cleaning dist folder...
if exist "dist" (
    rmdir /s /q "dist" 2>nul
    echo Done cleaning dist folder
)

echo.
echo All processes closed and files cleaned!
echo You can now run the build command again.
echo.
pause
