@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🏗️  بناء نظام إدارة المخازن
echo ========================================
echo.

echo 📋 التحقق من جاهزية البناء...
call npm run test-build
if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في اختبار الجاهزية!
    echo يرجى إصلاح المشاكل المذكورة أعلاه قبل المتابعة.
    pause
    exit /b 1
)

echo.
echo ✅ الاختبارات نجحت! بدء عملية البناء...
echo.

echo 🚀 تشغيل سكريبت البناء الشامل...
call npm run build-installer
if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في عملية البناء!
    pause
    exit /b 1
)

echo.
echo 🎉 تم بناء التطبيق بنجاح!
echo.
echo 📁 ستجد الملفات التنفيذية في مجلد: dist\
echo.

if exist "dist\" (
    echo 📋 الملفات المُنشأة:
    dir /b "dist\*.exe" 2>nul
    echo.
    echo ✨ يمكنك الآن توزيع هذه الملفات على أجهزة العملاء!
    echo.
    echo 📖 للمزيد من المعلومات:
    echo    - راجع INSTALLER_README.md
    echo    - راجع DEPLOYMENT_GUIDE.md
) else (
    echo ❌ لم يتم العثور على مجلد dist
)

echo.
pause
