@echo off
echo ========================================
echo تطبيق ترقية قاعدة البيانات لميزة إلغاء الفواتير
echo ========================================
echo.

REM البحث عن قاعدة البيانات
set DB_PATH=""
if exist "wms-database.db" set DB_PATH="wms-database.db"
if exist "wms-database\warehouse.db" set DB_PATH="wms-database\warehouse.db"
if exist "warehouse.db" set DB_PATH="warehouse.db"

if %DB_PATH%=="" (
    echo ❌ لم يتم العثور على قاعدة البيانات
    echo يرجى التأكد من وجود أحد الملفات التالية:
    echo   - wms-database.db
    echo   - wms-database\warehouse.db  
    echo   - warehouse.db
    pause
    exit /b 1
)

echo ✅ تم العثور على قاعدة البيانات: %DB_PATH%
echo.

REM التحقق من وجود SQLite
where sqlite3 >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ SQLite غير مثبت في النظام
    echo.
    echo يرجى تطبيق الترقية يدوياً:
    echo 1. حمل SQLite Browser من: https://sqlitebrowser.org/
    echo 2. افتح قاعدة البيانات: %DB_PATH%
    echo 3. نفذ محتويات ملف: manual-db-migration.sql
    echo 4. احفظ التغييرات
    echo.
    echo أو استخدم أي أداة SQLite أخرى لتنفيذ الأوامر التالية:
    echo.
    type manual-db-migration.sql
    pause
    exit /b 1
)

echo 🔧 تطبيق ترقية قاعدة البيانات...
echo.

REM تطبيق الترقية
sqlite3 %DB_PATH% < manual-db-migration.sql

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم تطبيق ترقية قاعدة البيانات بنجاح!
    echo.
    echo الآن يمكنك:
    echo 1. تشغيل التطبيق
    echo 2. الانتقال لصفحة المشتريات
    echo 3. اختبار ميزة إلغاء الفواتير
) else (
    echo ❌ فشل في تطبيق الترقية
    echo يرجى تطبيقها يدوياً باستخدام SQLite Browser
)

echo.
pause
