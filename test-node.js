console.log('✅ Node.js يعمل بشكل صحيح!');
console.log('إصدار Node.js:', process.version);
console.log('المجلد الحالي:', process.cwd());

// اختبار وجود الملفات المطلوبة
const fs = require('fs');

console.log('\n🔍 فحص الملفات المطلوبة:');

const requiredFiles = [
    'package.json',
    'webpack.config.js',
    'src/index.js',
    'simple-db-migration.js'
];

requiredFiles.forEach(file => {
    const exists = fs.existsSync(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
});

console.log('\n📦 فحص node_modules:');
const nodeModulesExists = fs.existsSync('node_modules');
console.log(`  ${nodeModulesExists ? '✅' : '❌'} node_modules`);

if (nodeModulesExists) {
    const webpackExists = fs.existsSync('node_modules/webpack');
    console.log(`  ${webpackExists ? '✅' : '❌'} webpack`);
}

console.log('\n🎯 الخطوات التالية:');
if (!nodeModulesExists) {
    console.log('  1. تشغيل: npm install');
}
console.log('  2. تطبيق ترقية قاعدة البيانات: node simple-db-migration.js');
console.log('  3. بناء التطبيق: npx webpack --mode production');
console.log('  4. تشغيل التطبيق: npm start');

process.exit(0);
