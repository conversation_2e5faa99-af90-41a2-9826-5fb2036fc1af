/**
 * اختبار سريع لإصلاح الخلل البصري في عرض إجمالي الأرباح
 * 
 * هذا السكريبت يقوم باختبار سريع للتأكد من أن الواجهة تعرض القيم الصحيحة
 */

console.log('⚡ اختبار سريع لإصلاح الخلل البصري في عرض إجمالي الأرباح...');

// التحقق من وجود window.api
if (typeof window !== 'undefined' && window.api) {
  
  // اختبار سريع
  async function quickUIDisplayTest() {
    try {
      console.log('🚀 بدء الاختبار السريع...');
      
      // 1. تعيين رصيد ابتدائي
      console.log('\n1️⃣ تعيين رصيد ابتدائي (5000)...');
      const initialResult = await window.api.cashbox.updateInitialBalance(5000);
      if (initialResult.success) {
        console.log(`✅ الرصيد الابتدائي: ${initialResult.cashbox.initial_balance}`);
        console.log(`✅ إجمالي الأرباح: ${initialResult.cashbox.profit_total}`);
      } else {
        console.error('❌ فشل في تعيين الرصيد الابتدائي');
        return;
      }
      
      // 2. عملية بيع لإنشاء أرباح
      console.log('\n2️⃣ عملية بيع لإنشاء أرباح (1000)...');
      const saleResult = await window.api.cashbox.addTransaction({
        type: 'sale',
        amount: 1000,
        source: 'test',
        notes: 'بيع لإنشاء أرباح'
      });
      
      if (saleResult.success) {
        console.log(`📊 إجمالي الأرباح بعد البيع: ${saleResult.cashbox.profit_total}`);
        console.log(`📊 متوقع: 1000, فعلي: ${saleResult.cashbox.profit_total} ${saleResult.cashbox.profit_total === 1000 ? '✅' : '❌'}`);
      } else {
        console.error('❌ فشل في عملية البيع');
        return;
      }
      
      // انتظار قصير
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 3. عملية شراء أولى
      console.log('\n3️⃣ عملية شراء أولى (400)...');
      const beforeFirstPurchase = await window.api.cashbox.getCashbox();
      console.log(`📊 الأرباح قبل الشراء الأول: ${beforeFirstPurchase.profit_total}`);
      
      const firstPurchaseResult = await window.api.cashbox.addTransaction({
        type: 'purchase',
        amount: 400,
        source: 'test',
        notes: 'شراء أول'
      });
      
      if (firstPurchaseResult.success) {
        console.log(`📊 الأرباح بعد الشراء الأول: ${firstPurchaseResult.cashbox.profit_total}`);
        const expectedProfit1 = 1000 - 400; // 600
        console.log(`📊 متوقع: ${expectedProfit1}, فعلي: ${firstPurchaseResult.cashbox.profit_total} ${firstPurchaseResult.cashbox.profit_total === expectedProfit1 ? '✅' : '❌'}`);
        
        if (firstPurchaseResult.cashbox.profit_total === expectedProfit1) {
          console.log('✅ الأرباح تعرض بشكل صحيح بعد الشراء الأول');
        } else {
          console.error('❌ خلل في عرض الأرباح بعد الشراء الأول!');
        }
      } else {
        console.error('❌ فشل في عملية الشراء الأولى');
        return;
      }
      
      // انتظار قصير
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 4. عملية شراء ثانية متتالية (هنا كان يحدث الخلل)
      console.log('\n4️⃣ عملية شراء ثانية متتالية (300) - اختبار الخلل البصري...');
      const beforeSecondPurchase = await window.api.cashbox.getCashbox();
      console.log(`📊 الأرباح قبل الشراء الثاني: ${beforeSecondPurchase.profit_total}`);
      
      const secondPurchaseResult = await window.api.cashbox.addTransaction({
        type: 'purchase',
        amount: 300,
        source: 'test',
        notes: 'شراء ثاني متتالي'
      });
      
      if (secondPurchaseResult.success) {
        console.log(`📊 الأرباح بعد الشراء الثاني: ${secondPurchaseResult.cashbox.profit_total}`);
        const expectedProfit2 = 1000 - 400 - 300; // 300
        console.log(`📊 متوقع: ${expectedProfit2}, فعلي: ${secondPurchaseResult.cashbox.profit_total} ${secondPurchaseResult.cashbox.profit_total === expectedProfit2 ? '✅' : '❌'}`);
        
        if (secondPurchaseResult.cashbox.profit_total === expectedProfit2) {
          console.log('✅ تم إصلاح الخلل البصري! الأرباح تعرض بشكل صحيح بعد الشراء الثاني');
        } else {
          console.error('❌ الخلل البصري لا يزال موجود!');
        }
      } else {
        console.error('❌ فشل في عملية الشراء الثانية');
        return;
      }
      
      // انتظار قصير
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 5. عملية بيع للتأكد من عمل النظام
      console.log('\n5️⃣ عملية بيع للتأكد من عمل النظام (800)...');
      const finalSaleResult = await window.api.cashbox.addTransaction({
        type: 'sale',
        amount: 800,
        source: 'test',
        notes: 'بيع نهائي'
      });
      
      if (finalSaleResult.success) {
        console.log(`📊 الأرباح بعد البيع النهائي: ${finalSaleResult.cashbox.profit_total}`);
        const expectedFinalProfit = (1000 + 800) - (400 + 300); // 1100
        console.log(`📊 متوقع: ${expectedFinalProfit}, فعلي: ${finalSaleResult.cashbox.profit_total} ${finalSaleResult.cashbox.profit_total === expectedFinalProfit ? '✅' : '❌'}`);
      } else {
        console.error('❌ فشل في عملية البيع النهائية');
        return;
      }
      
      // 6. النتيجة النهائية
      console.log('\n📊 النتيجة النهائية:');
      const finalCashbox = await window.api.cashbox.getCashbox();
      console.log(`الرصيد الابتدائي: ${finalCashbox.initial_balance}`);
      console.log(`الرصيد الحالي: ${finalCashbox.current_balance}`);
      console.log(`إجمالي المبيعات: ${finalCashbox.sales_total}`);
      console.log(`إجمالي المشتريات: ${finalCashbox.purchases_total}`);
      console.log(`إجمالي الأرباح: ${finalCashbox.profit_total}`);
      
      // التحقق من صحة الحسابات النهائية
      const expectedValues = {
        sales_total: 1800, // 1000 + 800
        purchases_total: 700, // 400 + 300
        profit_total: 1100 // 1800 - 700
      };
      
      let allCorrect = true;
      for (const [key, expectedValue] of Object.entries(expectedValues)) {
        const actualValue = finalCashbox[key];
        if (actualValue !== expectedValue) {
          allCorrect = false;
          console.error(`❌ خطأ في ${key}! المتوقع: ${expectedValue}، الفعلي: ${actualValue}`);
        }
      }
      
      if (allCorrect) {
        console.log('\n🎉 الاختبار السريع نجح! تم إصلاح الخلل البصري في عرض الأرباح');
        console.log('✅ الواجهة تعرض القيم الصحيحة فوراً أثناء عمليات الشراء المتتالية');
        console.log('✅ لا يوجد خلل بصري في عرض إجمالي الأرباح');
      } else {
        console.log('\n⚠️ هناك مشكلة في النظام. يرجى مراجعة الإصلاحات');
      }
      
    } catch (error) {
      console.error('❌ خطأ في الاختبار السريع:', error);
    }
  }
  
  // تشغيل الاختبار
  quickUIDisplayTest();
  
} else {
  console.error('❌ window.api غير متوفر');
  console.log('💡 يجب تشغيل هذا السكريبت من داخل التطبيق');
  console.log('');
  console.log('📋 لتشغيل الاختبار السريع:');
  console.log('1. افتح التطبيق');
  console.log('2. افتح وحدة التحكم (F12)');
  console.log('3. انسخ والصق الكود التالي:');
  console.log('');
  console.log('// اختبار سريع للخلل البصري');
  console.log('(async () => {');
  console.log('  const initial = await window.api.cashbox.updateInitialBalance(5000);');
  console.log('  console.log("رصيد ابتدائي:", initial.cashbox.profit_total);');
  console.log('  ');
  console.log('  const sale = await window.api.cashbox.addTransaction({type: "sale", amount: 1000, source: "test"});');
  console.log('  console.log("بعد البيع:", sale.cashbox.profit_total, "متوقع: 1000");');
  console.log('  ');
  console.log('  const purchase1 = await window.api.cashbox.addTransaction({type: "purchase", amount: 400, source: "test"});');
  console.log('  console.log("بعد الشراء الأول:", purchase1.cashbox.profit_total, "متوقع: 600");');
  console.log('  ');
  console.log('  const purchase2 = await window.api.cashbox.addTransaction({type: "purchase", amount: 300, source: "test"});');
  console.log('  console.log("بعد الشراء الثاني:", purchase2.cashbox.profit_total, "متوقع: 300");');
  console.log('  console.log("الخلل البصري محلول:", purchase2.cashbox.profit_total === 300 ? "✅" : "❌");');
  console.log('})();');
}
