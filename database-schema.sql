-- نموذج قاعدة البيانات الموحد لنظام إدارة المخزون
-- هذا الملف يحتوي على هيكل قاعدة البيانات المحسن مع العلاقات المناسبة

-- تمكين المفاتيح الأجنبية
PRAGMA foreign_keys = ON;

-- جدول الأصناف (يحتوي فقط على اسم الصنف ووحدة القياس)
CREATE TABLE IF NOT EXISTS items_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    unit TEXT NOT NULL DEFAULT 'قطعة',
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- جدول المخزون (يحتوي على معلومات المخزون للأصناف)
CREATE TABLE IF NOT EXISTS inventory_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    current_quantity INTEGER NOT NULL DEFAULT 0,
    minimum_quantity INTEGER NOT NULL DEFAULT 0,
    avg_price REAL NOT NULL DEFAULT 0,
    selling_price REAL NOT NULL DEFAULT 0,
    last_updated TEXT NOT NULL,
    FOREIGN KEY (item_id) REFERENCES items_new(id) ON DELETE CASCADE
);

-- جدول العملاء
CREATE TABLE IF NOT EXISTS customers_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    contact_person TEXT,
    phone TEXT,
    email TEXT,
    address TEXT,
    customer_type TEXT DEFAULT 'normal',
    parent_id INTEGER,
    credit_limit REAL DEFAULT 0,
    balance REAL DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (parent_id) REFERENCES customers_new(id) ON DELETE SET NULL
);

-- جدول المعاملات (يحتوي على جميع معاملات الشراء والبيع والاستلام والصرف)
CREATE TABLE IF NOT EXISTS transactions_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_id TEXT NOT NULL UNIQUE,
    item_id INTEGER NOT NULL,
    transaction_type TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    price REAL NOT NULL DEFAULT 0,
    selling_price REAL NOT NULL DEFAULT 0,
    total_price REAL NOT NULL DEFAULT 0,
    profit REAL NOT NULL DEFAULT 0,
    customer_id INTEGER,
    invoice_number TEXT,
    notes TEXT,
    user_id INTEGER,
    transaction_date TEXT NOT NULL,
    skip_inventory_update INTEGER DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'active',
    cancelled_at TEXT,
    cancelled_by INTEGER,
    cancellation_reason TEXT,
    FOREIGN KEY (item_id) REFERENCES items_new(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers_new(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (cancelled_by) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول سجل مبيعات العملاء
CREATE TABLE IF NOT EXISTS customer_sales_history_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    transaction_id INTEGER,
    invoice_number TEXT,
    total_amount REAL NOT NULL DEFAULT 0,
    total_profit REAL NOT NULL DEFAULT 0,
    transaction_date TEXT NOT NULL,
    notes TEXT,
    FOREIGN KEY (customer_id) REFERENCES customers_new(id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES transactions_new(id) ON DELETE SET NULL
);

-- جدول الخزينة
CREATE TABLE IF NOT EXISTS cashbox_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    initial_balance REAL NOT NULL DEFAULT 0,
    current_balance REAL NOT NULL DEFAULT 0,
    profit_total REAL NOT NULL DEFAULT 0,
    sales_total REAL NOT NULL DEFAULT 0,
    purchases_total REAL NOT NULL DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- جدول معاملات الخزينة
CREATE TABLE IF NOT EXISTS cashbox_transactions_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type TEXT NOT NULL,
    amount REAL NOT NULL,
    source TEXT,
    notes TEXT,
    user_id INTEGER,
    created_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول سجل تدقيق المعاملات (لتتبع الإلغاءات والتعديلات)
CREATE TABLE IF NOT EXISTS transaction_audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_id INTEGER NOT NULL,
    action_type TEXT NOT NULL, -- 'cancel', 'modify', 'restore'
    old_status TEXT,
    new_status TEXT,
    reason TEXT,
    performed_by INTEGER NOT NULL,
    performed_at TEXT NOT NULL,
    additional_data TEXT, -- JSON data for extra information
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_inventory_new_item_id ON inventory_new(item_id);
CREATE INDEX IF NOT EXISTS idx_transactions_new_item_id ON transactions_new(item_id);
CREATE INDEX IF NOT EXISTS idx_transactions_new_transaction_type ON transactions_new(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_new_transaction_date ON transactions_new(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transactions_new_customer_id ON transactions_new(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_sales_history_new_customer_id ON customer_sales_history_new(customer_id);
CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_new_type ON cashbox_transactions_new(type);
CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_new_created_at ON cashbox_transactions_new(created_at);

-- نقل البيانات من الجداول القديمة إلى الجداول الجديدة
-- نقل بيانات الأصناف
INSERT INTO items_new (id, name, unit, created_at, updated_at)
SELECT
    id,
    name,
    COALESCE(unit, 'قطعة') AS unit,
    COALESCE(created_at, CURRENT_TIMESTAMP) AS created_at,
    COALESCE(updated_at, CURRENT_TIMESTAMP) AS updated_at
FROM items;

-- نقل بيانات المخزون
INSERT INTO inventory_new (item_id, current_quantity, minimum_quantity, avg_price, selling_price, last_updated)
SELECT
    item_id,
    COALESCE(current_quantity, 0) AS current_quantity,
    COALESCE(minimum_quantity, 0) AS minimum_quantity,
    COALESCE(avg_price, 0) AS avg_price,
    COALESCE(selling_price, 0) AS selling_price,
    COALESCE(last_updated, CURRENT_TIMESTAMP) AS last_updated
FROM inventory;

-- نقل بيانات العملاء
INSERT INTO customers_new (id, name, contact_person, phone, email, address, customer_type, parent_id, credit_limit, balance, created_at, updated_at)
SELECT
    id,
    name,
    contact_person,
    phone,
    email,
    address,
    COALESCE(customer_type, 'normal') AS customer_type,
    parent_id,
    COALESCE(credit_limit, 0) AS credit_limit,
    COALESCE(balance, 0) AS balance,
    COALESCE(created_at, CURRENT_TIMESTAMP) AS created_at,
    COALESCE(updated_at, CURRENT_TIMESTAMP) AS updated_at
FROM customers;

-- نقل بيانات المعاملات
INSERT INTO transactions_new (id, transaction_id, item_id, transaction_type, quantity, price, selling_price, total_price, profit, customer_id, invoice_number, notes, user_id, transaction_date, skip_inventory_update, status, cancelled_at, cancelled_by, cancellation_reason)
SELECT
    id,
    transaction_id,
    item_id,
    transaction_type,
    quantity,
    COALESCE(price, 0) AS price,
    COALESCE(selling_price, 0) AS selling_price,
    COALESCE(total_price, 0) AS total_price,
    COALESCE(profit, 0) AS profit,
    customer_id,
    invoice_number,
    notes,
    user_id,
    COALESCE(transaction_date, CURRENT_TIMESTAMP) AS transaction_date,
    COALESCE(skip_inventory_update, 0) AS skip_inventory_update,
    'active' AS status,
    NULL AS cancelled_at,
    NULL AS cancelled_by,
    NULL AS cancellation_reason
FROM transactions;

-- نقل بيانات سجل مبيعات العملاء
INSERT INTO customer_sales_history_new (id, customer_id, transaction_id, invoice_number, total_amount, total_profit, transaction_date, notes)
SELECT
    id,
    customer_id,
    transaction_id,
    invoice_number,
    COALESCE(total_amount, 0) AS total_amount,
    COALESCE(total_profit, 0) AS total_profit,
    COALESCE(transaction_date, CURRENT_TIMESTAMP) AS transaction_date,
    notes
FROM customer_sales_history;

-- نقل بيانات الخزينة
INSERT INTO cashbox_new (id, initial_balance, current_balance, profit_total, sales_total, purchases_total, created_at, updated_at)
SELECT
    id,
    COALESCE(initial_balance, 0) AS initial_balance,
    COALESCE(current_balance, 0) AS current_balance,
    COALESCE(profit_total, 0) AS profit_total,
    COALESCE(sales_total, 0) AS sales_total,
    COALESCE(purchases_total, 0) AS purchases_total,
    COALESCE(created_at, CURRENT_TIMESTAMP) AS created_at,
    COALESCE(updated_at, CURRENT_TIMESTAMP) AS updated_at
FROM cashbox;

-- نقل بيانات معاملات الخزينة
INSERT INTO cashbox_transactions_new (id, type, amount, source, notes, user_id, created_at)
SELECT
    id,
    type,
    amount,
    source,
    notes,
    user_id,
    COALESCE(created_at, CURRENT_TIMESTAMP) AS created_at
FROM cashbox_transactions;

-- حذف الجداول القديمة
DROP TABLE IF EXISTS cashbox_transactions;
DROP TABLE IF EXISTS cashbox;
DROP TABLE IF EXISTS customer_sales_history;
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS inventory;
DROP TABLE IF EXISTS customers;
DROP TABLE IF EXISTS items;

-- إعادة تسمية الجداول الجديدة
ALTER TABLE items_new RENAME TO items;
ALTER TABLE inventory_new RENAME TO inventory;
ALTER TABLE customers_new RENAME TO customers;
ALTER TABLE transactions_new RENAME TO transactions;
ALTER TABLE customer_sales_history_new RENAME TO customer_sales_history;
ALTER TABLE cashbox_new RENAME TO cashbox;
ALTER TABLE cashbox_transactions_new RENAME TO cashbox_transactions;

-- إعادة إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_inventory_item_id ON inventory(item_id);
CREATE INDEX IF NOT EXISTS idx_transactions_item_id ON transactions(item_id);
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_type ON transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_date ON transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transactions_customer_id ON transactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_cancelled_by ON transactions(cancelled_by);
CREATE INDEX IF NOT EXISTS idx_customer_sales_history_customer_id ON customer_sales_history(customer_id);
CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_type ON cashbox_transactions(type);
CREATE INDEX IF NOT EXISTS idx_cashbox_transactions_created_at ON cashbox_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transaction_audit_log_transaction_id ON transaction_audit_log(transaction_id);
CREATE INDEX IF NOT EXISTS idx_transaction_audit_log_performed_by ON transaction_audit_log(performed_by);
CREATE INDEX IF NOT EXISTS idx_transaction_audit_log_performed_at ON transaction_audit_log(performed_at);

-- تحديث الإحصائيات لتحسين أداء قاعدة البيانات
ANALYZE;
