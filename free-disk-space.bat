@echo off
echo.
echo ========================================
echo Cleaning Disk Space...
echo ========================================
echo.

echo Checking current disk space...
echo.
dir C:\ | findstr "bytes free"
echo.

echo Cleaning temporary files...
echo.

echo 1. Cleaning Temp folder...
del /q /s "%TEMP%\*" 2>nul
for /d %%i in ("%TEMP%\*") do rmdir /s /q "%%i" 2>nul
echo    Done cleaning Temp folder

echo.
echo 2. Cleaning Recycle Bin...
rd /s /q C:\$Recycle.Bin 2>nul
echo    Done cleaning Recycle Bin

echo.
echo 3. Cleaning Windows temp files...
del /q /s "C:\Windows\Temp\*" 2>nul
for /d %%i in ("C:\Windows\Temp\*") do rmdir /s /q "%%i" 2>nul
echo    Done cleaning Windows temp files

echo.
echo 4. Cleaning npm cache...
npm cache clean --force 2>nul
echo    Done cleaning npm cache

echo.
echo 5. Cleaning node_modules cache...
if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache" 2>nul
    echo    Done removing node_modules\.cache
)

echo.
echo Checking disk space after cleanup...
echo.
dir C:\ | findstr "bytes free"
echo.

echo Disk cleanup completed!
echo.
echo Additional steps you can do:
echo    1. Empty Recycle Bin manually
echo    2. Delete old files from Downloads folder
echo    3. Move large files to another drive
echo    4. Use Windows Disk Cleanup tool
echo.
pause
