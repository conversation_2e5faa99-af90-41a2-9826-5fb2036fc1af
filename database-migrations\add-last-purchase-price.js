#!/usr/bin/env node

/**
 * Migration Script: إضافة حقل آخر سعر شراء إلى جدول المخزون
 *
 * هذا السكريبت يقوم بـ:
 * 1. إضافة حقل last_purchase_price إلى جدول inventory
 * 2. ملء الحقل الجديد بآخر سعر شراء من جدول المعاملات
 * 3. التحقق من صحة البيانات بعد التحديث
 */

const Database = require('better-sqlite3');
const path = require('path');

// مسار قاعدة البيانات
const dbPath = path.join(__dirname, '..', 'wms.db');

console.log('🔄 بدء migration: إضافة حقل آخر سعر شراء...');
console.log('📁 مسار قاعدة البيانات:', dbPath);

async function runMigration() {
  let db;

  try {
    // فتح قاعدة البيانات
    db = new Database(dbPath);

    console.log('✅ تم فتح قاعدة البيانات بنجاح');

    // التحقق من وجود جدول المخزون
    const tableExists = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='inventory'
    `).get();

    if (!tableExists) {
      throw new Error('جدول المخزون غير موجود');
    }

    console.log('✅ تم التحقق من وجود جدول المخزون');

    // التحقق من وجود الحقل الجديد
    const columnExists = db.prepare(`
      PRAGMA table_info(inventory)
    `).all().find(col => col.name === 'last_purchase_price');

    if (columnExists) {
      console.log('⚠️ حقل last_purchase_price موجود بالفعل');
      return;
    }

    // بدء المعاملة
    db.exec('BEGIN TRANSACTION');

    console.log('🔧 إضافة حقل last_purchase_price...');

    // إضافة الحقل الجديد
    db.exec(`
      ALTER TABLE inventory
      ADD COLUMN last_purchase_price REAL DEFAULT 0
    `);

    console.log('✅ تم إضافة الحقل بنجاح');

    // ملء الحقل الجديد بآخر سعر شراء لكل صنف
    console.log('📊 ملء البيانات من آخر معاملات الشراء...');

    const updateStmt = db.prepare(`
      UPDATE inventory
      SET last_purchase_price = (
        SELECT price
        FROM transactions
        WHERE transactions.item_id = inventory.item_id
          AND transactions.transaction_type = 'purchase'
          AND (transactions.status IS NULL OR transactions.status = 'active')
        ORDER BY transactions.transaction_date DESC, transactions.id DESC
        LIMIT 1
      )
      WHERE EXISTS (
        SELECT 1
        FROM transactions
        WHERE transactions.item_id = inventory.item_id
          AND transactions.transaction_type = 'purchase'
          AND (transactions.status IS NULL OR transactions.status = 'active')
      )
    `);

    const result = updateStmt.run();
    console.log(`✅ تم تحديث ${result.changes} سجل في المخزون`);

    // التحقق من النتائج
    const verificationStmt = db.prepare(`
      SELECT
        i.id,
        i.name,
        inv.avg_price,
        inv.last_purchase_price,
        (
          SELECT price
          FROM transactions t
          WHERE t.item_id = inv.item_id
            AND t.transaction_type = 'purchase'
            AND (t.status IS NULL OR t.status = 'active')
          ORDER BY t.transaction_date DESC, t.id DESC
          LIMIT 1
        ) as latest_purchase_price_check
      FROM inventory inv
      JOIN items i ON inv.item_id = i.id
      WHERE inv.last_purchase_price > 0
      LIMIT 5
    `);

    const verificationResults = verificationStmt.all();

    console.log('\n📋 عينة من النتائج:');
    verificationResults.forEach((row, index) => {
      console.log(`${index + 1}. ${row.name}:`);
      console.log(`   متوسط السعر: ${row.avg_price}`);
      console.log(`   آخر سعر شراء: ${row.last_purchase_price}`);
      console.log(`   التحقق: ${row.latest_purchase_price_check}`);
      console.log(`   ✅ ${row.last_purchase_price === row.latest_purchase_price_check ? 'صحيح' : 'خطأ'}`);
    });

    // إنهاء المعاملة
    db.exec('COMMIT');

    console.log('\n🎉 تم إنجاز Migration بنجاح!');

    // إحصائيات نهائية
    const stats = db.prepare(`
      SELECT
        COUNT(*) as total_items,
        COUNT(CASE WHEN last_purchase_price > 0 THEN 1 END) as items_with_last_price,
        COUNT(CASE WHEN avg_price > 0 THEN 1 END) as items_with_avg_price
      FROM inventory
    `).get();

    console.log('\n📊 إحصائيات نهائية:');
    console.log(`   إجمالي الأصناف: ${stats.total_items}`);
    console.log(`   أصناف لها آخر سعر شراء: ${stats.items_with_last_price}`);
    console.log(`   أصناف لها متوسط سعر: ${stats.items_with_avg_price}`);

  } catch (error) {
    console.error('❌ خطأ في Migration:', error.message);

    if (db) {
      try {
        db.exec('ROLLBACK');
        console.log('🔄 تم التراجع عن التغييرات');
      } catch (rollbackError) {
        console.error('❌ خطأ في التراجع:', rollbackError.message);
      }
    }

    process.exit(1);
  } finally {
    if (db) {
      db.close();
      console.log('🔒 تم إغلاق قاعدة البيانات');
    }
  }
}

// تشغيل Migration
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };