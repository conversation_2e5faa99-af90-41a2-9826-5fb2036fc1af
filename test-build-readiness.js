/**
 * سكريبت اختبار جاهزية البناء
 * يتحقق من جميع المتطلبات قبل بدء عملية البناء
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 اختبار جاهزية البناء لنظام إدارة المخازن...\n');

let allTestsPassed = true;
const errors = [];
const warnings = [];

// دالة لتسجيل النتائج
function logResult(test, passed, message = '') {
  const icon = passed ? '✅' : '❌';
  const status = passed ? 'نجح' : 'فشل';
  console.log(`${icon} ${test}: ${status}`);
  if (message) {
    console.log(`   ${message}`);
  }
  if (!passed) {
    allTestsPassed = false;
    errors.push(`${test}: ${message}`);
  }
  console.log();
}

function logWarning(test, message) {
  console.log(`⚠️  ${test}: تحذير`);
  console.log(`   ${message}`);
  warnings.push(`${test}: ${message}`);
  console.log();
}

// اختبار 1: التحقق من Node.js
console.log('📋 اختبار بيئة التطوير...');
try {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  const passed = majorVersion >= 18;
  logResult('إصدار Node.js', passed, 
    passed ? `الإصدار ${nodeVersion} مدعوم` : `الإصدار ${nodeVersion} قديم، يتطلب 18 أو أحدث`);
} catch (error) {
  logResult('إصدار Node.js', false, 'فشل في التحقق من إصدار Node.js');
}

// اختبار 2: التحقق من npm
try {
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  logResult('npm متوفر', true, `الإصدار ${npmVersion}`);
} catch (error) {
  logResult('npm متوفر', false, 'npm غير مثبت أو غير متوفر في PATH');
}

// اختبار 3: التحقق من الملفات الأساسية
console.log('📁 اختبار الملفات الأساسية...');
const requiredFiles = [
  { path: 'package.json', critical: true },
  { path: 'main.js', critical: true },
  { path: 'preload.js', critical: true },
  { path: 'index.html', critical: true },
  { path: 'src/index.js', critical: true },
  { path: 'webpack.config.js', critical: true },
  { path: 'assets/icons/share_ico_socialnetwork_16174.ico', critical: false }
];

requiredFiles.forEach(file => {
  const exists = fs.existsSync(file.path);
  if (file.critical) {
    logResult(`ملف ${file.path}`, exists, exists ? 'موجود' : 'مفقود - مطلوب للبناء');
  } else if (!exists) {
    logWarning(`ملف ${file.path}`, 'مفقود - قد يؤثر على المظهر');
  } else {
    logResult(`ملف ${file.path}`, true, 'موجود');
  }
});

// اختبار 4: التحقق من package.json
console.log('📋 اختبار package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // التحقق من الحقول المطلوبة
  const requiredFields = [
    { field: 'name', value: packageJson.name },
    { field: 'version', value: packageJson.version },
    { field: 'main', value: packageJson.main },
    { field: 'build', value: packageJson.build }
  ];
  
  requiredFields.forEach(({ field, value }) => {
    const exists = value !== undefined && value !== null;
    logResult(`حقل ${field}`, exists, exists ? `القيمة: ${typeof value === 'object' ? 'كائن' : value}` : 'مفقود');
  });
  
  // التحقق من سكريبت البناء
  const hasBuildScript = packageJson.scripts && packageJson.scripts['build-installer'];
  logResult('سكريبت build-installer', hasBuildScript, 
    hasBuildScript ? 'متوفر' : 'مفقود - أضف "build-installer": "node build-installer.js"');
  
} catch (error) {
  logResult('قراءة package.json', false, `خطأ: ${error.message}`);
}

// اختبار 5: التحقق من node_modules
console.log('📦 اختبار التبعيات...');
const nodeModulesExists = fs.existsSync('node_modules');
logResult('مجلد node_modules', nodeModulesExists, 
  nodeModulesExists ? 'موجود' : 'مفقود - شغل npm install');

if (nodeModulesExists) {
  // التحقق من التبعيات المهمة
  const importantDeps = [
    'electron',
    'electron-builder',
    'webpack',
    'react',
    'better-sqlite3'
  ];
  
  importantDeps.forEach(dep => {
    const depPath = path.join('node_modules', dep);
    const exists = fs.existsSync(depPath);
    if (!exists) {
      logWarning(`تبعية ${dep}`, 'مفقودة - قد تحتاج لإعادة تثبيت التبعيات');
    } else {
      logResult(`تبعية ${dep}`, true, 'مثبتة');
    }
  });
}

// اختبار 6: التحقق من مساحة القرص
console.log('💾 اختبار مساحة القرص...');
try {
  const stats = fs.statSync('.');
  // تقدير تقريبي - نحتاج على الأقل 2 GB للبناء
  logWarning('مساحة القرص', 'تأكد من وجود مساحة كافية (2 GB على الأقل) للبناء');
} catch (error) {
  logWarning('مساحة القرص', 'فشل في التحقق من مساحة القرص');
}

// اختبار 7: التحقق من صلاحيات الكتابة
console.log('🔐 اختبار الصلاحيات...');
try {
  const testFile = 'test-write-permission.tmp';
  fs.writeFileSync(testFile, 'test');
  fs.unlinkSync(testFile);
  logResult('صلاحيات الكتابة', true, 'متوفرة');
} catch (error) {
  logResult('صلاحيات الكتابة', false, 'غير متوفرة - شغل كمدير');
}

// اختبار 8: التحقق من إعدادات البناء
console.log('⚙️  اختبار إعدادات البناء...');
const buildFiles = [
  'build/installer.nsh',
  'build/license.txt',
  'build-installer.js'
];

buildFiles.forEach(file => {
  const exists = fs.existsSync(file);
  logResult(`ملف ${file}`, exists, exists ? 'موجود' : 'مفقود - سيتم إنشاؤه تلقائياً');
});

// النتيجة النهائية
console.log('=' .repeat(60));
console.log('📊 ملخص نتائج الاختبار:');
console.log('=' .repeat(60));

if (allTestsPassed) {
  console.log('🎉 جميع الاختبارات نجحت! البناء جاهز للتشغيل.');
  console.log('\n🚀 يمكنك الآن تشغيل: npm run build-installer');
} else {
  console.log('❌ بعض الاختبارات فشلت. يجب إصلاح المشاكل التالية:');
  errors.forEach((error, index) => {
    console.log(`   ${index + 1}. ${error}`);
  });
}

if (warnings.length > 0) {
  console.log('\n⚠️  تحذيرات (لا تمنع البناء):');
  warnings.forEach((warning, index) => {
    console.log(`   ${index + 1}. ${warning}`);
  });
}

console.log('\n📋 خطوات الإصلاح المقترحة:');
if (!allTestsPassed) {
  console.log('1. شغل: npm install');
  console.log('2. تأكد من وجود جميع الملفات المطلوبة');
  console.log('3. تحقق من صحة package.json');
  console.log('4. شغل الاختبار مرة أخرى: node test-build-readiness.js');
}
console.log('5. إذا كانت جميع الاختبارات ناجحة، شغل: npm run build-installer');

process.exit(allTestsPassed ? 0 : 1);
